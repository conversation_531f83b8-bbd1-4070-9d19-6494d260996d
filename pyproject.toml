[project]
name = "rippr"
version = "0.1.0"
description = "A powerful code agent framework"
readme = "README.md"
requires-python = ">=3.12, <3.14"
license = { text = "Apache-2.0" }
authors = [
    { name = "<PERSON>", email = "<EMAIL>" }
]

dependencies = [
    "langchain>=0.3.25",
    "langchain-core>=0.3.60",
    "langchain-openai>=0.3.17",
    "langgraph>=0.4.5",
    "langgraph-prebuilt",
    "anthropic>=0.51.0",
    "openai>=1.78.1",
    "tiktoken>=0.9.0",
    "pydantic>=2.11.4",
    "langsmith>=0.3.42",
    "codegen-sdk-pink>=0.1.0",
    "codegen>=0.55.7",
    "codegen-api-client>=1.0.0",
    "langchain-google-genai>=2.1.4",
    "langchain-anthropic>=0.3.13",
    "lsprotocol>=2023.0.1",
    "litellm>=1.55.0",
    "python-dotenv>=1.0.0",
    "fastapi>=0.104.0",
    "uvicorn[standard]>=0.24.0",
    "websockets>=12.0",
    "python-multipart>=0.0.6",
    "slowapi>=0.1.9"
]

[project.scripts]
rippr = "rippr.cli:main"
rippr-api = "src.api.cli:main"

[project.optional-dependencies]
dev = [
    "pytest>=8.3.5",
    "pytest-cov>=6.1.1",
    "mypy>=1.5.0",
    "ruff>=0.1.6",
    "black>=25.1.0",
    "build>=1.0.0"
]
docs = [
    "mkdocs>=1.5.0",
    "mkdocs-material>=9.2.0",
]

[build-system]
requires = ["hatchling>=1.18.0"]
build-backend = "hatchling.build"

[tool.hatch.version]
path = "rippr/__init__.py"

[tool.hatch.build.targets.wheel]
packages = ["src", "rippr"]

[tool.ruff]
target-version = "py312"
line-length = 120
select = ["E", "F", "I", "N", "W"]
ignore = []

[tool.ruff.format]
quote-style = "double"
indent-style = "space"
skip-magic-trailing-comma = false
line-ending = "auto"

[tool.mypy]
python_version = "3.12"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = "test_*.py"
python_functions = "test_*"
python_classes = "Test*"