version: '3.8'

services:
  rippr-api:
    build:
      context: .
      dockerfile: Dockerfile.api
    ports:
      - "8000:8000"
    environment:
      - RIPPR_API_HOST=0.0.0.0
      - RIPPR_API_PORT=8000
      - RIPPR_API_WORKERS=4
      - RIPPR_DISABLE_AUTH=false
      - RIPPR_CORS_ORIGINS=*
      - RIPPR_RATE_LIMIT=60
    env_file:
      - .env
    volumes:
      # Mount your codebase directory
      - ./:/app/workspace:ro
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Optional: Add a reverse proxy for production
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      # Add SSL certificates if needed
      # - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - rippr-api
    restart: unless-stopped
    profiles:
      - production
