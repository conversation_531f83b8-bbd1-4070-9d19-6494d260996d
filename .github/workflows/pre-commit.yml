name: pre-commit

on:
  pull_request:
    branches:
      - "develop"
  push:
    branches:
      - "develop"

jobs:
  pre-commit:
    runs-on: ubuntu-latest
    timeout-minutes: 10
    env:
      REPO_SCOPED_TOKEN: ${{ secrets.REPO_SCOPED_TOKEN }}

    steps:
      - name: Check out the repo
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          token: ${{ env.REPO_SCOPED_TOKEN || github.token }}

      - name: Setup environment
        uses: ./.github/actions/setup-environment

      - name: Setup-pre-commit
        run: uv tool install pre-commit --with pre-commit-uv --force-reinstall

      - uses: actions/cache@v4
        with:
          path: ~/.cache/pre-commit
          key: pre-commit|${{ env.pythonLocation }}|${{ hashFiles('.pre-commit-config.yaml') }}
          restore-keys: pre-commit

      - run: uv run --frozen pre-commit run --show-diff-on-failure --color=always --all-files --source ${{ github.event.pull_request.base.sha || github.event.before }} --origin ${{ github.event.pull_request.head.sha || github.event.after }}
        shell: bash

      - uses: stefanzweifel/git-auto-commit-action@v5
        if: ${{ always() && env.REPO_SCOPED_TOKEN && github.event_name == 'pull_request' }}
        with:
          commit_message: "Automated pre-commit update"
          push_options: "--no-verify"
