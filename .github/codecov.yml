codecov:
  branch: develop
  max_report_age: '12h'
component_management:
  default_rules:  # default rules that will be inherited by all components
    statuses:
      - type: project # in this case every component that doens't have a status defined will have a project type one
        threshold: 0 # Shouldn't remove coverage
  individual_components:
    - component_id: codegen-sdk-python  # this is an identifier that should not be changed
      name: codegen-sdk-python  # this is a display name, and can be changed freely
      paths:
        - src/codegen/sdk/python/**
      statuses:
        - type: project # in this case every component that doens't have a status defined will have a project type one
          threshold: 0 # Shouldn't remove coverage
        - type: patch
          target: 50 # Language specific featues must be 100% covered
      flags:
        - unit-tests
    - component_id: codegen-sdk-typescript
      name: codegen-sdk-typescript
      paths:
        - src/codegen/sdk/typescript/**
      statuses:
        - type: project # in this case every component that doens't have a status defined will have a project type one
          threshold: 0 # Shouldn't remove coverage
        - type: patch
          target: 50 # Language specific featues must be 100% covered
      flags:
        - unit-tests
    - component_id: codegen-sdk-core
      name: codegen-sdk-core
      paths:
        - src/codegen/sdk/**
      flags:
        - unit-tests

flag_management:
  default_rules:
    carryforward: true
    carryforward_mode: 'labels'
    statuses:
      - type: project
  individual_flags:
    - name: unit-tests
      carryforward: true
      carryforward_mode: 'labels'
      statuses:
        - type: 'project'
        - type: 'patch'
    - name: codemod-tests
      carryforward: true
      carryforward_mode: 'labels'
    - name: integration-tests
      carryforward: true
      carryforward_mode: 'labels'
comment:
  layout: "condensed_header, condensed_files"
  hide_project_coverage: true
cli:
  plugins:
    pycoverage:
      report_type: 'json'
      include_contexts: true
  runners:
    pytest:
      coverage_root: "./"
      execute_tests_options:
        - "cov-report=xml"
        - "--verbose"
        - "-n=auto"
      python_path: ".venv/bin/python"
