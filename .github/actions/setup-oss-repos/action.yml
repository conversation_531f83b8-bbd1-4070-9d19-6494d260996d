# yaml-language-server: $schema=https://json.schemastore.org/github-action.json
name: "Setup OSS repos"
description: "Setup OSS repos"
# TODO: add size filter
runs:
  using: "composite"
  steps:
    - name: Cache oss-repos
      id: cache-oss-repos
      uses: actions/cache@v4
      with:
        path: oss_repos
        key: ${{ runner.os }}-repo-cache-2-${{hashFiles('codegen-backend/codegen_tests/graph_sitter/codemod/repos/open_source/*.json')}}
    - name: Populate oss-repos if the cache is empty
      if: steps.cache-oss-repos.outputs.cache-hit != 'true'
      shell: bash
      run: |
        uv run --frozen python -m tests.shared.codemod.commands clone-repos --clean-cache
      env:
        GITHUB_WORKSPACE: $GITHUB_WORKSPACE
    - name: Verify cache contents
      shell: bash
      run: ls -la $GITHUB_WORKSPACE/oss_repos/
