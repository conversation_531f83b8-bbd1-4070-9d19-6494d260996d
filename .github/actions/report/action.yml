name: report
description: "Publish Test Reports"
inputs:
  flag:
    required: true
    description: "Relevant codecov flag"
  codecov_token:
    required: true
    description: "Codecov token"
runs:
  using: "composite"
  steps:
      - name: Upload test results to Codecov
        if: ${{ !cancelled() }}
        uses: codecov/test-results-action@v1
        with:
          token: ${{ inputs.codecov_token }}
          files: build/test-results/test/TEST.xml
      - name: Upload coverage reports to Codecov
        if: (success() || failure()) # always upload coverage reports even if the tests fail
        continue-on-error: true
        uses: codecov/codecov-action@v5.4.0
        with:
          token: ${{ inputs.codecov_token }}
          files: coverage.xml
          flags: ${{ inputs.flag }}
          plugins: pycoverage,compress-pycoverage
