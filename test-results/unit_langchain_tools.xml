<?xml version="1.0" encoding="utf-8"?><testsuites><testsuite name="pytest" errors="38" failures="1" skipped="0" tests="40" time="4.593" timestamp="2025-05-24T13:01:05.412022-05:00" hostname="MacBook-Pro-2.local"><testcase classname="tests.langchain.unit.test_langchain_tools.TestViewFileTool" name="test_view_file_tool_initialization" time="0.001"><error message="failed on setup with &quot;file /Users/<USER>/Documents/github/rippr/tests/langchain/unit/test_langchain_tools.py, line 27&#10;      def test_view_file_tool_initialization(self, mock_codebase):&#10;file /Users/<USER>/Documents/github/rippr/tests/fixtures.py, line 120&#10;  @pytest.fixture&#10;  def mock_codebase(temp_workspace: Path, sample_files: Dict[str, str]) -&gt; Mock:&#10;E       fixture 'temp_workspace' not found&#10;&gt;       available fixtures: _session_event_loop, anyio_backend, anyio_backend_name, anyio_backend_options, cache, capfd, capfdbinary, caplog, capsys, capsysbinary, cov, doctest_namespace, event_loop, event_loop_policy, free_tcp_port, free_tcp_port_factory, free_udp_port, free_udp_port_factory, mock_codebase, mock_env_vars, monkeypatch, no_cover, pytestconfig, record_property, record_testsuite_property, record_xml_attribute, recwarn, sample_files, snapshot, temp_env_vars, tests/langchain/unit/test_langchain_tools.py::&lt;event_loop&gt;, tests/langchain/unit/test_langchain_tools.py::TestViewFileTool::&lt;event_loop&gt;, tests::&lt;event_loop&gt;, tmp_path, tmp_path_factory, tmpdir, tmpdir_factory, unused_tcp_port, unused_tcp_port_factory, unused_udp_port, unused_udp_port_factory&#10;&gt;       use 'pytest --fixtures [testpath]' for help on them.&#10;&#10;/Users/<USER>/Documents/github/rippr/tests/fixtures.py:120&quot;">file /Users/<USER>/Documents/github/rippr/tests/langchain/unit/test_langchain_tools.py, line 27
      def test_view_file_tool_initialization(self, mock_codebase):
file /Users/<USER>/Documents/github/rippr/tests/fixtures.py, line 120
  @pytest.fixture
  def mock_codebase(temp_workspace: Path, sample_files: Dict[str, str]) -&gt; Mock:
E       fixture 'temp_workspace' not found
&gt;       available fixtures: _session_event_loop, anyio_backend, anyio_backend_name, anyio_backend_options, cache, capfd, capfdbinary, caplog, capsys, capsysbinary, cov, doctest_namespace, event_loop, event_loop_policy, free_tcp_port, free_tcp_port_factory, free_udp_port, free_udp_port_factory, mock_codebase, mock_env_vars, monkeypatch, no_cover, pytestconfig, record_property, record_testsuite_property, record_xml_attribute, recwarn, sample_files, snapshot, temp_env_vars, tests/langchain/unit/test_langchain_tools.py::&lt;event_loop&gt;, tests/langchain/unit/test_langchain_tools.py::TestViewFileTool::&lt;event_loop&gt;, tests::&lt;event_loop&gt;, tmp_path, tmp_path_factory, tmpdir, tmpdir_factory, unused_tcp_port, unused_tcp_port_factory, unused_udp_port, unused_udp_port_factory
&gt;       use 'pytest --fixtures [testpath]' for help on them.

/Users/<USER>/Documents/github/rippr/tests/fixtures.py:120</error></testcase><testcase classname="tests.langchain.unit.test_langchain_tools.TestViewFileTool" name="test_view_file_tool_run_success" time="0.000"><error message="failed on setup with &quot;file /Users/<USER>/Documents/github/rippr/tests/langchain/unit/test_langchain_tools.py, line 36&#10;      def test_view_file_tool_run_success(self, mock_codebase):&#10;file /Users/<USER>/Documents/github/rippr/tests/fixtures.py, line 120&#10;  @pytest.fixture&#10;  def mock_codebase(temp_workspace: Path, sample_files: Dict[str, str]) -&gt; Mock:&#10;E       fixture 'temp_workspace' not found&#10;&gt;       available fixtures: _session_event_loop, anyio_backend, anyio_backend_name, anyio_backend_options, cache, capfd, capfdbinary, caplog, capsys, capsysbinary, cov, doctest_namespace, event_loop, event_loop_policy, free_tcp_port, free_tcp_port_factory, free_udp_port, free_udp_port_factory, mock_codebase, mock_env_vars, monkeypatch, no_cover, pytestconfig, record_property, record_testsuite_property, record_xml_attribute, recwarn, sample_files, snapshot, temp_env_vars, tests/langchain/unit/test_langchain_tools.py::&lt;event_loop&gt;, tests/langchain/unit/test_langchain_tools.py::TestViewFileTool::&lt;event_loop&gt;, tests::&lt;event_loop&gt;, tmp_path, tmp_path_factory, tmpdir, tmpdir_factory, unused_tcp_port, unused_tcp_port_factory, unused_udp_port, unused_udp_port_factory&#10;&gt;       use 'pytest --fixtures [testpath]' for help on them.&#10;&#10;/Users/<USER>/Documents/github/rippr/tests/fixtures.py:120&quot;">file /Users/<USER>/Documents/github/rippr/tests/langchain/unit/test_langchain_tools.py, line 36
      def test_view_file_tool_run_success(self, mock_codebase):
file /Users/<USER>/Documents/github/rippr/tests/fixtures.py, line 120
  @pytest.fixture
  def mock_codebase(temp_workspace: Path, sample_files: Dict[str, str]) -&gt; Mock:
E       fixture 'temp_workspace' not found
&gt;       available fixtures: _session_event_loop, anyio_backend, anyio_backend_name, anyio_backend_options, cache, capfd, capfdbinary, caplog, capsys, capsysbinary, cov, doctest_namespace, event_loop, event_loop_policy, free_tcp_port, free_tcp_port_factory, free_udp_port, free_udp_port_factory, mock_codebase, mock_env_vars, monkeypatch, no_cover, pytestconfig, record_property, record_testsuite_property, record_xml_attribute, recwarn, sample_files, snapshot, temp_env_vars, tests/langchain/unit/test_langchain_tools.py::&lt;event_loop&gt;, tests/langchain/unit/test_langchain_tools.py::TestViewFileTool::&lt;event_loop&gt;, tests::&lt;event_loop&gt;, tmp_path, tmp_path_factory, tmpdir, tmpdir_factory, unused_tcp_port, unused_tcp_port_factory, unused_udp_port, unused_udp_port_factory
&gt;       use 'pytest --fixtures [testpath]' for help on them.

/Users/<USER>/Documents/github/rippr/tests/fixtures.py:120</error></testcase><testcase classname="tests.langchain.unit.test_langchain_tools.TestViewFileTool" name="test_view_file_tool_with_pagination" time="0.000"><error message="failed on setup with &quot;file /Users/<USER>/Documents/github/rippr/tests/langchain/unit/test_langchain_tools.py, line 59&#10;      def test_view_file_tool_with_pagination(self, mock_codebase):&#10;file /Users/<USER>/Documents/github/rippr/tests/fixtures.py, line 120&#10;  @pytest.fixture&#10;  def mock_codebase(temp_workspace: Path, sample_files: Dict[str, str]) -&gt; Mock:&#10;E       fixture 'temp_workspace' not found&#10;&gt;       available fixtures: _session_event_loop, anyio_backend, anyio_backend_name, anyio_backend_options, cache, capfd, capfdbinary, caplog, capsys, capsysbinary, cov, doctest_namespace, event_loop, event_loop_policy, free_tcp_port, free_tcp_port_factory, free_udp_port, free_udp_port_factory, mock_codebase, mock_env_vars, monkeypatch, no_cover, pytestconfig, record_property, record_testsuite_property, record_xml_attribute, recwarn, sample_files, snapshot, temp_env_vars, tests/langchain/unit/test_langchain_tools.py::&lt;event_loop&gt;, tests/langchain/unit/test_langchain_tools.py::TestViewFileTool::&lt;event_loop&gt;, tests::&lt;event_loop&gt;, tmp_path, tmp_path_factory, tmpdir, tmpdir_factory, unused_tcp_port, unused_tcp_port_factory, unused_udp_port, unused_udp_port_factory&#10;&gt;       use 'pytest --fixtures [testpath]' for help on them.&#10;&#10;/Users/<USER>/Documents/github/rippr/tests/fixtures.py:120&quot;">file /Users/<USER>/Documents/github/rippr/tests/langchain/unit/test_langchain_tools.py, line 59
      def test_view_file_tool_with_pagination(self, mock_codebase):
file /Users/<USER>/Documents/github/rippr/tests/fixtures.py, line 120
  @pytest.fixture
  def mock_codebase(temp_workspace: Path, sample_files: Dict[str, str]) -&gt; Mock:
E       fixture 'temp_workspace' not found
&gt;       available fixtures: _session_event_loop, anyio_backend, anyio_backend_name, anyio_backend_options, cache, capfd, capfdbinary, caplog, capsys, capsysbinary, cov, doctest_namespace, event_loop, event_loop_policy, free_tcp_port, free_tcp_port_factory, free_udp_port, free_udp_port_factory, mock_codebase, mock_env_vars, monkeypatch, no_cover, pytestconfig, record_property, record_testsuite_property, record_xml_attribute, recwarn, sample_files, snapshot, temp_env_vars, tests/langchain/unit/test_langchain_tools.py::&lt;event_loop&gt;, tests/langchain/unit/test_langchain_tools.py::TestViewFileTool::&lt;event_loop&gt;, tests::&lt;event_loop&gt;, tmp_path, tmp_path_factory, tmpdir, tmpdir_factory, unused_tcp_port, unused_tcp_port_factory, unused_udp_port, unused_udp_port_factory
&gt;       use 'pytest --fixtures [testpath]' for help on them.

/Users/<USER>/Documents/github/rippr/tests/fixtures.py:120</error></testcase><testcase classname="tests.langchain.unit.test_langchain_tools.TestListDirectoryTool" name="test_list_directory_tool_initialization" time="0.000"><error message="failed on setup with &quot;file /Users/<USER>/Documents/github/rippr/tests/langchain/unit/test_langchain_tools.py, line 93&#10;      def test_list_directory_tool_initialization(self, mock_codebase):&#10;file /Users/<USER>/Documents/github/rippr/tests/fixtures.py, line 120&#10;  @pytest.fixture&#10;  def mock_codebase(temp_workspace: Path, sample_files: Dict[str, str]) -&gt; Mock:&#10;E       fixture 'temp_workspace' not found&#10;&gt;       available fixtures: _session_event_loop, anyio_backend, anyio_backend_name, anyio_backend_options, cache, capfd, capfdbinary, caplog, capsys, capsysbinary, cov, doctest_namespace, event_loop, event_loop_policy, free_tcp_port, free_tcp_port_factory, free_udp_port, free_udp_port_factory, mock_codebase, mock_env_vars, monkeypatch, no_cover, pytestconfig, record_property, record_testsuite_property, record_xml_attribute, recwarn, sample_files, snapshot, temp_env_vars, tests/langchain/unit/test_langchain_tools.py::&lt;event_loop&gt;, tests/langchain/unit/test_langchain_tools.py::TestListDirectoryTool::&lt;event_loop&gt;, tests::&lt;event_loop&gt;, tmp_path, tmp_path_factory, tmpdir, tmpdir_factory, unused_tcp_port, unused_tcp_port_factory, unused_udp_port, unused_udp_port_factory&#10;&gt;       use 'pytest --fixtures [testpath]' for help on them.&#10;&#10;/Users/<USER>/Documents/github/rippr/tests/fixtures.py:120&quot;">file /Users/<USER>/Documents/github/rippr/tests/langchain/unit/test_langchain_tools.py, line 93
      def test_list_directory_tool_initialization(self, mock_codebase):
file /Users/<USER>/Documents/github/rippr/tests/fixtures.py, line 120
  @pytest.fixture
  def mock_codebase(temp_workspace: Path, sample_files: Dict[str, str]) -&gt; Mock:
E       fixture 'temp_workspace' not found
&gt;       available fixtures: _session_event_loop, anyio_backend, anyio_backend_name, anyio_backend_options, cache, capfd, capfdbinary, caplog, capsys, capsysbinary, cov, doctest_namespace, event_loop, event_loop_policy, free_tcp_port, free_tcp_port_factory, free_udp_port, free_udp_port_factory, mock_codebase, mock_env_vars, monkeypatch, no_cover, pytestconfig, record_property, record_testsuite_property, record_xml_attribute, recwarn, sample_files, snapshot, temp_env_vars, tests/langchain/unit/test_langchain_tools.py::&lt;event_loop&gt;, tests/langchain/unit/test_langchain_tools.py::TestListDirectoryTool::&lt;event_loop&gt;, tests::&lt;event_loop&gt;, tmp_path, tmp_path_factory, tmpdir, tmpdir_factory, unused_tcp_port, unused_tcp_port_factory, unused_udp_port, unused_udp_port_factory
&gt;       use 'pytest --fixtures [testpath]' for help on them.

/Users/<USER>/Documents/github/rippr/tests/fixtures.py:120</error></testcase><testcase classname="tests.langchain.unit.test_langchain_tools.TestListDirectoryTool" name="test_list_directory_tool_run_success" time="0.000"><error message="failed on setup with &quot;file /Users/<USER>/Documents/github/rippr/tests/langchain/unit/test_langchain_tools.py, line 101&#10;      def test_list_directory_tool_run_success(self, mock_codebase):&#10;file /Users/<USER>/Documents/github/rippr/tests/fixtures.py, line 120&#10;  @pytest.fixture&#10;  def mock_codebase(temp_workspace: Path, sample_files: Dict[str, str]) -&gt; Mock:&#10;E       fixture 'temp_workspace' not found&#10;&gt;       available fixtures: _session_event_loop, anyio_backend, anyio_backend_name, anyio_backend_options, cache, capfd, capfdbinary, caplog, capsys, capsysbinary, cov, doctest_namespace, event_loop, event_loop_policy, free_tcp_port, free_tcp_port_factory, free_udp_port, free_udp_port_factory, mock_codebase, mock_env_vars, monkeypatch, no_cover, pytestconfig, record_property, record_testsuite_property, record_xml_attribute, recwarn, sample_files, snapshot, temp_env_vars, tests/langchain/unit/test_langchain_tools.py::&lt;event_loop&gt;, tests/langchain/unit/test_langchain_tools.py::TestListDirectoryTool::&lt;event_loop&gt;, tests::&lt;event_loop&gt;, tmp_path, tmp_path_factory, tmpdir, tmpdir_factory, unused_tcp_port, unused_tcp_port_factory, unused_udp_port, unused_udp_port_factory&#10;&gt;       use 'pytest --fixtures [testpath]' for help on them.&#10;&#10;/Users/<USER>/Documents/github/rippr/tests/fixtures.py:120&quot;">file /Users/<USER>/Documents/github/rippr/tests/langchain/unit/test_langchain_tools.py, line 101
      def test_list_directory_tool_run_success(self, mock_codebase):
file /Users/<USER>/Documents/github/rippr/tests/fixtures.py, line 120
  @pytest.fixture
  def mock_codebase(temp_workspace: Path, sample_files: Dict[str, str]) -&gt; Mock:
E       fixture 'temp_workspace' not found
&gt;       available fixtures: _session_event_loop, anyio_backend, anyio_backend_name, anyio_backend_options, cache, capfd, capfdbinary, caplog, capsys, capsysbinary, cov, doctest_namespace, event_loop, event_loop_policy, free_tcp_port, free_tcp_port_factory, free_udp_port, free_udp_port_factory, mock_codebase, mock_env_vars, monkeypatch, no_cover, pytestconfig, record_property, record_testsuite_property, record_xml_attribute, recwarn, sample_files, snapshot, temp_env_vars, tests/langchain/unit/test_langchain_tools.py::&lt;event_loop&gt;, tests/langchain/unit/test_langchain_tools.py::TestListDirectoryTool::&lt;event_loop&gt;, tests::&lt;event_loop&gt;, tmp_path, tmp_path_factory, tmpdir, tmpdir_factory, unused_tcp_port, unused_tcp_port_factory, unused_udp_port, unused_udp_port_factory
&gt;       use 'pytest --fixtures [testpath]' for help on them.

/Users/<USER>/Documents/github/rippr/tests/fixtures.py:120</error></testcase><testcase classname="tests.langchain.unit.test_langchain_tools.TestRipGrepTool" name="test_ripgrep_tool_initialization" time="0.000"><error message="failed on setup with &quot;file /Users/<USER>/Documents/github/rippr/tests/langchain/unit/test_langchain_tools.py, line 123&#10;      def test_ripgrep_tool_initialization(self, mock_codebase):&#10;file /Users/<USER>/Documents/github/rippr/tests/fixtures.py, line 120&#10;  @pytest.fixture&#10;  def mock_codebase(temp_workspace: Path, sample_files: Dict[str, str]) -&gt; Mock:&#10;E       fixture 'temp_workspace' not found&#10;&gt;       available fixtures: _session_event_loop, anyio_backend, anyio_backend_name, anyio_backend_options, cache, capfd, capfdbinary, caplog, capsys, capsysbinary, cov, doctest_namespace, event_loop, event_loop_policy, free_tcp_port, free_tcp_port_factory, free_udp_port, free_udp_port_factory, mock_codebase, mock_env_vars, monkeypatch, no_cover, pytestconfig, record_property, record_testsuite_property, record_xml_attribute, recwarn, sample_files, snapshot, temp_env_vars, tests/langchain/unit/test_langchain_tools.py::&lt;event_loop&gt;, tests/langchain/unit/test_langchain_tools.py::TestRipGrepTool::&lt;event_loop&gt;, tests::&lt;event_loop&gt;, tmp_path, tmp_path_factory, tmpdir, tmpdir_factory, unused_tcp_port, unused_tcp_port_factory, unused_udp_port, unused_udp_port_factory&#10;&gt;       use 'pytest --fixtures [testpath]' for help on them.&#10;&#10;/Users/<USER>/Documents/github/rippr/tests/fixtures.py:120&quot;">file /Users/<USER>/Documents/github/rippr/tests/langchain/unit/test_langchain_tools.py, line 123
      def test_ripgrep_tool_initialization(self, mock_codebase):
file /Users/<USER>/Documents/github/rippr/tests/fixtures.py, line 120
  @pytest.fixture
  def mock_codebase(temp_workspace: Path, sample_files: Dict[str, str]) -&gt; Mock:
E       fixture 'temp_workspace' not found
&gt;       available fixtures: _session_event_loop, anyio_backend, anyio_backend_name, anyio_backend_options, cache, capfd, capfdbinary, caplog, capsys, capsysbinary, cov, doctest_namespace, event_loop, event_loop_policy, free_tcp_port, free_tcp_port_factory, free_udp_port, free_udp_port_factory, mock_codebase, mock_env_vars, monkeypatch, no_cover, pytestconfig, record_property, record_testsuite_property, record_xml_attribute, recwarn, sample_files, snapshot, temp_env_vars, tests/langchain/unit/test_langchain_tools.py::&lt;event_loop&gt;, tests/langchain/unit/test_langchain_tools.py::TestRipGrepTool::&lt;event_loop&gt;, tests::&lt;event_loop&gt;, tmp_path, tmp_path_factory, tmpdir, tmpdir_factory, unused_tcp_port, unused_tcp_port_factory, unused_udp_port, unused_udp_port_factory
&gt;       use 'pytest --fixtures [testpath]' for help on them.

/Users/<USER>/Documents/github/rippr/tests/fixtures.py:120</error></testcase><testcase classname="tests.langchain.unit.test_langchain_tools.TestRipGrepTool" name="test_ripgrep_tool_run_success" time="0.000"><error message="failed on setup with &quot;file /Users/<USER>/Documents/github/rippr/tests/langchain/unit/test_langchain_tools.py, line 131&#10;      def test_ripgrep_tool_run_success(self, mock_codebase):&#10;file /Users/<USER>/Documents/github/rippr/tests/fixtures.py, line 120&#10;  @pytest.fixture&#10;  def mock_codebase(temp_workspace: Path, sample_files: Dict[str, str]) -&gt; Mock:&#10;E       fixture 'temp_workspace' not found&#10;&gt;       available fixtures: _session_event_loop, anyio_backend, anyio_backend_name, anyio_backend_options, cache, capfd, capfdbinary, caplog, capsys, capsysbinary, cov, doctest_namespace, event_loop, event_loop_policy, free_tcp_port, free_tcp_port_factory, free_udp_port, free_udp_port_factory, mock_codebase, mock_env_vars, monkeypatch, no_cover, pytestconfig, record_property, record_testsuite_property, record_xml_attribute, recwarn, sample_files, snapshot, temp_env_vars, tests/langchain/unit/test_langchain_tools.py::&lt;event_loop&gt;, tests/langchain/unit/test_langchain_tools.py::TestRipGrepTool::&lt;event_loop&gt;, tests::&lt;event_loop&gt;, tmp_path, tmp_path_factory, tmpdir, tmpdir_factory, unused_tcp_port, unused_tcp_port_factory, unused_udp_port, unused_udp_port_factory&#10;&gt;       use 'pytest --fixtures [testpath]' for help on them.&#10;&#10;/Users/<USER>/Documents/github/rippr/tests/fixtures.py:120&quot;">file /Users/<USER>/Documents/github/rippr/tests/langchain/unit/test_langchain_tools.py, line 131
      def test_ripgrep_tool_run_success(self, mock_codebase):
file /Users/<USER>/Documents/github/rippr/tests/fixtures.py, line 120
  @pytest.fixture
  def mock_codebase(temp_workspace: Path, sample_files: Dict[str, str]) -&gt; Mock:
E       fixture 'temp_workspace' not found
&gt;       available fixtures: _session_event_loop, anyio_backend, anyio_backend_name, anyio_backend_options, cache, capfd, capfdbinary, caplog, capsys, capsysbinary, cov, doctest_namespace, event_loop, event_loop_policy, free_tcp_port, free_tcp_port_factory, free_udp_port, free_udp_port_factory, mock_codebase, mock_env_vars, monkeypatch, no_cover, pytestconfig, record_property, record_testsuite_property, record_xml_attribute, recwarn, sample_files, snapshot, temp_env_vars, tests/langchain/unit/test_langchain_tools.py::&lt;event_loop&gt;, tests/langchain/unit/test_langchain_tools.py::TestRipGrepTool::&lt;event_loop&gt;, tests::&lt;event_loop&gt;, tmp_path, tmp_path_factory, tmpdir, tmpdir_factory, unused_tcp_port, unused_tcp_port_factory, unused_udp_port, unused_udp_port_factory
&gt;       use 'pytest --fixtures [testpath]' for help on them.

/Users/<USER>/Documents/github/rippr/tests/fixtures.py:120</error></testcase><testcase classname="tests.langchain.unit.test_langchain_tools.TestEditFileTool" name="test_edit_file_tool_initialization" time="0.000"><error message="failed on setup with &quot;file /Users/<USER>/Documents/github/rippr/tests/langchain/unit/test_langchain_tools.py, line 167&#10;      def test_edit_file_tool_initialization(self, mock_codebase):&#10;file /Users/<USER>/Documents/github/rippr/tests/fixtures.py, line 120&#10;  @pytest.fixture&#10;  def mock_codebase(temp_workspace: Path, sample_files: Dict[str, str]) -&gt; Mock:&#10;E       fixture 'temp_workspace' not found&#10;&gt;       available fixtures: _session_event_loop, anyio_backend, anyio_backend_name, anyio_backend_options, cache, capfd, capfdbinary, caplog, capsys, capsysbinary, cov, doctest_namespace, event_loop, event_loop_policy, free_tcp_port, free_tcp_port_factory, free_udp_port, free_udp_port_factory, mock_codebase, mock_env_vars, monkeypatch, no_cover, pytestconfig, record_property, record_testsuite_property, record_xml_attribute, recwarn, sample_files, snapshot, temp_env_vars, tests/langchain/unit/test_langchain_tools.py::&lt;event_loop&gt;, tests/langchain/unit/test_langchain_tools.py::TestEditFileTool::&lt;event_loop&gt;, tests::&lt;event_loop&gt;, tmp_path, tmp_path_factory, tmpdir, tmpdir_factory, unused_tcp_port, unused_tcp_port_factory, unused_udp_port, unused_udp_port_factory&#10;&gt;       use 'pytest --fixtures [testpath]' for help on them.&#10;&#10;/Users/<USER>/Documents/github/rippr/tests/fixtures.py:120&quot;">file /Users/<USER>/Documents/github/rippr/tests/langchain/unit/test_langchain_tools.py, line 167
      def test_edit_file_tool_initialization(self, mock_codebase):
file /Users/<USER>/Documents/github/rippr/tests/fixtures.py, line 120
  @pytest.fixture
  def mock_codebase(temp_workspace: Path, sample_files: Dict[str, str]) -&gt; Mock:
E       fixture 'temp_workspace' not found
&gt;       available fixtures: _session_event_loop, anyio_backend, anyio_backend_name, anyio_backend_options, cache, capfd, capfdbinary, caplog, capsys, capsysbinary, cov, doctest_namespace, event_loop, event_loop_policy, free_tcp_port, free_tcp_port_factory, free_udp_port, free_udp_port_factory, mock_codebase, mock_env_vars, monkeypatch, no_cover, pytestconfig, record_property, record_testsuite_property, record_xml_attribute, recwarn, sample_files, snapshot, temp_env_vars, tests/langchain/unit/test_langchain_tools.py::&lt;event_loop&gt;, tests/langchain/unit/test_langchain_tools.py::TestEditFileTool::&lt;event_loop&gt;, tests::&lt;event_loop&gt;, tmp_path, tmp_path_factory, tmpdir, tmpdir_factory, unused_tcp_port, unused_tcp_port_factory, unused_udp_port, unused_udp_port_factory
&gt;       use 'pytest --fixtures [testpath]' for help on them.

/Users/<USER>/Documents/github/rippr/tests/fixtures.py:120</error></testcase><testcase classname="tests.langchain.unit.test_langchain_tools.TestEditFileTool" name="test_edit_file_tool_run_success" time="0.000"><error message="failed on setup with &quot;file /Users/<USER>/Documents/github/rippr/tests/langchain/unit/test_langchain_tools.py, line 175&#10;      def test_edit_file_tool_run_success(self, mock_codebase):&#10;file /Users/<USER>/Documents/github/rippr/tests/fixtures.py, line 120&#10;  @pytest.fixture&#10;  def mock_codebase(temp_workspace: Path, sample_files: Dict[str, str]) -&gt; Mock:&#10;E       fixture 'temp_workspace' not found&#10;&gt;       available fixtures: _session_event_loop, anyio_backend, anyio_backend_name, anyio_backend_options, cache, capfd, capfdbinary, caplog, capsys, capsysbinary, cov, doctest_namespace, event_loop, event_loop_policy, free_tcp_port, free_tcp_port_factory, free_udp_port, free_udp_port_factory, mock_codebase, mock_env_vars, monkeypatch, no_cover, pytestconfig, record_property, record_testsuite_property, record_xml_attribute, recwarn, sample_files, snapshot, temp_env_vars, tests/langchain/unit/test_langchain_tools.py::&lt;event_loop&gt;, tests/langchain/unit/test_langchain_tools.py::TestEditFileTool::&lt;event_loop&gt;, tests::&lt;event_loop&gt;, tmp_path, tmp_path_factory, tmpdir, tmpdir_factory, unused_tcp_port, unused_tcp_port_factory, unused_udp_port, unused_udp_port_factory&#10;&gt;       use 'pytest --fixtures [testpath]' for help on them.&#10;&#10;/Users/<USER>/Documents/github/rippr/tests/fixtures.py:120&quot;">file /Users/<USER>/Documents/github/rippr/tests/langchain/unit/test_langchain_tools.py, line 175
      def test_edit_file_tool_run_success(self, mock_codebase):
file /Users/<USER>/Documents/github/rippr/tests/fixtures.py, line 120
  @pytest.fixture
  def mock_codebase(temp_workspace: Path, sample_files: Dict[str, str]) -&gt; Mock:
E       fixture 'temp_workspace' not found
&gt;       available fixtures: _session_event_loop, anyio_backend, anyio_backend_name, anyio_backend_options, cache, capfd, capfdbinary, caplog, capsys, capsysbinary, cov, doctest_namespace, event_loop, event_loop_policy, free_tcp_port, free_tcp_port_factory, free_udp_port, free_udp_port_factory, mock_codebase, mock_env_vars, monkeypatch, no_cover, pytestconfig, record_property, record_testsuite_property, record_xml_attribute, recwarn, sample_files, snapshot, temp_env_vars, tests/langchain/unit/test_langchain_tools.py::&lt;event_loop&gt;, tests/langchain/unit/test_langchain_tools.py::TestEditFileTool::&lt;event_loop&gt;, tests::&lt;event_loop&gt;, tmp_path, tmp_path_factory, tmpdir, tmpdir_factory, unused_tcp_port, unused_tcp_port_factory, unused_udp_port, unused_udp_port_factory
&gt;       use 'pytest --fixtures [testpath]' for help on them.

/Users/<USER>/Documents/github/rippr/tests/fixtures.py:120</error></testcase><testcase classname="tests.langchain.unit.test_langchain_tools.TestCreateFileTool" name="test_create_file_tool_initialization" time="0.000"><error message="failed on setup with &quot;file /Users/<USER>/Documents/github/rippr/tests/langchain/unit/test_langchain_tools.py, line 201&#10;      def test_create_file_tool_initialization(self, mock_codebase):&#10;file /Users/<USER>/Documents/github/rippr/tests/fixtures.py, line 120&#10;  @pytest.fixture&#10;  def mock_codebase(temp_workspace: Path, sample_files: Dict[str, str]) -&gt; Mock:&#10;E       fixture 'temp_workspace' not found&#10;&gt;       available fixtures: _session_event_loop, anyio_backend, anyio_backend_name, anyio_backend_options, cache, capfd, capfdbinary, caplog, capsys, capsysbinary, cov, doctest_namespace, event_loop, event_loop_policy, free_tcp_port, free_tcp_port_factory, free_udp_port, free_udp_port_factory, mock_codebase, mock_env_vars, monkeypatch, no_cover, pytestconfig, record_property, record_testsuite_property, record_xml_attribute, recwarn, sample_files, snapshot, temp_env_vars, tests/langchain/unit/test_langchain_tools.py::&lt;event_loop&gt;, tests/langchain/unit/test_langchain_tools.py::TestCreateFileTool::&lt;event_loop&gt;, tests::&lt;event_loop&gt;, tmp_path, tmp_path_factory, tmpdir, tmpdir_factory, unused_tcp_port, unused_tcp_port_factory, unused_udp_port, unused_udp_port_factory&#10;&gt;       use 'pytest --fixtures [testpath]' for help on them.&#10;&#10;/Users/<USER>/Documents/github/rippr/tests/fixtures.py:120&quot;">file /Users/<USER>/Documents/github/rippr/tests/langchain/unit/test_langchain_tools.py, line 201
      def test_create_file_tool_initialization(self, mock_codebase):
file /Users/<USER>/Documents/github/rippr/tests/fixtures.py, line 120
  @pytest.fixture
  def mock_codebase(temp_workspace: Path, sample_files: Dict[str, str]) -&gt; Mock:
E       fixture 'temp_workspace' not found
&gt;       available fixtures: _session_event_loop, anyio_backend, anyio_backend_name, anyio_backend_options, cache, capfd, capfdbinary, caplog, capsys, capsysbinary, cov, doctest_namespace, event_loop, event_loop_policy, free_tcp_port, free_tcp_port_factory, free_udp_port, free_udp_port_factory, mock_codebase, mock_env_vars, monkeypatch, no_cover, pytestconfig, record_property, record_testsuite_property, record_xml_attribute, recwarn, sample_files, snapshot, temp_env_vars, tests/langchain/unit/test_langchain_tools.py::&lt;event_loop&gt;, tests/langchain/unit/test_langchain_tools.py::TestCreateFileTool::&lt;event_loop&gt;, tests::&lt;event_loop&gt;, tmp_path, tmp_path_factory, tmpdir, tmpdir_factory, unused_tcp_port, unused_tcp_port_factory, unused_udp_port, unused_udp_port_factory
&gt;       use 'pytest --fixtures [testpath]' for help on them.

/Users/<USER>/Documents/github/rippr/tests/fixtures.py:120</error></testcase><testcase classname="tests.langchain.unit.test_langchain_tools.TestCreateFileTool" name="test_create_file_tool_run_success" time="0.000"><error message="failed on setup with &quot;file /Users/<USER>/Documents/github/rippr/tests/langchain/unit/test_langchain_tools.py, line 209&#10;      def test_create_file_tool_run_success(self, mock_codebase):&#10;file /Users/<USER>/Documents/github/rippr/tests/fixtures.py, line 120&#10;  @pytest.fixture&#10;  def mock_codebase(temp_workspace: Path, sample_files: Dict[str, str]) -&gt; Mock:&#10;E       fixture 'temp_workspace' not found&#10;&gt;       available fixtures: _session_event_loop, anyio_backend, anyio_backend_name, anyio_backend_options, cache, capfd, capfdbinary, caplog, capsys, capsysbinary, cov, doctest_namespace, event_loop, event_loop_policy, free_tcp_port, free_tcp_port_factory, free_udp_port, free_udp_port_factory, mock_codebase, mock_env_vars, monkeypatch, no_cover, pytestconfig, record_property, record_testsuite_property, record_xml_attribute, recwarn, sample_files, snapshot, temp_env_vars, tests/langchain/unit/test_langchain_tools.py::&lt;event_loop&gt;, tests/langchain/unit/test_langchain_tools.py::TestCreateFileTool::&lt;event_loop&gt;, tests::&lt;event_loop&gt;, tmp_path, tmp_path_factory, tmpdir, tmpdir_factory, unused_tcp_port, unused_tcp_port_factory, unused_udp_port, unused_udp_port_factory&#10;&gt;       use 'pytest --fixtures [testpath]' for help on them.&#10;&#10;/Users/<USER>/Documents/github/rippr/tests/fixtures.py:120&quot;">file /Users/<USER>/Documents/github/rippr/tests/langchain/unit/test_langchain_tools.py, line 209
      def test_create_file_tool_run_success(self, mock_codebase):
file /Users/<USER>/Documents/github/rippr/tests/fixtures.py, line 120
  @pytest.fixture
  def mock_codebase(temp_workspace: Path, sample_files: Dict[str, str]) -&gt; Mock:
E       fixture 'temp_workspace' not found
&gt;       available fixtures: _session_event_loop, anyio_backend, anyio_backend_name, anyio_backend_options, cache, capfd, capfdbinary, caplog, capsys, capsysbinary, cov, doctest_namespace, event_loop, event_loop_policy, free_tcp_port, free_tcp_port_factory, free_udp_port, free_udp_port_factory, mock_codebase, mock_env_vars, monkeypatch, no_cover, pytestconfig, record_property, record_testsuite_property, record_xml_attribute, recwarn, sample_files, snapshot, temp_env_vars, tests/langchain/unit/test_langchain_tools.py::&lt;event_loop&gt;, tests/langchain/unit/test_langchain_tools.py::TestCreateFileTool::&lt;event_loop&gt;, tests::&lt;event_loop&gt;, tmp_path, tmp_path_factory, tmpdir, tmpdir_factory, unused_tcp_port, unused_tcp_port_factory, unused_udp_port, unused_udp_port_factory
&gt;       use 'pytest --fixtures [testpath]' for help on them.

/Users/<USER>/Documents/github/rippr/tests/fixtures.py:120</error></testcase><testcase classname="tests.langchain.unit.test_langchain_tools.TestDeleteFileTool" name="test_delete_file_tool_initialization" time="0.000"><error message="failed on setup with &quot;file /Users/<USER>/Documents/github/rippr/tests/langchain/unit/test_langchain_tools.py, line 241&#10;      def test_delete_file_tool_initialization(self, mock_codebase):&#10;file /Users/<USER>/Documents/github/rippr/tests/fixtures.py, line 120&#10;  @pytest.fixture&#10;  def mock_codebase(temp_workspace: Path, sample_files: Dict[str, str]) -&gt; Mock:&#10;E       fixture 'temp_workspace' not found&#10;&gt;       available fixtures: _session_event_loop, anyio_backend, anyio_backend_name, anyio_backend_options, cache, capfd, capfdbinary, caplog, capsys, capsysbinary, cov, doctest_namespace, event_loop, event_loop_policy, free_tcp_port, free_tcp_port_factory, free_udp_port, free_udp_port_factory, mock_codebase, mock_env_vars, monkeypatch, no_cover, pytestconfig, record_property, record_testsuite_property, record_xml_attribute, recwarn, sample_files, snapshot, temp_env_vars, tests/langchain/unit/test_langchain_tools.py::&lt;event_loop&gt;, tests/langchain/unit/test_langchain_tools.py::TestDeleteFileTool::&lt;event_loop&gt;, tests::&lt;event_loop&gt;, tmp_path, tmp_path_factory, tmpdir, tmpdir_factory, unused_tcp_port, unused_tcp_port_factory, unused_udp_port, unused_udp_port_factory&#10;&gt;       use 'pytest --fixtures [testpath]' for help on them.&#10;&#10;/Users/<USER>/Documents/github/rippr/tests/fixtures.py:120&quot;">file /Users/<USER>/Documents/github/rippr/tests/langchain/unit/test_langchain_tools.py, line 241
      def test_delete_file_tool_initialization(self, mock_codebase):
file /Users/<USER>/Documents/github/rippr/tests/fixtures.py, line 120
  @pytest.fixture
  def mock_codebase(temp_workspace: Path, sample_files: Dict[str, str]) -&gt; Mock:
E       fixture 'temp_workspace' not found
&gt;       available fixtures: _session_event_loop, anyio_backend, anyio_backend_name, anyio_backend_options, cache, capfd, capfdbinary, caplog, capsys, capsysbinary, cov, doctest_namespace, event_loop, event_loop_policy, free_tcp_port, free_tcp_port_factory, free_udp_port, free_udp_port_factory, mock_codebase, mock_env_vars, monkeypatch, no_cover, pytestconfig, record_property, record_testsuite_property, record_xml_attribute, recwarn, sample_files, snapshot, temp_env_vars, tests/langchain/unit/test_langchain_tools.py::&lt;event_loop&gt;, tests/langchain/unit/test_langchain_tools.py::TestDeleteFileTool::&lt;event_loop&gt;, tests::&lt;event_loop&gt;, tmp_path, tmp_path_factory, tmpdir, tmpdir_factory, unused_tcp_port, unused_tcp_port_factory, unused_udp_port, unused_udp_port_factory
&gt;       use 'pytest --fixtures [testpath]' for help on them.

/Users/<USER>/Documents/github/rippr/tests/fixtures.py:120</error></testcase><testcase classname="tests.langchain.unit.test_langchain_tools.TestDeleteFileTool" name="test_delete_file_tool_run_success" time="0.000"><error message="failed on setup with &quot;file /Users/<USER>/Documents/github/rippr/tests/langchain/unit/test_langchain_tools.py, line 249&#10;      def test_delete_file_tool_run_success(self, mock_codebase):&#10;file /Users/<USER>/Documents/github/rippr/tests/fixtures.py, line 120&#10;  @pytest.fixture&#10;  def mock_codebase(temp_workspace: Path, sample_files: Dict[str, str]) -&gt; Mock:&#10;E       fixture 'temp_workspace' not found&#10;&gt;       available fixtures: _session_event_loop, anyio_backend, anyio_backend_name, anyio_backend_options, cache, capfd, capfdbinary, caplog, capsys, capsysbinary, cov, doctest_namespace, event_loop, event_loop_policy, free_tcp_port, free_tcp_port_factory, free_udp_port, free_udp_port_factory, mock_codebase, mock_env_vars, monkeypatch, no_cover, pytestconfig, record_property, record_testsuite_property, record_xml_attribute, recwarn, sample_files, snapshot, temp_env_vars, tests/langchain/unit/test_langchain_tools.py::&lt;event_loop&gt;, tests/langchain/unit/test_langchain_tools.py::TestDeleteFileTool::&lt;event_loop&gt;, tests::&lt;event_loop&gt;, tmp_path, tmp_path_factory, tmpdir, tmpdir_factory, unused_tcp_port, unused_tcp_port_factory, unused_udp_port, unused_udp_port_factory&#10;&gt;       use 'pytest --fixtures [testpath]' for help on them.&#10;&#10;/Users/<USER>/Documents/github/rippr/tests/fixtures.py:120&quot;">file /Users/<USER>/Documents/github/rippr/tests/langchain/unit/test_langchain_tools.py, line 249
      def test_delete_file_tool_run_success(self, mock_codebase):
file /Users/<USER>/Documents/github/rippr/tests/fixtures.py, line 120
  @pytest.fixture
  def mock_codebase(temp_workspace: Path, sample_files: Dict[str, str]) -&gt; Mock:
E       fixture 'temp_workspace' not found
&gt;       available fixtures: _session_event_loop, anyio_backend, anyio_backend_name, anyio_backend_options, cache, capfd, capfdbinary, caplog, capsys, capsysbinary, cov, doctest_namespace, event_loop, event_loop_policy, free_tcp_port, free_tcp_port_factory, free_udp_port, free_udp_port_factory, mock_codebase, mock_env_vars, monkeypatch, no_cover, pytestconfig, record_property, record_testsuite_property, record_xml_attribute, recwarn, sample_files, snapshot, temp_env_vars, tests/langchain/unit/test_langchain_tools.py::&lt;event_loop&gt;, tests/langchain/unit/test_langchain_tools.py::TestDeleteFileTool::&lt;event_loop&gt;, tests::&lt;event_loop&gt;, tmp_path, tmp_path_factory, tmpdir, tmpdir_factory, unused_tcp_port, unused_tcp_port_factory, unused_udp_port, unused_udp_port_factory
&gt;       use 'pytest --fixtures [testpath]' for help on them.

/Users/<USER>/Documents/github/rippr/tests/fixtures.py:120</error></testcase><testcase classname="tests.langchain.unit.test_langchain_tools.TestCommitTool" name="test_commit_tool_initialization" time="0.000"><error message="failed on setup with &quot;file /Users/<USER>/Documents/github/rippr/tests/langchain/unit/test_langchain_tools.py, line 271&#10;      def test_commit_tool_initialization(self, mock_codebase):&#10;file /Users/<USER>/Documents/github/rippr/tests/fixtures.py, line 120&#10;  @pytest.fixture&#10;  def mock_codebase(temp_workspace: Path, sample_files: Dict[str, str]) -&gt; Mock:&#10;E       fixture 'temp_workspace' not found&#10;&gt;       available fixtures: _session_event_loop, anyio_backend, anyio_backend_name, anyio_backend_options, cache, capfd, capfdbinary, caplog, capsys, capsysbinary, cov, doctest_namespace, event_loop, event_loop_policy, free_tcp_port, free_tcp_port_factory, free_udp_port, free_udp_port_factory, mock_codebase, mock_env_vars, monkeypatch, no_cover, pytestconfig, record_property, record_testsuite_property, record_xml_attribute, recwarn, sample_files, snapshot, temp_env_vars, tests/langchain/unit/test_langchain_tools.py::&lt;event_loop&gt;, tests/langchain/unit/test_langchain_tools.py::TestCommitTool::&lt;event_loop&gt;, tests::&lt;event_loop&gt;, tmp_path, tmp_path_factory, tmpdir, tmpdir_factory, unused_tcp_port, unused_tcp_port_factory, unused_udp_port, unused_udp_port_factory&#10;&gt;       use 'pytest --fixtures [testpath]' for help on them.&#10;&#10;/Users/<USER>/Documents/github/rippr/tests/fixtures.py:120&quot;">file /Users/<USER>/Documents/github/rippr/tests/langchain/unit/test_langchain_tools.py, line 271
      def test_commit_tool_initialization(self, mock_codebase):
file /Users/<USER>/Documents/github/rippr/tests/fixtures.py, line 120
  @pytest.fixture
  def mock_codebase(temp_workspace: Path, sample_files: Dict[str, str]) -&gt; Mock:
E       fixture 'temp_workspace' not found
&gt;       available fixtures: _session_event_loop, anyio_backend, anyio_backend_name, anyio_backend_options, cache, capfd, capfdbinary, caplog, capsys, capsysbinary, cov, doctest_namespace, event_loop, event_loop_policy, free_tcp_port, free_tcp_port_factory, free_udp_port, free_udp_port_factory, mock_codebase, mock_env_vars, monkeypatch, no_cover, pytestconfig, record_property, record_testsuite_property, record_xml_attribute, recwarn, sample_files, snapshot, temp_env_vars, tests/langchain/unit/test_langchain_tools.py::&lt;event_loop&gt;, tests/langchain/unit/test_langchain_tools.py::TestCommitTool::&lt;event_loop&gt;, tests::&lt;event_loop&gt;, tmp_path, tmp_path_factory, tmpdir, tmpdir_factory, unused_tcp_port, unused_tcp_port_factory, unused_udp_port, unused_udp_port_factory
&gt;       use 'pytest --fixtures [testpath]' for help on them.

/Users/<USER>/Documents/github/rippr/tests/fixtures.py:120</error></testcase><testcase classname="tests.langchain.unit.test_langchain_tools.TestCommitTool" name="test_commit_tool_run_success" time="0.000"><error message="failed on setup with &quot;file /Users/<USER>/Documents/github/rippr/tests/langchain/unit/test_langchain_tools.py, line 279&#10;      def test_commit_tool_run_success(self, mock_codebase):&#10;file /Users/<USER>/Documents/github/rippr/tests/fixtures.py, line 120&#10;  @pytest.fixture&#10;  def mock_codebase(temp_workspace: Path, sample_files: Dict[str, str]) -&gt; Mock:&#10;E       fixture 'temp_workspace' not found&#10;&gt;       available fixtures: _session_event_loop, anyio_backend, anyio_backend_name, anyio_backend_options, cache, capfd, capfdbinary, caplog, capsys, capsysbinary, cov, doctest_namespace, event_loop, event_loop_policy, free_tcp_port, free_tcp_port_factory, free_udp_port, free_udp_port_factory, mock_codebase, mock_env_vars, monkeypatch, no_cover, pytestconfig, record_property, record_testsuite_property, record_xml_attribute, recwarn, sample_files, snapshot, temp_env_vars, tests/langchain/unit/test_langchain_tools.py::&lt;event_loop&gt;, tests/langchain/unit/test_langchain_tools.py::TestCommitTool::&lt;event_loop&gt;, tests::&lt;event_loop&gt;, tmp_path, tmp_path_factory, tmpdir, tmpdir_factory, unused_tcp_port, unused_tcp_port_factory, unused_udp_port, unused_udp_port_factory&#10;&gt;       use 'pytest --fixtures [testpath]' for help on them.&#10;&#10;/Users/<USER>/Documents/github/rippr/tests/fixtures.py:120&quot;">file /Users/<USER>/Documents/github/rippr/tests/langchain/unit/test_langchain_tools.py, line 279
      def test_commit_tool_run_success(self, mock_codebase):
file /Users/<USER>/Documents/github/rippr/tests/fixtures.py, line 120
  @pytest.fixture
  def mock_codebase(temp_workspace: Path, sample_files: Dict[str, str]) -&gt; Mock:
E       fixture 'temp_workspace' not found
&gt;       available fixtures: _session_event_loop, anyio_backend, anyio_backend_name, anyio_backend_options, cache, capfd, capfdbinary, caplog, capsys, capsysbinary, cov, doctest_namespace, event_loop, event_loop_policy, free_tcp_port, free_tcp_port_factory, free_udp_port, free_udp_port_factory, mock_codebase, mock_env_vars, monkeypatch, no_cover, pytestconfig, record_property, record_testsuite_property, record_xml_attribute, recwarn, sample_files, snapshot, temp_env_vars, tests/langchain/unit/test_langchain_tools.py::&lt;event_loop&gt;, tests/langchain/unit/test_langchain_tools.py::TestCommitTool::&lt;event_loop&gt;, tests::&lt;event_loop&gt;, tmp_path, tmp_path_factory, tmpdir, tmpdir_factory, unused_tcp_port, unused_tcp_port_factory, unused_udp_port, unused_udp_port_factory
&gt;       use 'pytest --fixtures [testpath]' for help on them.

/Users/<USER>/Documents/github/rippr/tests/fixtures.py:120</error></testcase><testcase classname="tests.langchain.unit.test_langchain_tools.TestRevealSymbolTool" name="test_reveal_symbol_tool_initialization" time="0.000"><error message="failed on setup with &quot;file /Users/<USER>/Documents/github/rippr/tests/langchain/unit/test_langchain_tools.py, line 301&#10;      def test_reveal_symbol_tool_initialization(self, mock_codebase):&#10;file /Users/<USER>/Documents/github/rippr/tests/fixtures.py, line 120&#10;  @pytest.fixture&#10;  def mock_codebase(temp_workspace: Path, sample_files: Dict[str, str]) -&gt; Mock:&#10;E       fixture 'temp_workspace' not found&#10;&gt;       available fixtures: _session_event_loop, anyio_backend, anyio_backend_name, anyio_backend_options, cache, capfd, capfdbinary, caplog, capsys, capsysbinary, cov, doctest_namespace, event_loop, event_loop_policy, free_tcp_port, free_tcp_port_factory, free_udp_port, free_udp_port_factory, mock_codebase, mock_env_vars, monkeypatch, no_cover, pytestconfig, record_property, record_testsuite_property, record_xml_attribute, recwarn, sample_files, snapshot, temp_env_vars, tests/langchain/unit/test_langchain_tools.py::&lt;event_loop&gt;, tests/langchain/unit/test_langchain_tools.py::TestRevealSymbolTool::&lt;event_loop&gt;, tests::&lt;event_loop&gt;, tmp_path, tmp_path_factory, tmpdir, tmpdir_factory, unused_tcp_port, unused_tcp_port_factory, unused_udp_port, unused_udp_port_factory&#10;&gt;       use 'pytest --fixtures [testpath]' for help on them.&#10;&#10;/Users/<USER>/Documents/github/rippr/tests/fixtures.py:120&quot;">file /Users/<USER>/Documents/github/rippr/tests/langchain/unit/test_langchain_tools.py, line 301
      def test_reveal_symbol_tool_initialization(self, mock_codebase):
file /Users/<USER>/Documents/github/rippr/tests/fixtures.py, line 120
  @pytest.fixture
  def mock_codebase(temp_workspace: Path, sample_files: Dict[str, str]) -&gt; Mock:
E       fixture 'temp_workspace' not found
&gt;       available fixtures: _session_event_loop, anyio_backend, anyio_backend_name, anyio_backend_options, cache, capfd, capfdbinary, caplog, capsys, capsysbinary, cov, doctest_namespace, event_loop, event_loop_policy, free_tcp_port, free_tcp_port_factory, free_udp_port, free_udp_port_factory, mock_codebase, mock_env_vars, monkeypatch, no_cover, pytestconfig, record_property, record_testsuite_property, record_xml_attribute, recwarn, sample_files, snapshot, temp_env_vars, tests/langchain/unit/test_langchain_tools.py::&lt;event_loop&gt;, tests/langchain/unit/test_langchain_tools.py::TestRevealSymbolTool::&lt;event_loop&gt;, tests::&lt;event_loop&gt;, tmp_path, tmp_path_factory, tmpdir, tmpdir_factory, unused_tcp_port, unused_tcp_port_factory, unused_udp_port, unused_udp_port_factory
&gt;       use 'pytest --fixtures [testpath]' for help on them.

/Users/<USER>/Documents/github/rippr/tests/fixtures.py:120</error></testcase><testcase classname="tests.langchain.unit.test_langchain_tools.TestRevealSymbolTool" name="test_reveal_symbol_tool_run_success" time="0.000"><error message="failed on setup with &quot;file /Users/<USER>/Documents/github/rippr/tests/langchain/unit/test_langchain_tools.py, line 309&#10;      def test_reveal_symbol_tool_run_success(self, mock_codebase):&#10;file /Users/<USER>/Documents/github/rippr/tests/fixtures.py, line 120&#10;  @pytest.fixture&#10;  def mock_codebase(temp_workspace: Path, sample_files: Dict[str, str]) -&gt; Mock:&#10;E       fixture 'temp_workspace' not found&#10;&gt;       available fixtures: _session_event_loop, anyio_backend, anyio_backend_name, anyio_backend_options, cache, capfd, capfdbinary, caplog, capsys, capsysbinary, cov, doctest_namespace, event_loop, event_loop_policy, free_tcp_port, free_tcp_port_factory, free_udp_port, free_udp_port_factory, mock_codebase, mock_env_vars, monkeypatch, no_cover, pytestconfig, record_property, record_testsuite_property, record_xml_attribute, recwarn, sample_files, snapshot, temp_env_vars, tests/langchain/unit/test_langchain_tools.py::&lt;event_loop&gt;, tests/langchain/unit/test_langchain_tools.py::TestRevealSymbolTool::&lt;event_loop&gt;, tests::&lt;event_loop&gt;, tmp_path, tmp_path_factory, tmpdir, tmpdir_factory, unused_tcp_port, unused_tcp_port_factory, unused_udp_port, unused_udp_port_factory&#10;&gt;       use 'pytest --fixtures [testpath]' for help on them.&#10;&#10;/Users/<USER>/Documents/github/rippr/tests/fixtures.py:120&quot;">file /Users/<USER>/Documents/github/rippr/tests/langchain/unit/test_langchain_tools.py, line 309
      def test_reveal_symbol_tool_run_success(self, mock_codebase):
file /Users/<USER>/Documents/github/rippr/tests/fixtures.py, line 120
  @pytest.fixture
  def mock_codebase(temp_workspace: Path, sample_files: Dict[str, str]) -&gt; Mock:
E       fixture 'temp_workspace' not found
&gt;       available fixtures: _session_event_loop, anyio_backend, anyio_backend_name, anyio_backend_options, cache, capfd, capfdbinary, caplog, capsys, capsysbinary, cov, doctest_namespace, event_loop, event_loop_policy, free_tcp_port, free_tcp_port_factory, free_udp_port, free_udp_port_factory, mock_codebase, mock_env_vars, monkeypatch, no_cover, pytestconfig, record_property, record_testsuite_property, record_xml_attribute, recwarn, sample_files, snapshot, temp_env_vars, tests/langchain/unit/test_langchain_tools.py::&lt;event_loop&gt;, tests/langchain/unit/test_langchain_tools.py::TestRevealSymbolTool::&lt;event_loop&gt;, tests::&lt;event_loop&gt;, tmp_path, tmp_path_factory, tmpdir, tmpdir_factory, unused_tcp_port, unused_tcp_port_factory, unused_udp_port, unused_udp_port_factory
&gt;       use 'pytest --fixtures [testpath]' for help on them.

/Users/<USER>/Documents/github/rippr/tests/fixtures.py:120</error></testcase><testcase classname="tests.langchain.unit.test_langchain_tools.TestSemanticEditTool" name="test_semantic_edit_tool_initialization" time="0.000"><error message="failed on setup with &quot;file /Users/<USER>/Documents/github/rippr/tests/langchain/unit/test_langchain_tools.py, line 341&#10;      def test_semantic_edit_tool_initialization(self, mock_codebase):&#10;file /Users/<USER>/Documents/github/rippr/tests/fixtures.py, line 120&#10;  @pytest.fixture&#10;  def mock_codebase(temp_workspace: Path, sample_files: Dict[str, str]) -&gt; Mock:&#10;E       fixture 'temp_workspace' not found&#10;&gt;       available fixtures: _session_event_loop, anyio_backend, anyio_backend_name, anyio_backend_options, cache, capfd, capfdbinary, caplog, capsys, capsysbinary, cov, doctest_namespace, event_loop, event_loop_policy, free_tcp_port, free_tcp_port_factory, free_udp_port, free_udp_port_factory, mock_codebase, mock_env_vars, monkeypatch, no_cover, pytestconfig, record_property, record_testsuite_property, record_xml_attribute, recwarn, sample_files, snapshot, temp_env_vars, tests/langchain/unit/test_langchain_tools.py::&lt;event_loop&gt;, tests/langchain/unit/test_langchain_tools.py::TestSemanticEditTool::&lt;event_loop&gt;, tests::&lt;event_loop&gt;, tmp_path, tmp_path_factory, tmpdir, tmpdir_factory, unused_tcp_port, unused_tcp_port_factory, unused_udp_port, unused_udp_port_factory&#10;&gt;       use 'pytest --fixtures [testpath]' for help on them.&#10;&#10;/Users/<USER>/Documents/github/rippr/tests/fixtures.py:120&quot;">file /Users/<USER>/Documents/github/rippr/tests/langchain/unit/test_langchain_tools.py, line 341
      def test_semantic_edit_tool_initialization(self, mock_codebase):
file /Users/<USER>/Documents/github/rippr/tests/fixtures.py, line 120
  @pytest.fixture
  def mock_codebase(temp_workspace: Path, sample_files: Dict[str, str]) -&gt; Mock:
E       fixture 'temp_workspace' not found
&gt;       available fixtures: _session_event_loop, anyio_backend, anyio_backend_name, anyio_backend_options, cache, capfd, capfdbinary, caplog, capsys, capsysbinary, cov, doctest_namespace, event_loop, event_loop_policy, free_tcp_port, free_tcp_port_factory, free_udp_port, free_udp_port_factory, mock_codebase, mock_env_vars, monkeypatch, no_cover, pytestconfig, record_property, record_testsuite_property, record_xml_attribute, recwarn, sample_files, snapshot, temp_env_vars, tests/langchain/unit/test_langchain_tools.py::&lt;event_loop&gt;, tests/langchain/unit/test_langchain_tools.py::TestSemanticEditTool::&lt;event_loop&gt;, tests::&lt;event_loop&gt;, tmp_path, tmp_path_factory, tmpdir, tmpdir_factory, unused_tcp_port, unused_tcp_port_factory, unused_udp_port, unused_udp_port_factory
&gt;       use 'pytest --fixtures [testpath]' for help on them.

/Users/<USER>/Documents/github/rippr/tests/fixtures.py:120</error></testcase><testcase classname="tests.langchain.unit.test_langchain_tools.TestSemanticEditTool" name="test_semantic_edit_tool_run_success" time="0.000"><error message="failed on setup with &quot;file /Users/<USER>/Documents/github/rippr/tests/langchain/unit/test_langchain_tools.py, line 349&#10;      def test_semantic_edit_tool_run_success(self, mock_codebase):&#10;file /Users/<USER>/Documents/github/rippr/tests/fixtures.py, line 120&#10;  @pytest.fixture&#10;  def mock_codebase(temp_workspace: Path, sample_files: Dict[str, str]) -&gt; Mock:&#10;E       fixture 'temp_workspace' not found&#10;&gt;       available fixtures: _session_event_loop, anyio_backend, anyio_backend_name, anyio_backend_options, cache, capfd, capfdbinary, caplog, capsys, capsysbinary, cov, doctest_namespace, event_loop, event_loop_policy, free_tcp_port, free_tcp_port_factory, free_udp_port, free_udp_port_factory, mock_codebase, mock_env_vars, monkeypatch, no_cover, pytestconfig, record_property, record_testsuite_property, record_xml_attribute, recwarn, sample_files, snapshot, temp_env_vars, tests/langchain/unit/test_langchain_tools.py::&lt;event_loop&gt;, tests/langchain/unit/test_langchain_tools.py::TestSemanticEditTool::&lt;event_loop&gt;, tests::&lt;event_loop&gt;, tmp_path, tmp_path_factory, tmpdir, tmpdir_factory, unused_tcp_port, unused_tcp_port_factory, unused_udp_port, unused_udp_port_factory&#10;&gt;       use 'pytest --fixtures [testpath]' for help on them.&#10;&#10;/Users/<USER>/Documents/github/rippr/tests/fixtures.py:120&quot;">file /Users/<USER>/Documents/github/rippr/tests/langchain/unit/test_langchain_tools.py, line 349
      def test_semantic_edit_tool_run_success(self, mock_codebase):
file /Users/<USER>/Documents/github/rippr/tests/fixtures.py, line 120
  @pytest.fixture
  def mock_codebase(temp_workspace: Path, sample_files: Dict[str, str]) -&gt; Mock:
E       fixture 'temp_workspace' not found
&gt;       available fixtures: _session_event_loop, anyio_backend, anyio_backend_name, anyio_backend_options, cache, capfd, capfdbinary, caplog, capsys, capsysbinary, cov, doctest_namespace, event_loop, event_loop_policy, free_tcp_port, free_tcp_port_factory, free_udp_port, free_udp_port_factory, mock_codebase, mock_env_vars, monkeypatch, no_cover, pytestconfig, record_property, record_testsuite_property, record_xml_attribute, recwarn, sample_files, snapshot, temp_env_vars, tests/langchain/unit/test_langchain_tools.py::&lt;event_loop&gt;, tests/langchain/unit/test_langchain_tools.py::TestSemanticEditTool::&lt;event_loop&gt;, tests::&lt;event_loop&gt;, tmp_path, tmp_path_factory, tmpdir, tmpdir_factory, unused_tcp_port, unused_tcp_port_factory, unused_udp_port, unused_udp_port_factory
&gt;       use 'pytest --fixtures [testpath]' for help on them.

/Users/<USER>/Documents/github/rippr/tests/fixtures.py:120</error></testcase><testcase classname="tests.langchain.unit.test_langchain_tools.TestGitHubTools" name="test_github_create_pr_tool_initialization" time="0.000"><error message="failed on setup with &quot;file /Users/<USER>/Documents/github/rippr/tests/langchain/unit/test_langchain_tools.py, line 379&#10;      def test_github_create_pr_tool_initialization(self, mock_codebase):&#10;file /Users/<USER>/Documents/github/rippr/tests/fixtures.py, line 120&#10;  @pytest.fixture&#10;  def mock_codebase(temp_workspace: Path, sample_files: Dict[str, str]) -&gt; Mock:&#10;E       fixture 'temp_workspace' not found&#10;&gt;       available fixtures: _session_event_loop, anyio_backend, anyio_backend_name, anyio_backend_options, cache, capfd, capfdbinary, caplog, capsys, capsysbinary, cov, doctest_namespace, event_loop, event_loop_policy, free_tcp_port, free_tcp_port_factory, free_udp_port, free_udp_port_factory, mock_codebase, mock_env_vars, monkeypatch, no_cover, pytestconfig, record_property, record_testsuite_property, record_xml_attribute, recwarn, sample_files, snapshot, temp_env_vars, tests/langchain/unit/test_langchain_tools.py::&lt;event_loop&gt;, tests/langchain/unit/test_langchain_tools.py::TestGitHubTools::&lt;event_loop&gt;, tests::&lt;event_loop&gt;, tmp_path, tmp_path_factory, tmpdir, tmpdir_factory, unused_tcp_port, unused_tcp_port_factory, unused_udp_port, unused_udp_port_factory&#10;&gt;       use 'pytest --fixtures [testpath]' for help on them.&#10;&#10;/Users/<USER>/Documents/github/rippr/tests/fixtures.py:120&quot;">file /Users/<USER>/Documents/github/rippr/tests/langchain/unit/test_langchain_tools.py, line 379
      def test_github_create_pr_tool_initialization(self, mock_codebase):
file /Users/<USER>/Documents/github/rippr/tests/fixtures.py, line 120
  @pytest.fixture
  def mock_codebase(temp_workspace: Path, sample_files: Dict[str, str]) -&gt; Mock:
E       fixture 'temp_workspace' not found
&gt;       available fixtures: _session_event_loop, anyio_backend, anyio_backend_name, anyio_backend_options, cache, capfd, capfdbinary, caplog, capsys, capsysbinary, cov, doctest_namespace, event_loop, event_loop_policy, free_tcp_port, free_tcp_port_factory, free_udp_port, free_udp_port_factory, mock_codebase, mock_env_vars, monkeypatch, no_cover, pytestconfig, record_property, record_testsuite_property, record_xml_attribute, recwarn, sample_files, snapshot, temp_env_vars, tests/langchain/unit/test_langchain_tools.py::&lt;event_loop&gt;, tests/langchain/unit/test_langchain_tools.py::TestGitHubTools::&lt;event_loop&gt;, tests::&lt;event_loop&gt;, tmp_path, tmp_path_factory, tmpdir, tmpdir_factory, unused_tcp_port, unused_tcp_port_factory, unused_udp_port, unused_udp_port_factory
&gt;       use 'pytest --fixtures [testpath]' for help on them.

/Users/<USER>/Documents/github/rippr/tests/fixtures.py:120</error></testcase><testcase classname="tests.langchain.unit.test_langchain_tools.TestGitHubTools" name="test_github_create_pr_tool_run_success" time="0.000"><error message="failed on setup with &quot;file /Users/<USER>/Documents/github/rippr/tests/langchain/unit/test_langchain_tools.py, line 387&#10;      def test_github_create_pr_tool_run_success(self, mock_codebase, mock_env_vars):&#10;file /Users/<USER>/Documents/github/rippr/tests/fixtures.py, line 120&#10;  @pytest.fixture&#10;  def mock_codebase(temp_workspace: Path, sample_files: Dict[str, str]) -&gt; Mock:&#10;E       fixture 'temp_workspace' not found&#10;&gt;       available fixtures: _session_event_loop, anyio_backend, anyio_backend_name, anyio_backend_options, cache, capfd, capfdbinary, caplog, capsys, capsysbinary, cov, doctest_namespace, event_loop, event_loop_policy, free_tcp_port, free_tcp_port_factory, free_udp_port, free_udp_port_factory, mock_codebase, mock_env_vars, monkeypatch, no_cover, pytestconfig, record_property, record_testsuite_property, record_xml_attribute, recwarn, sample_files, snapshot, temp_env_vars, tests/langchain/unit/test_langchain_tools.py::&lt;event_loop&gt;, tests/langchain/unit/test_langchain_tools.py::TestGitHubTools::&lt;event_loop&gt;, tests::&lt;event_loop&gt;, tmp_path, tmp_path_factory, tmpdir, tmpdir_factory, unused_tcp_port, unused_tcp_port_factory, unused_udp_port, unused_udp_port_factory&#10;&gt;       use 'pytest --fixtures [testpath]' for help on them.&#10;&#10;/Users/<USER>/Documents/github/rippr/tests/fixtures.py:120&quot;">file /Users/<USER>/Documents/github/rippr/tests/langchain/unit/test_langchain_tools.py, line 387
      def test_github_create_pr_tool_run_success(self, mock_codebase, mock_env_vars):
file /Users/<USER>/Documents/github/rippr/tests/fixtures.py, line 120
  @pytest.fixture
  def mock_codebase(temp_workspace: Path, sample_files: Dict[str, str]) -&gt; Mock:
E       fixture 'temp_workspace' not found
&gt;       available fixtures: _session_event_loop, anyio_backend, anyio_backend_name, anyio_backend_options, cache, capfd, capfdbinary, caplog, capsys, capsysbinary, cov, doctest_namespace, event_loop, event_loop_policy, free_tcp_port, free_tcp_port_factory, free_udp_port, free_udp_port_factory, mock_codebase, mock_env_vars, monkeypatch, no_cover, pytestconfig, record_property, record_testsuite_property, record_xml_attribute, recwarn, sample_files, snapshot, temp_env_vars, tests/langchain/unit/test_langchain_tools.py::&lt;event_loop&gt;, tests/langchain/unit/test_langchain_tools.py::TestGitHubTools::&lt;event_loop&gt;, tests::&lt;event_loop&gt;, tmp_path, tmp_path_factory, tmpdir, tmpdir_factory, unused_tcp_port, unused_tcp_port_factory, unused_udp_port, unused_udp_port_factory
&gt;       use 'pytest --fixtures [testpath]' for help on them.

/Users/<USER>/Documents/github/rippr/tests/fixtures.py:120</error></testcase><testcase classname="tests.langchain.unit.test_langchain_tools.TestGitHubTools" name="test_github_view_pr_tool_initialization" time="0.000"><error message="failed on setup with &quot;file /Users/<USER>/Documents/github/rippr/tests/langchain/unit/test_langchain_tools.py, line 417&#10;      def test_github_view_pr_tool_initialization(self, mock_codebase):&#10;file /Users/<USER>/Documents/github/rippr/tests/fixtures.py, line 120&#10;  @pytest.fixture&#10;  def mock_codebase(temp_workspace: Path, sample_files: Dict[str, str]) -&gt; Mock:&#10;E       fixture 'temp_workspace' not found&#10;&gt;       available fixtures: _session_event_loop, anyio_backend, anyio_backend_name, anyio_backend_options, cache, capfd, capfdbinary, caplog, capsys, capsysbinary, cov, doctest_namespace, event_loop, event_loop_policy, free_tcp_port, free_tcp_port_factory, free_udp_port, free_udp_port_factory, mock_codebase, mock_env_vars, monkeypatch, no_cover, pytestconfig, record_property, record_testsuite_property, record_xml_attribute, recwarn, sample_files, snapshot, temp_env_vars, tests/langchain/unit/test_langchain_tools.py::&lt;event_loop&gt;, tests/langchain/unit/test_langchain_tools.py::TestGitHubTools::&lt;event_loop&gt;, tests::&lt;event_loop&gt;, tmp_path, tmp_path_factory, tmpdir, tmpdir_factory, unused_tcp_port, unused_tcp_port_factory, unused_udp_port, unused_udp_port_factory&#10;&gt;       use 'pytest --fixtures [testpath]' for help on them.&#10;&#10;/Users/<USER>/Documents/github/rippr/tests/fixtures.py:120&quot;">file /Users/<USER>/Documents/github/rippr/tests/langchain/unit/test_langchain_tools.py, line 417
      def test_github_view_pr_tool_initialization(self, mock_codebase):
file /Users/<USER>/Documents/github/rippr/tests/fixtures.py, line 120
  @pytest.fixture
  def mock_codebase(temp_workspace: Path, sample_files: Dict[str, str]) -&gt; Mock:
E       fixture 'temp_workspace' not found
&gt;       available fixtures: _session_event_loop, anyio_backend, anyio_backend_name, anyio_backend_options, cache, capfd, capfdbinary, caplog, capsys, capsysbinary, cov, doctest_namespace, event_loop, event_loop_policy, free_tcp_port, free_tcp_port_factory, free_udp_port, free_udp_port_factory, mock_codebase, mock_env_vars, monkeypatch, no_cover, pytestconfig, record_property, record_testsuite_property, record_xml_attribute, recwarn, sample_files, snapshot, temp_env_vars, tests/langchain/unit/test_langchain_tools.py::&lt;event_loop&gt;, tests/langchain/unit/test_langchain_tools.py::TestGitHubTools::&lt;event_loop&gt;, tests::&lt;event_loop&gt;, tmp_path, tmp_path_factory, tmpdir, tmpdir_factory, unused_tcp_port, unused_tcp_port_factory, unused_udp_port, unused_udp_port_factory
&gt;       use 'pytest --fixtures [testpath]' for help on them.

/Users/<USER>/Documents/github/rippr/tests/fixtures.py:120</error></testcase><testcase classname="tests.langchain.unit.test_langchain_tools.TestGitHubTools" name="test_github_view_pr_tool_run_success" time="0.000"><error message="failed on setup with &quot;file /Users/<USER>/Documents/github/rippr/tests/langchain/unit/test_langchain_tools.py, line 425&#10;      def test_github_view_pr_tool_run_success(self, mock_codebase, mock_env_vars):&#10;file /Users/<USER>/Documents/github/rippr/tests/fixtures.py, line 120&#10;  @pytest.fixture&#10;  def mock_codebase(temp_workspace: Path, sample_files: Dict[str, str]) -&gt; Mock:&#10;E       fixture 'temp_workspace' not found&#10;&gt;       available fixtures: _session_event_loop, anyio_backend, anyio_backend_name, anyio_backend_options, cache, capfd, capfdbinary, caplog, capsys, capsysbinary, cov, doctest_namespace, event_loop, event_loop_policy, free_tcp_port, free_tcp_port_factory, free_udp_port, free_udp_port_factory, mock_codebase, mock_env_vars, monkeypatch, no_cover, pytestconfig, record_property, record_testsuite_property, record_xml_attribute, recwarn, sample_files, snapshot, temp_env_vars, tests/langchain/unit/test_langchain_tools.py::&lt;event_loop&gt;, tests/langchain/unit/test_langchain_tools.py::TestGitHubTools::&lt;event_loop&gt;, tests::&lt;event_loop&gt;, tmp_path, tmp_path_factory, tmpdir, tmpdir_factory, unused_tcp_port, unused_tcp_port_factory, unused_udp_port, unused_udp_port_factory&#10;&gt;       use 'pytest --fixtures [testpath]' for help on them.&#10;&#10;/Users/<USER>/Documents/github/rippr/tests/fixtures.py:120&quot;">file /Users/<USER>/Documents/github/rippr/tests/langchain/unit/test_langchain_tools.py, line 425
      def test_github_view_pr_tool_run_success(self, mock_codebase, mock_env_vars):
file /Users/<USER>/Documents/github/rippr/tests/fixtures.py, line 120
  @pytest.fixture
  def mock_codebase(temp_workspace: Path, sample_files: Dict[str, str]) -&gt; Mock:
E       fixture 'temp_workspace' not found
&gt;       available fixtures: _session_event_loop, anyio_backend, anyio_backend_name, anyio_backend_options, cache, capfd, capfdbinary, caplog, capsys, capsysbinary, cov, doctest_namespace, event_loop, event_loop_policy, free_tcp_port, free_tcp_port_factory, free_udp_port, free_udp_port_factory, mock_codebase, mock_env_vars, monkeypatch, no_cover, pytestconfig, record_property, record_testsuite_property, record_xml_attribute, recwarn, sample_files, snapshot, temp_env_vars, tests/langchain/unit/test_langchain_tools.py::&lt;event_loop&gt;, tests/langchain/unit/test_langchain_tools.py::TestGitHubTools::&lt;event_loop&gt;, tests::&lt;event_loop&gt;, tmp_path, tmp_path_factory, tmpdir, tmpdir_factory, unused_tcp_port, unused_tcp_port_factory, unused_udp_port, unused_udp_port_factory
&gt;       use 'pytest --fixtures [testpath]' for help on them.

/Users/<USER>/Documents/github/rippr/tests/fixtures.py:120</error></testcase><testcase classname="tests.langchain.unit.test_langchain_tools.TestLinearTools" name="test_linear_get_issue_tool_initialization" time="0.000"><error message="failed on setup with &quot;file /Users/<USER>/Documents/github/rippr/tests/langchain/unit/test_langchain_tools.py, line 447&#10;      def test_linear_get_issue_tool_initialization(self, mock_codebase):&#10;file /Users/<USER>/Documents/github/rippr/tests/fixtures.py, line 120&#10;  @pytest.fixture&#10;  def mock_codebase(temp_workspace: Path, sample_files: Dict[str, str]) -&gt; Mock:&#10;E       fixture 'temp_workspace' not found&#10;&gt;       available fixtures: _session_event_loop, anyio_backend, anyio_backend_name, anyio_backend_options, cache, capfd, capfdbinary, caplog, capsys, capsysbinary, cov, doctest_namespace, event_loop, event_loop_policy, free_tcp_port, free_tcp_port_factory, free_udp_port, free_udp_port_factory, mock_codebase, mock_env_vars, monkeypatch, no_cover, pytestconfig, record_property, record_testsuite_property, record_xml_attribute, recwarn, sample_files, snapshot, temp_env_vars, tests/langchain/unit/test_langchain_tools.py::&lt;event_loop&gt;, tests/langchain/unit/test_langchain_tools.py::TestLinearTools::&lt;event_loop&gt;, tests::&lt;event_loop&gt;, tmp_path, tmp_path_factory, tmpdir, tmpdir_factory, unused_tcp_port, unused_tcp_port_factory, unused_udp_port, unused_udp_port_factory&#10;&gt;       use 'pytest --fixtures [testpath]' for help on them.&#10;&#10;/Users/<USER>/Documents/github/rippr/tests/fixtures.py:120&quot;">file /Users/<USER>/Documents/github/rippr/tests/langchain/unit/test_langchain_tools.py, line 447
      def test_linear_get_issue_tool_initialization(self, mock_codebase):
file /Users/<USER>/Documents/github/rippr/tests/fixtures.py, line 120
  @pytest.fixture
  def mock_codebase(temp_workspace: Path, sample_files: Dict[str, str]) -&gt; Mock:
E       fixture 'temp_workspace' not found
&gt;       available fixtures: _session_event_loop, anyio_backend, anyio_backend_name, anyio_backend_options, cache, capfd, capfdbinary, caplog, capsys, capsysbinary, cov, doctest_namespace, event_loop, event_loop_policy, free_tcp_port, free_tcp_port_factory, free_udp_port, free_udp_port_factory, mock_codebase, mock_env_vars, monkeypatch, no_cover, pytestconfig, record_property, record_testsuite_property, record_xml_attribute, recwarn, sample_files, snapshot, temp_env_vars, tests/langchain/unit/test_langchain_tools.py::&lt;event_loop&gt;, tests/langchain/unit/test_langchain_tools.py::TestLinearTools::&lt;event_loop&gt;, tests::&lt;event_loop&gt;, tmp_path, tmp_path_factory, tmpdir, tmpdir_factory, unused_tcp_port, unused_tcp_port_factory, unused_udp_port, unused_udp_port_factory
&gt;       use 'pytest --fixtures [testpath]' for help on them.

/Users/<USER>/Documents/github/rippr/tests/fixtures.py:120</error></testcase><testcase classname="tests.langchain.unit.test_langchain_tools.TestLinearTools" name="test_linear_get_issue_tool_run_success" time="0.000"><error message="failed on setup with &quot;file /Users/<USER>/Documents/github/rippr/tests/langchain/unit/test_langchain_tools.py, line 455&#10;      def test_linear_get_issue_tool_run_success(self, mock_codebase, mock_env_vars):&#10;file /Users/<USER>/Documents/github/rippr/tests/fixtures.py, line 120&#10;  @pytest.fixture&#10;  def mock_codebase(temp_workspace: Path, sample_files: Dict[str, str]) -&gt; Mock:&#10;E       fixture 'temp_workspace' not found&#10;&gt;       available fixtures: _session_event_loop, anyio_backend, anyio_backend_name, anyio_backend_options, cache, capfd, capfdbinary, caplog, capsys, capsysbinary, cov, doctest_namespace, event_loop, event_loop_policy, free_tcp_port, free_tcp_port_factory, free_udp_port, free_udp_port_factory, mock_codebase, mock_env_vars, monkeypatch, no_cover, pytestconfig, record_property, record_testsuite_property, record_xml_attribute, recwarn, sample_files, snapshot, temp_env_vars, tests/langchain/unit/test_langchain_tools.py::&lt;event_loop&gt;, tests/langchain/unit/test_langchain_tools.py::TestLinearTools::&lt;event_loop&gt;, tests::&lt;event_loop&gt;, tmp_path, tmp_path_factory, tmpdir, tmpdir_factory, unused_tcp_port, unused_tcp_port_factory, unused_udp_port, unused_udp_port_factory&#10;&gt;       use 'pytest --fixtures [testpath]' for help on them.&#10;&#10;/Users/<USER>/Documents/github/rippr/tests/fixtures.py:120&quot;">file /Users/<USER>/Documents/github/rippr/tests/langchain/unit/test_langchain_tools.py, line 455
      def test_linear_get_issue_tool_run_success(self, mock_codebase, mock_env_vars):
file /Users/<USER>/Documents/github/rippr/tests/fixtures.py, line 120
  @pytest.fixture
  def mock_codebase(temp_workspace: Path, sample_files: Dict[str, str]) -&gt; Mock:
E       fixture 'temp_workspace' not found
&gt;       available fixtures: _session_event_loop, anyio_backend, anyio_backend_name, anyio_backend_options, cache, capfd, capfdbinary, caplog, capsys, capsysbinary, cov, doctest_namespace, event_loop, event_loop_policy, free_tcp_port, free_tcp_port_factory, free_udp_port, free_udp_port_factory, mock_codebase, mock_env_vars, monkeypatch, no_cover, pytestconfig, record_property, record_testsuite_property, record_xml_attribute, recwarn, sample_files, snapshot, temp_env_vars, tests/langchain/unit/test_langchain_tools.py::&lt;event_loop&gt;, tests/langchain/unit/test_langchain_tools.py::TestLinearTools::&lt;event_loop&gt;, tests::&lt;event_loop&gt;, tmp_path, tmp_path_factory, tmpdir, tmpdir_factory, unused_tcp_port, unused_tcp_port_factory, unused_udp_port, unused_udp_port_factory
&gt;       use 'pytest --fixtures [testpath]' for help on them.

/Users/<USER>/Documents/github/rippr/tests/fixtures.py:120</error></testcase><testcase classname="tests.langchain.unit.test_langchain_tools.TestLinearTools" name="test_linear_comment_on_issue_tool_initialization" time="0.000"><error message="failed on setup with &quot;file /Users/<USER>/Documents/github/rippr/tests/langchain/unit/test_langchain_tools.py, line 473&#10;      def test_linear_comment_on_issue_tool_initialization(self, mock_codebase):&#10;file /Users/<USER>/Documents/github/rippr/tests/fixtures.py, line 120&#10;  @pytest.fixture&#10;  def mock_codebase(temp_workspace: Path, sample_files: Dict[str, str]) -&gt; Mock:&#10;E       fixture 'temp_workspace' not found&#10;&gt;       available fixtures: _session_event_loop, anyio_backend, anyio_backend_name, anyio_backend_options, cache, capfd, capfdbinary, caplog, capsys, capsysbinary, cov, doctest_namespace, event_loop, event_loop_policy, free_tcp_port, free_tcp_port_factory, free_udp_port, free_udp_port_factory, mock_codebase, mock_env_vars, monkeypatch, no_cover, pytestconfig, record_property, record_testsuite_property, record_xml_attribute, recwarn, sample_files, snapshot, temp_env_vars, tests/langchain/unit/test_langchain_tools.py::&lt;event_loop&gt;, tests/langchain/unit/test_langchain_tools.py::TestLinearTools::&lt;event_loop&gt;, tests::&lt;event_loop&gt;, tmp_path, tmp_path_factory, tmpdir, tmpdir_factory, unused_tcp_port, unused_tcp_port_factory, unused_udp_port, unused_udp_port_factory&#10;&gt;       use 'pytest --fixtures [testpath]' for help on them.&#10;&#10;/Users/<USER>/Documents/github/rippr/tests/fixtures.py:120&quot;">file /Users/<USER>/Documents/github/rippr/tests/langchain/unit/test_langchain_tools.py, line 473
      def test_linear_comment_on_issue_tool_initialization(self, mock_codebase):
file /Users/<USER>/Documents/github/rippr/tests/fixtures.py, line 120
  @pytest.fixture
  def mock_codebase(temp_workspace: Path, sample_files: Dict[str, str]) -&gt; Mock:
E       fixture 'temp_workspace' not found
&gt;       available fixtures: _session_event_loop, anyio_backend, anyio_backend_name, anyio_backend_options, cache, capfd, capfdbinary, caplog, capsys, capsysbinary, cov, doctest_namespace, event_loop, event_loop_policy, free_tcp_port, free_tcp_port_factory, free_udp_port, free_udp_port_factory, mock_codebase, mock_env_vars, monkeypatch, no_cover, pytestconfig, record_property, record_testsuite_property, record_xml_attribute, recwarn, sample_files, snapshot, temp_env_vars, tests/langchain/unit/test_langchain_tools.py::&lt;event_loop&gt;, tests/langchain/unit/test_langchain_tools.py::TestLinearTools::&lt;event_loop&gt;, tests::&lt;event_loop&gt;, tmp_path, tmp_path_factory, tmpdir, tmpdir_factory, unused_tcp_port, unused_tcp_port_factory, unused_udp_port, unused_udp_port_factory
&gt;       use 'pytest --fixtures [testpath]' for help on them.

/Users/<USER>/Documents/github/rippr/tests/fixtures.py:120</error></testcase><testcase classname="tests.langchain.unit.test_langchain_tools.TestLinearTools" name="test_linear_comment_on_issue_tool_run_success" time="0.000"><error message="failed on setup with &quot;file /Users/<USER>/Documents/github/rippr/tests/langchain/unit/test_langchain_tools.py, line 481&#10;      def test_linear_comment_on_issue_tool_run_success(self, mock_codebase, mock_env_vars):&#10;file /Users/<USER>/Documents/github/rippr/tests/fixtures.py, line 120&#10;  @pytest.fixture&#10;  def mock_codebase(temp_workspace: Path, sample_files: Dict[str, str]) -&gt; Mock:&#10;E       fixture 'temp_workspace' not found&#10;&gt;       available fixtures: _session_event_loop, anyio_backend, anyio_backend_name, anyio_backend_options, cache, capfd, capfdbinary, caplog, capsys, capsysbinary, cov, doctest_namespace, event_loop, event_loop_policy, free_tcp_port, free_tcp_port_factory, free_udp_port, free_udp_port_factory, mock_codebase, mock_env_vars, monkeypatch, no_cover, pytestconfig, record_property, record_testsuite_property, record_xml_attribute, recwarn, sample_files, snapshot, temp_env_vars, tests/langchain/unit/test_langchain_tools.py::&lt;event_loop&gt;, tests/langchain/unit/test_langchain_tools.py::TestLinearTools::&lt;event_loop&gt;, tests::&lt;event_loop&gt;, tmp_path, tmp_path_factory, tmpdir, tmpdir_factory, unused_tcp_port, unused_tcp_port_factory, unused_udp_port, unused_udp_port_factory&#10;&gt;       use 'pytest --fixtures [testpath]' for help on them.&#10;&#10;/Users/<USER>/Documents/github/rippr/tests/fixtures.py:120&quot;">file /Users/<USER>/Documents/github/rippr/tests/langchain/unit/test_langchain_tools.py, line 481
      def test_linear_comment_on_issue_tool_run_success(self, mock_codebase, mock_env_vars):
file /Users/<USER>/Documents/github/rippr/tests/fixtures.py, line 120
  @pytest.fixture
  def mock_codebase(temp_workspace: Path, sample_files: Dict[str, str]) -&gt; Mock:
E       fixture 'temp_workspace' not found
&gt;       available fixtures: _session_event_loop, anyio_backend, anyio_backend_name, anyio_backend_options, cache, capfd, capfdbinary, caplog, capsys, capsysbinary, cov, doctest_namespace, event_loop, event_loop_policy, free_tcp_port, free_tcp_port_factory, free_udp_port, free_udp_port_factory, mock_codebase, mock_env_vars, monkeypatch, no_cover, pytestconfig, record_property, record_testsuite_property, record_xml_attribute, recwarn, sample_files, snapshot, temp_env_vars, tests/langchain/unit/test_langchain_tools.py::&lt;event_loop&gt;, tests/langchain/unit/test_langchain_tools.py::TestLinearTools::&lt;event_loop&gt;, tests::&lt;event_loop&gt;, tmp_path, tmp_path_factory, tmpdir, tmpdir_factory, unused_tcp_port, unused_tcp_port_factory, unused_udp_port, unused_udp_port_factory
&gt;       use 'pytest --fixtures [testpath]' for help on them.

/Users/<USER>/Documents/github/rippr/tests/fixtures.py:120</error></testcase><testcase classname="tests.langchain.unit.test_langchain_tools.TestEditTools" name="test_replacement_edit_tool_initialization" time="0.000"><error message="failed on setup with &quot;file /Users/<USER>/Documents/github/rippr/tests/langchain/unit/test_langchain_tools.py, line 511&#10;      def test_replacement_edit_tool_initialization(self, mock_codebase):&#10;file /Users/<USER>/Documents/github/rippr/tests/fixtures.py, line 120&#10;  @pytest.fixture&#10;  def mock_codebase(temp_workspace: Path, sample_files: Dict[str, str]) -&gt; Mock:&#10;E       fixture 'temp_workspace' not found&#10;&gt;       available fixtures: _session_event_loop, anyio_backend, anyio_backend_name, anyio_backend_options, cache, capfd, capfdbinary, caplog, capsys, capsysbinary, cov, doctest_namespace, event_loop, event_loop_policy, free_tcp_port, free_tcp_port_factory, free_udp_port, free_udp_port_factory, mock_codebase, mock_env_vars, monkeypatch, no_cover, pytestconfig, record_property, record_testsuite_property, record_xml_attribute, recwarn, sample_files, snapshot, temp_env_vars, tests/langchain/unit/test_langchain_tools.py::&lt;event_loop&gt;, tests/langchain/unit/test_langchain_tools.py::TestEditTools::&lt;event_loop&gt;, tests::&lt;event_loop&gt;, tmp_path, tmp_path_factory, tmpdir, tmpdir_factory, unused_tcp_port, unused_tcp_port_factory, unused_udp_port, unused_udp_port_factory&#10;&gt;       use 'pytest --fixtures [testpath]' for help on them.&#10;&#10;/Users/<USER>/Documents/github/rippr/tests/fixtures.py:120&quot;">file /Users/<USER>/Documents/github/rippr/tests/langchain/unit/test_langchain_tools.py, line 511
      def test_replacement_edit_tool_initialization(self, mock_codebase):
file /Users/<USER>/Documents/github/rippr/tests/fixtures.py, line 120
  @pytest.fixture
  def mock_codebase(temp_workspace: Path, sample_files: Dict[str, str]) -&gt; Mock:
E       fixture 'temp_workspace' not found
&gt;       available fixtures: _session_event_loop, anyio_backend, anyio_backend_name, anyio_backend_options, cache, capfd, capfdbinary, caplog, capsys, capsysbinary, cov, doctest_namespace, event_loop, event_loop_policy, free_tcp_port, free_tcp_port_factory, free_udp_port, free_udp_port_factory, mock_codebase, mock_env_vars, monkeypatch, no_cover, pytestconfig, record_property, record_testsuite_property, record_xml_attribute, recwarn, sample_files, snapshot, temp_env_vars, tests/langchain/unit/test_langchain_tools.py::&lt;event_loop&gt;, tests/langchain/unit/test_langchain_tools.py::TestEditTools::&lt;event_loop&gt;, tests::&lt;event_loop&gt;, tmp_path, tmp_path_factory, tmpdir, tmpdir_factory, unused_tcp_port, unused_tcp_port_factory, unused_udp_port, unused_udp_port_factory
&gt;       use 'pytest --fixtures [testpath]' for help on them.

/Users/<USER>/Documents/github/rippr/tests/fixtures.py:120</error></testcase><testcase classname="tests.langchain.unit.test_langchain_tools.TestEditTools" name="test_replacement_edit_tool_run_success" time="0.000"><error message="failed on setup with &quot;file /Users/<USER>/Documents/github/rippr/tests/langchain/unit/test_langchain_tools.py, line 519&#10;      def test_replacement_edit_tool_run_success(self, mock_codebase):&#10;file /Users/<USER>/Documents/github/rippr/tests/fixtures.py, line 120&#10;  @pytest.fixture&#10;  def mock_codebase(temp_workspace: Path, sample_files: Dict[str, str]) -&gt; Mock:&#10;E       fixture 'temp_workspace' not found&#10;&gt;       available fixtures: _session_event_loop, anyio_backend, anyio_backend_name, anyio_backend_options, cache, capfd, capfdbinary, caplog, capsys, capsysbinary, cov, doctest_namespace, event_loop, event_loop_policy, free_tcp_port, free_tcp_port_factory, free_udp_port, free_udp_port_factory, mock_codebase, mock_env_vars, monkeypatch, no_cover, pytestconfig, record_property, record_testsuite_property, record_xml_attribute, recwarn, sample_files, snapshot, temp_env_vars, tests/langchain/unit/test_langchain_tools.py::&lt;event_loop&gt;, tests/langchain/unit/test_langchain_tools.py::TestEditTools::&lt;event_loop&gt;, tests::&lt;event_loop&gt;, tmp_path, tmp_path_factory, tmpdir, tmpdir_factory, unused_tcp_port, unused_tcp_port_factory, unused_udp_port, unused_udp_port_factory&#10;&gt;       use 'pytest --fixtures [testpath]' for help on them.&#10;&#10;/Users/<USER>/Documents/github/rippr/tests/fixtures.py:120&quot;">file /Users/<USER>/Documents/github/rippr/tests/langchain/unit/test_langchain_tools.py, line 519
      def test_replacement_edit_tool_run_success(self, mock_codebase):
file /Users/<USER>/Documents/github/rippr/tests/fixtures.py, line 120
  @pytest.fixture
  def mock_codebase(temp_workspace: Path, sample_files: Dict[str, str]) -&gt; Mock:
E       fixture 'temp_workspace' not found
&gt;       available fixtures: _session_event_loop, anyio_backend, anyio_backend_name, anyio_backend_options, cache, capfd, capfdbinary, caplog, capsys, capsysbinary, cov, doctest_namespace, event_loop, event_loop_policy, free_tcp_port, free_tcp_port_factory, free_udp_port, free_udp_port_factory, mock_codebase, mock_env_vars, monkeypatch, no_cover, pytestconfig, record_property, record_testsuite_property, record_xml_attribute, recwarn, sample_files, snapshot, temp_env_vars, tests/langchain/unit/test_langchain_tools.py::&lt;event_loop&gt;, tests/langchain/unit/test_langchain_tools.py::TestEditTools::&lt;event_loop&gt;, tests::&lt;event_loop&gt;, tmp_path, tmp_path_factory, tmpdir, tmpdir_factory, unused_tcp_port, unused_tcp_port_factory, unused_udp_port, unused_udp_port_factory
&gt;       use 'pytest --fixtures [testpath]' for help on them.

/Users/<USER>/Documents/github/rippr/tests/fixtures.py:120</error></testcase><testcase classname="tests.langchain.unit.test_langchain_tools.TestEditTools" name="test_global_replacement_edit_tool_initialization" time="0.000"><error message="failed on setup with &quot;file /Users/<USER>/Documents/github/rippr/tests/langchain/unit/test_langchain_tools.py, line 549&#10;      def test_global_replacement_edit_tool_initialization(self, mock_codebase):&#10;file /Users/<USER>/Documents/github/rippr/tests/fixtures.py, line 120&#10;  @pytest.fixture&#10;  def mock_codebase(temp_workspace: Path, sample_files: Dict[str, str]) -&gt; Mock:&#10;E       fixture 'temp_workspace' not found&#10;&gt;       available fixtures: _session_event_loop, anyio_backend, anyio_backend_name, anyio_backend_options, cache, capfd, capfdbinary, caplog, capsys, capsysbinary, cov, doctest_namespace, event_loop, event_loop_policy, free_tcp_port, free_tcp_port_factory, free_udp_port, free_udp_port_factory, mock_codebase, mock_env_vars, monkeypatch, no_cover, pytestconfig, record_property, record_testsuite_property, record_xml_attribute, recwarn, sample_files, snapshot, temp_env_vars, tests/langchain/unit/test_langchain_tools.py::&lt;event_loop&gt;, tests/langchain/unit/test_langchain_tools.py::TestEditTools::&lt;event_loop&gt;, tests::&lt;event_loop&gt;, tmp_path, tmp_path_factory, tmpdir, tmpdir_factory, unused_tcp_port, unused_tcp_port_factory, unused_udp_port, unused_udp_port_factory&#10;&gt;       use 'pytest --fixtures [testpath]' for help on them.&#10;&#10;/Users/<USER>/Documents/github/rippr/tests/fixtures.py:120&quot;">file /Users/<USER>/Documents/github/rippr/tests/langchain/unit/test_langchain_tools.py, line 549
      def test_global_replacement_edit_tool_initialization(self, mock_codebase):
file /Users/<USER>/Documents/github/rippr/tests/fixtures.py, line 120
  @pytest.fixture
  def mock_codebase(temp_workspace: Path, sample_files: Dict[str, str]) -&gt; Mock:
E       fixture 'temp_workspace' not found
&gt;       available fixtures: _session_event_loop, anyio_backend, anyio_backend_name, anyio_backend_options, cache, capfd, capfdbinary, caplog, capsys, capsysbinary, cov, doctest_namespace, event_loop, event_loop_policy, free_tcp_port, free_tcp_port_factory, free_udp_port, free_udp_port_factory, mock_codebase, mock_env_vars, monkeypatch, no_cover, pytestconfig, record_property, record_testsuite_property, record_xml_attribute, recwarn, sample_files, snapshot, temp_env_vars, tests/langchain/unit/test_langchain_tools.py::&lt;event_loop&gt;, tests/langchain/unit/test_langchain_tools.py::TestEditTools::&lt;event_loop&gt;, tests::&lt;event_loop&gt;, tmp_path, tmp_path_factory, tmpdir, tmpdir_factory, unused_tcp_port, unused_tcp_port_factory, unused_udp_port, unused_udp_port_factory
&gt;       use 'pytest --fixtures [testpath]' for help on them.

/Users/<USER>/Documents/github/rippr/tests/fixtures.py:120</error></testcase><testcase classname="tests.langchain.unit.test_langchain_tools.TestEditTools" name="test_global_replacement_edit_tool_run_success" time="0.000"><error message="failed on setup with &quot;file /Users/<USER>/Documents/github/rippr/tests/langchain/unit/test_langchain_tools.py, line 557&#10;      def test_global_replacement_edit_tool_run_success(self, mock_codebase):&#10;file /Users/<USER>/Documents/github/rippr/tests/fixtures.py, line 120&#10;  @pytest.fixture&#10;  def mock_codebase(temp_workspace: Path, sample_files: Dict[str, str]) -&gt; Mock:&#10;E       fixture 'temp_workspace' not found&#10;&gt;       available fixtures: _session_event_loop, anyio_backend, anyio_backend_name, anyio_backend_options, cache, capfd, capfdbinary, caplog, capsys, capsysbinary, cov, doctest_namespace, event_loop, event_loop_policy, free_tcp_port, free_tcp_port_factory, free_udp_port, free_udp_port_factory, mock_codebase, mock_env_vars, monkeypatch, no_cover, pytestconfig, record_property, record_testsuite_property, record_xml_attribute, recwarn, sample_files, snapshot, temp_env_vars, tests/langchain/unit/test_langchain_tools.py::&lt;event_loop&gt;, tests/langchain/unit/test_langchain_tools.py::TestEditTools::&lt;event_loop&gt;, tests::&lt;event_loop&gt;, tmp_path, tmp_path_factory, tmpdir, tmpdir_factory, unused_tcp_port, unused_tcp_port_factory, unused_udp_port, unused_udp_port_factory&#10;&gt;       use 'pytest --fixtures [testpath]' for help on them.&#10;&#10;/Users/<USER>/Documents/github/rippr/tests/fixtures.py:120&quot;">file /Users/<USER>/Documents/github/rippr/tests/langchain/unit/test_langchain_tools.py, line 557
      def test_global_replacement_edit_tool_run_success(self, mock_codebase):
file /Users/<USER>/Documents/github/rippr/tests/fixtures.py, line 120
  @pytest.fixture
  def mock_codebase(temp_workspace: Path, sample_files: Dict[str, str]) -&gt; Mock:
E       fixture 'temp_workspace' not found
&gt;       available fixtures: _session_event_loop, anyio_backend, anyio_backend_name, anyio_backend_options, cache, capfd, capfdbinary, caplog, capsys, capsysbinary, cov, doctest_namespace, event_loop, event_loop_policy, free_tcp_port, free_tcp_port_factory, free_udp_port, free_udp_port_factory, mock_codebase, mock_env_vars, monkeypatch, no_cover, pytestconfig, record_property, record_testsuite_property, record_xml_attribute, recwarn, sample_files, snapshot, temp_env_vars, tests/langchain/unit/test_langchain_tools.py::&lt;event_loop&gt;, tests/langchain/unit/test_langchain_tools.py::TestEditTools::&lt;event_loop&gt;, tests::&lt;event_loop&gt;, tmp_path, tmp_path_factory, tmpdir, tmpdir_factory, unused_tcp_port, unused_tcp_port_factory, unused_udp_port, unused_udp_port_factory
&gt;       use 'pytest --fixtures [testpath]' for help on them.

/Users/<USER>/Documents/github/rippr/tests/fixtures.py:120</error></testcase><testcase classname="tests.langchain.unit.test_langchain_tools.TestUtilityTools" name="test_bash_command_tool_initialization" time="0.000" /><testcase classname="tests.langchain.unit.test_langchain_tools.TestUtilityTools" name="test_bash_command_tool_run_success" time="0.003"><failure message="TypeError: RunBashCommandTool._run() got an unexpected keyword argument 'tool_call_id'">self = &lt;test_langchain_tools.TestUtilityTools object at 0x12c14f6b0&gt;

    def test_bash_command_tool_run_success(self):
        """Test successful RunBashCommandTool execution."""
        tool = RunBashCommandTool()
    
        with patch('src.tools.bash.run_bash_command') as mock_bash:
            mock_result = Mock()
            mock_result.render.return_value = ToolMessage(
                content="Command executed",
                tool_call_id="test_id",
                name="run_bash_command"
            )
            mock_bash.return_value = mock_result
    
&gt;           result = tool._run(tool_call_id="test_id", command="ls -la")
E           TypeError: RunBashCommandTool._run() got an unexpected keyword argument 'tool_call_id'

tests/langchain/unit/test_langchain_tools.py:610: TypeError</failure></testcase><testcase classname="tests.langchain.unit.test_langchain_tools.TestUtilityTools" name="test_reflection_tool_initialization" time="0.000"><error message="failed on setup with &quot;file /Users/<USER>/Documents/github/rippr/tests/langchain/unit/test_langchain_tools.py, line 615&#10;      def test_reflection_tool_initialization(self, mock_codebase):&#10;file /Users/<USER>/Documents/github/rippr/tests/fixtures.py, line 120&#10;  @pytest.fixture&#10;  def mock_codebase(temp_workspace: Path, sample_files: Dict[str, str]) -&gt; Mock:&#10;E       fixture 'temp_workspace' not found&#10;&gt;       available fixtures: _session_event_loop, anyio_backend, anyio_backend_name, anyio_backend_options, cache, capfd, capfdbinary, caplog, capsys, capsysbinary, cov, doctest_namespace, event_loop, event_loop_policy, free_tcp_port, free_tcp_port_factory, free_udp_port, free_udp_port_factory, mock_codebase, mock_env_vars, monkeypatch, no_cover, pytestconfig, record_property, record_testsuite_property, record_xml_attribute, recwarn, sample_files, snapshot, temp_env_vars, tests/langchain/unit/test_langchain_tools.py::&lt;event_loop&gt;, tests/langchain/unit/test_langchain_tools.py::TestUtilityTools::&lt;event_loop&gt;, tests::&lt;event_loop&gt;, tmp_path, tmp_path_factory, tmpdir, tmpdir_factory, unused_tcp_port, unused_tcp_port_factory, unused_udp_port, unused_udp_port_factory&#10;&gt;       use 'pytest --fixtures [testpath]' for help on them.&#10;&#10;/Users/<USER>/Documents/github/rippr/tests/fixtures.py:120&quot;">file /Users/<USER>/Documents/github/rippr/tests/langchain/unit/test_langchain_tools.py, line 615
      def test_reflection_tool_initialization(self, mock_codebase):
file /Users/<USER>/Documents/github/rippr/tests/fixtures.py, line 120
  @pytest.fixture
  def mock_codebase(temp_workspace: Path, sample_files: Dict[str, str]) -&gt; Mock:
E       fixture 'temp_workspace' not found
&gt;       available fixtures: _session_event_loop, anyio_backend, anyio_backend_name, anyio_backend_options, cache, capfd, capfdbinary, caplog, capsys, capsysbinary, cov, doctest_namespace, event_loop, event_loop_policy, free_tcp_port, free_tcp_port_factory, free_udp_port, free_udp_port_factory, mock_codebase, mock_env_vars, monkeypatch, no_cover, pytestconfig, record_property, record_testsuite_property, record_xml_attribute, recwarn, sample_files, snapshot, temp_env_vars, tests/langchain/unit/test_langchain_tools.py::&lt;event_loop&gt;, tests/langchain/unit/test_langchain_tools.py::TestUtilityTools::&lt;event_loop&gt;, tests::&lt;event_loop&gt;, tmp_path, tmp_path_factory, tmpdir, tmpdir_factory, unused_tcp_port, unused_tcp_port_factory, unused_udp_port, unused_udp_port_factory
&gt;       use 'pytest --fixtures [testpath]' for help on them.

/Users/<USER>/Documents/github/rippr/tests/fixtures.py:120</error></testcase><testcase classname="tests.langchain.unit.test_langchain_tools.TestUtilityTools" name="test_reflection_tool_run_success" time="0.000"><error message="failed on setup with &quot;file /Users/<USER>/Documents/github/rippr/tests/langchain/unit/test_langchain_tools.py, line 623&#10;      def test_reflection_tool_run_success(self, mock_codebase):&#10;file /Users/<USER>/Documents/github/rippr/tests/fixtures.py, line 120&#10;  @pytest.fixture&#10;  def mock_codebase(temp_workspace: Path, sample_files: Dict[str, str]) -&gt; Mock:&#10;E       fixture 'temp_workspace' not found&#10;&gt;       available fixtures: _session_event_loop, anyio_backend, anyio_backend_name, anyio_backend_options, cache, capfd, capfdbinary, caplog, capsys, capsysbinary, cov, doctest_namespace, event_loop, event_loop_policy, free_tcp_port, free_tcp_port_factory, free_udp_port, free_udp_port_factory, mock_codebase, mock_env_vars, monkeypatch, no_cover, pytestconfig, record_property, record_testsuite_property, record_xml_attribute, recwarn, sample_files, snapshot, temp_env_vars, tests/langchain/unit/test_langchain_tools.py::&lt;event_loop&gt;, tests/langchain/unit/test_langchain_tools.py::TestUtilityTools::&lt;event_loop&gt;, tests::&lt;event_loop&gt;, tmp_path, tmp_path_factory, tmpdir, tmpdir_factory, unused_tcp_port, unused_tcp_port_factory, unused_udp_port, unused_udp_port_factory&#10;&gt;       use 'pytest --fixtures [testpath]' for help on them.&#10;&#10;/Users/<USER>/Documents/github/rippr/tests/fixtures.py:120&quot;">file /Users/<USER>/Documents/github/rippr/tests/langchain/unit/test_langchain_tools.py, line 623
      def test_reflection_tool_run_success(self, mock_codebase):
file /Users/<USER>/Documents/github/rippr/tests/fixtures.py, line 120
  @pytest.fixture
  def mock_codebase(temp_workspace: Path, sample_files: Dict[str, str]) -&gt; Mock:
E       fixture 'temp_workspace' not found
&gt;       available fixtures: _session_event_loop, anyio_backend, anyio_backend_name, anyio_backend_options, cache, capfd, capfdbinary, caplog, capsys, capsysbinary, cov, doctest_namespace, event_loop, event_loop_policy, free_tcp_port, free_tcp_port_factory, free_udp_port, free_udp_port_factory, mock_codebase, mock_env_vars, monkeypatch, no_cover, pytestconfig, record_property, record_testsuite_property, record_xml_attribute, recwarn, sample_files, snapshot, temp_env_vars, tests/langchain/unit/test_langchain_tools.py::&lt;event_loop&gt;, tests/langchain/unit/test_langchain_tools.py::TestUtilityTools::&lt;event_loop&gt;, tests::&lt;event_loop&gt;, tmp_path, tmp_path_factory, tmpdir, tmpdir_factory, unused_tcp_port, unused_tcp_port_factory, unused_udp_port, unused_udp_port_factory
&gt;       use 'pytest --fixtures [testpath]' for help on them.

/Users/<USER>/Documents/github/rippr/tests/fixtures.py:120</error></testcase><testcase classname="tests.langchain.unit.test_langchain_tools.TestGetWorkspaceTools" name="test_get_workspace_tools_returns_correct_tools" time="0.000"><error message="failed on setup with &quot;file /Users/<USER>/Documents/github/rippr/tests/langchain/unit/test_langchain_tools.py, line 653&#10;      def test_get_workspace_tools_returns_correct_tools(self, mock_codebase):&#10;file /Users/<USER>/Documents/github/rippr/tests/fixtures.py, line 120&#10;  @pytest.fixture&#10;  def mock_codebase(temp_workspace: Path, sample_files: Dict[str, str]) -&gt; Mock:&#10;E       fixture 'temp_workspace' not found&#10;&gt;       available fixtures: _session_event_loop, anyio_backend, anyio_backend_name, anyio_backend_options, cache, capfd, capfdbinary, caplog, capsys, capsysbinary, cov, doctest_namespace, event_loop, event_loop_policy, free_tcp_port, free_tcp_port_factory, free_udp_port, free_udp_port_factory, mock_codebase, mock_env_vars, monkeypatch, no_cover, pytestconfig, record_property, record_testsuite_property, record_xml_attribute, recwarn, sample_files, snapshot, temp_env_vars, tests/langchain/unit/test_langchain_tools.py::&lt;event_loop&gt;, tests/langchain/unit/test_langchain_tools.py::TestGetWorkspaceTools::&lt;event_loop&gt;, tests::&lt;event_loop&gt;, tmp_path, tmp_path_factory, tmpdir, tmpdir_factory, unused_tcp_port, unused_tcp_port_factory, unused_udp_port, unused_udp_port_factory&#10;&gt;       use 'pytest --fixtures [testpath]' for help on them.&#10;&#10;/Users/<USER>/Documents/github/rippr/tests/fixtures.py:120&quot;">file /Users/<USER>/Documents/github/rippr/tests/langchain/unit/test_langchain_tools.py, line 653
      def test_get_workspace_tools_returns_correct_tools(self, mock_codebase):
file /Users/<USER>/Documents/github/rippr/tests/fixtures.py, line 120
  @pytest.fixture
  def mock_codebase(temp_workspace: Path, sample_files: Dict[str, str]) -&gt; Mock:
E       fixture 'temp_workspace' not found
&gt;       available fixtures: _session_event_loop, anyio_backend, anyio_backend_name, anyio_backend_options, cache, capfd, capfdbinary, caplog, capsys, capsysbinary, cov, doctest_namespace, event_loop, event_loop_policy, free_tcp_port, free_tcp_port_factory, free_udp_port, free_udp_port_factory, mock_codebase, mock_env_vars, monkeypatch, no_cover, pytestconfig, record_property, record_testsuite_property, record_xml_attribute, recwarn, sample_files, snapshot, temp_env_vars, tests/langchain/unit/test_langchain_tools.py::&lt;event_loop&gt;, tests/langchain/unit/test_langchain_tools.py::TestGetWorkspaceTools::&lt;event_loop&gt;, tests::&lt;event_loop&gt;, tmp_path, tmp_path_factory, tmpdir, tmpdir_factory, unused_tcp_port, unused_tcp_port_factory, unused_udp_port, unused_udp_port_factory
&gt;       use 'pytest --fixtures [testpath]' for help on them.

/Users/<USER>/Documents/github/rippr/tests/fixtures.py:120</error></testcase><testcase classname="tests.langchain.unit.test_langchain_tools.TestGetWorkspaceTools" name="test_get_workspace_tools_with_codebase_injection" time="0.000"><error message="failed on setup with &quot;file /Users/<USER>/Documents/github/rippr/tests/langchain/unit/test_langchain_tools.py, line 671&#10;      def test_get_workspace_tools_with_codebase_injection(self, mock_codebase):&#10;file /Users/<USER>/Documents/github/rippr/tests/fixtures.py, line 120&#10;  @pytest.fixture&#10;  def mock_codebase(temp_workspace: Path, sample_files: Dict[str, str]) -&gt; Mock:&#10;E       fixture 'temp_workspace' not found&#10;&gt;       available fixtures: _session_event_loop, anyio_backend, anyio_backend_name, anyio_backend_options, cache, capfd, capfdbinary, caplog, capsys, capsysbinary, cov, doctest_namespace, event_loop, event_loop_policy, free_tcp_port, free_tcp_port_factory, free_udp_port, free_udp_port_factory, mock_codebase, mock_env_vars, monkeypatch, no_cover, pytestconfig, record_property, record_testsuite_property, record_xml_attribute, recwarn, sample_files, snapshot, temp_env_vars, tests/langchain/unit/test_langchain_tools.py::&lt;event_loop&gt;, tests/langchain/unit/test_langchain_tools.py::TestGetWorkspaceTools::&lt;event_loop&gt;, tests::&lt;event_loop&gt;, tmp_path, tmp_path_factory, tmpdir, tmpdir_factory, unused_tcp_port, unused_tcp_port_factory, unused_udp_port, unused_udp_port_factory&#10;&gt;       use 'pytest --fixtures [testpath]' for help on them.&#10;&#10;/Users/<USER>/Documents/github/rippr/tests/fixtures.py:120&quot;">file /Users/<USER>/Documents/github/rippr/tests/langchain/unit/test_langchain_tools.py, line 671
      def test_get_workspace_tools_with_codebase_injection(self, mock_codebase):
file /Users/<USER>/Documents/github/rippr/tests/fixtures.py, line 120
  @pytest.fixture
  def mock_codebase(temp_workspace: Path, sample_files: Dict[str, str]) -&gt; Mock:
E       fixture 'temp_workspace' not found
&gt;       available fixtures: _session_event_loop, anyio_backend, anyio_backend_name, anyio_backend_options, cache, capfd, capfdbinary, caplog, capsys, capsysbinary, cov, doctest_namespace, event_loop, event_loop_policy, free_tcp_port, free_tcp_port_factory, free_udp_port, free_udp_port_factory, mock_codebase, mock_env_vars, monkeypatch, no_cover, pytestconfig, record_property, record_testsuite_property, record_xml_attribute, recwarn, sample_files, snapshot, temp_env_vars, tests/langchain/unit/test_langchain_tools.py::&lt;event_loop&gt;, tests/langchain/unit/test_langchain_tools.py::TestGetWorkspaceTools::&lt;event_loop&gt;, tests::&lt;event_loop&gt;, tmp_path, tmp_path_factory, tmpdir, tmpdir_factory, unused_tcp_port, unused_tcp_port_factory, unused_udp_port, unused_udp_port_factory
&gt;       use 'pytest --fixtures [testpath]' for help on them.

/Users/<USER>/Documents/github/rippr/tests/fixtures.py:120</error></testcase><testcase classname="tests.langchain.unit.test_langchain_tools.TestToolIntegration" name="test_tool_message_consistency" time="0.000"><error message="failed on setup with &quot;file /Users/<USER>/Documents/github/rippr/tests/langchain/unit/test_langchain_tools.py, line 684&#10;      def test_tool_message_consistency(self, mock_codebase):&#10;file /Users/<USER>/Documents/github/rippr/tests/fixtures.py, line 120&#10;  @pytest.fixture&#10;  def mock_codebase(temp_workspace: Path, sample_files: Dict[str, str]) -&gt; Mock:&#10;E       fixture 'temp_workspace' not found&#10;&gt;       available fixtures: _session_event_loop, anyio_backend, anyio_backend_name, anyio_backend_options, cache, capfd, capfdbinary, caplog, capsys, capsysbinary, cov, doctest_namespace, event_loop, event_loop_policy, free_tcp_port, free_tcp_port_factory, free_udp_port, free_udp_port_factory, mock_codebase, mock_env_vars, monkeypatch, no_cover, pytestconfig, record_property, record_testsuite_property, record_xml_attribute, recwarn, sample_files, snapshot, temp_env_vars, tests/langchain/unit/test_langchain_tools.py::&lt;event_loop&gt;, tests/langchain/unit/test_langchain_tools.py::TestToolIntegration::&lt;event_loop&gt;, tests::&lt;event_loop&gt;, tmp_path, tmp_path_factory, tmpdir, tmpdir_factory, unused_tcp_port, unused_tcp_port_factory, unused_udp_port, unused_udp_port_factory&#10;&gt;       use 'pytest --fixtures [testpath]' for help on them.&#10;&#10;/Users/<USER>/Documents/github/rippr/tests/fixtures.py:120&quot;">file /Users/<USER>/Documents/github/rippr/tests/langchain/unit/test_langchain_tools.py, line 684
      def test_tool_message_consistency(self, mock_codebase):
file /Users/<USER>/Documents/github/rippr/tests/fixtures.py, line 120
  @pytest.fixture
  def mock_codebase(temp_workspace: Path, sample_files: Dict[str, str]) -&gt; Mock:
E       fixture 'temp_workspace' not found
&gt;       available fixtures: _session_event_loop, anyio_backend, anyio_backend_name, anyio_backend_options, cache, capfd, capfdbinary, caplog, capsys, capsysbinary, cov, doctest_namespace, event_loop, event_loop_policy, free_tcp_port, free_tcp_port_factory, free_udp_port, free_udp_port_factory, mock_codebase, mock_env_vars, monkeypatch, no_cover, pytestconfig, record_property, record_testsuite_property, record_xml_attribute, recwarn, sample_files, snapshot, temp_env_vars, tests/langchain/unit/test_langchain_tools.py::&lt;event_loop&gt;, tests/langchain/unit/test_langchain_tools.py::TestToolIntegration::&lt;event_loop&gt;, tests::&lt;event_loop&gt;, tmp_path, tmp_path_factory, tmpdir, tmpdir_factory, unused_tcp_port, unused_tcp_port_factory, unused_udp_port, unused_udp_port_factory
&gt;       use 'pytest --fixtures [testpath]' for help on them.

/Users/<USER>/Documents/github/rippr/tests/fixtures.py:120</error></testcase><testcase classname="tests.langchain.unit.test_langchain_tools.TestToolIntegration" name="test_error_handling_consistency" time="0.000"><error message="failed on setup with &quot;file /Users/<USER>/Documents/github/rippr/tests/langchain/unit/test_langchain_tools.py, line 706&#10;      def test_error_handling_consistency(self, mock_codebase):&#10;file /Users/<USER>/Documents/github/rippr/tests/fixtures.py, line 120&#10;  @pytest.fixture&#10;  def mock_codebase(temp_workspace: Path, sample_files: Dict[str, str]) -&gt; Mock:&#10;E       fixture 'temp_workspace' not found&#10;&gt;       available fixtures: _session_event_loop, anyio_backend, anyio_backend_name, anyio_backend_options, cache, capfd, capfdbinary, caplog, capsys, capsysbinary, cov, doctest_namespace, event_loop, event_loop_policy, free_tcp_port, free_tcp_port_factory, free_udp_port, free_udp_port_factory, mock_codebase, mock_env_vars, monkeypatch, no_cover, pytestconfig, record_property, record_testsuite_property, record_xml_attribute, recwarn, sample_files, snapshot, temp_env_vars, tests/langchain/unit/test_langchain_tools.py::&lt;event_loop&gt;, tests/langchain/unit/test_langchain_tools.py::TestToolIntegration::&lt;event_loop&gt;, tests::&lt;event_loop&gt;, tmp_path, tmp_path_factory, tmpdir, tmpdir_factory, unused_tcp_port, unused_tcp_port_factory, unused_udp_port, unused_udp_port_factory&#10;&gt;       use 'pytest --fixtures [testpath]' for help on them.&#10;&#10;/Users/<USER>/Documents/github/rippr/tests/fixtures.py:120&quot;">file /Users/<USER>/Documents/github/rippr/tests/langchain/unit/test_langchain_tools.py, line 706
      def test_error_handling_consistency(self, mock_codebase):
file /Users/<USER>/Documents/github/rippr/tests/fixtures.py, line 120
  @pytest.fixture
  def mock_codebase(temp_workspace: Path, sample_files: Dict[str, str]) -&gt; Mock:
E       fixture 'temp_workspace' not found
&gt;       available fixtures: _session_event_loop, anyio_backend, anyio_backend_name, anyio_backend_options, cache, capfd, capfdbinary, caplog, capsys, capsysbinary, cov, doctest_namespace, event_loop, event_loop_policy, free_tcp_port, free_tcp_port_factory, free_udp_port, free_udp_port_factory, mock_codebase, mock_env_vars, monkeypatch, no_cover, pytestconfig, record_property, record_testsuite_property, record_xml_attribute, recwarn, sample_files, snapshot, temp_env_vars, tests/langchain/unit/test_langchain_tools.py::&lt;event_loop&gt;, tests/langchain/unit/test_langchain_tools.py::TestToolIntegration::&lt;event_loop&gt;, tests::&lt;event_loop&gt;, tmp_path, tmp_path_factory, tmpdir, tmpdir_factory, unused_tcp_port, unused_tcp_port_factory, unused_udp_port, unused_udp_port_factory
&gt;       use 'pytest --fixtures [testpath]' for help on them.

/Users/<USER>/Documents/github/rippr/tests/fixtures.py:120</error></testcase><testcase classname="tests.langchain.unit.test_langchain_tools.TestToolIntegration" name="test_tool_schema_validation" time="0.000"><error message="failed on setup with &quot;file /Users/<USER>/Documents/github/rippr/tests/langchain/unit/test_langchain_tools.py, line 727&#10;      def test_tool_schema_validation(self, mock_codebase):&#10;file /Users/<USER>/Documents/github/rippr/tests/fixtures.py, line 120&#10;  @pytest.fixture&#10;  def mock_codebase(temp_workspace: Path, sample_files: Dict[str, str]) -&gt; Mock:&#10;E       fixture 'temp_workspace' not found&#10;&gt;       available fixtures: _session_event_loop, anyio_backend, anyio_backend_name, anyio_backend_options, cache, capfd, capfdbinary, caplog, capsys, capsysbinary, cov, doctest_namespace, event_loop, event_loop_policy, free_tcp_port, free_tcp_port_factory, free_udp_port, free_udp_port_factory, mock_codebase, mock_env_vars, monkeypatch, no_cover, pytestconfig, record_property, record_testsuite_property, record_xml_attribute, recwarn, sample_files, snapshot, temp_env_vars, tests/langchain/unit/test_langchain_tools.py::&lt;event_loop&gt;, tests/langchain/unit/test_langchain_tools.py::TestToolIntegration::&lt;event_loop&gt;, tests::&lt;event_loop&gt;, tmp_path, tmp_path_factory, tmpdir, tmpdir_factory, unused_tcp_port, unused_tcp_port_factory, unused_udp_port, unused_udp_port_factory&#10;&gt;       use 'pytest --fixtures [testpath]' for help on them.&#10;&#10;/Users/<USER>/Documents/github/rippr/tests/fixtures.py:120&quot;">file /Users/<USER>/Documents/github/rippr/tests/langchain/unit/test_langchain_tools.py, line 727
      def test_tool_schema_validation(self, mock_codebase):
file /Users/<USER>/Documents/github/rippr/tests/fixtures.py, line 120
  @pytest.fixture
  def mock_codebase(temp_workspace: Path, sample_files: Dict[str, str]) -&gt; Mock:
E       fixture 'temp_workspace' not found
&gt;       available fixtures: _session_event_loop, anyio_backend, anyio_backend_name, anyio_backend_options, cache, capfd, capfdbinary, caplog, capsys, capsysbinary, cov, doctest_namespace, event_loop, event_loop_policy, free_tcp_port, free_tcp_port_factory, free_udp_port, free_udp_port_factory, mock_codebase, mock_env_vars, monkeypatch, no_cover, pytestconfig, record_property, record_testsuite_property, record_xml_attribute, recwarn, sample_files, snapshot, temp_env_vars, tests/langchain/unit/test_langchain_tools.py::&lt;event_loop&gt;, tests/langchain/unit/test_langchain_tools.py::TestToolIntegration::&lt;event_loop&gt;, tests::&lt;event_loop&gt;, tmp_path, tmp_path_factory, tmpdir, tmpdir_factory, unused_tcp_port, unused_tcp_port_factory, unused_udp_port, unused_udp_port_factory
&gt;       use 'pytest --fixtures [testpath]' for help on them.

/Users/<USER>/Documents/github/rippr/tests/fixtures.py:120</error></testcase></testsuite></testsuites>