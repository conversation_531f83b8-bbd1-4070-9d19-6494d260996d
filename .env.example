# Rippr API Configuration
# Copy this file to .env and configure your settings

# API Server Configuration
RIPPR_API_HOST=0.0.0.0
RIPPR_API_PORT=8000
RIPPR_API_RELOAD=false
RIPPR_API_WORKERS=1

# Authentication
# Comma-separated list of valid API keys
RIPPR_API_KEYS=your-api-key-1,your-api-key-2,your-api-key-3
# Default API key for development (used if RIPPR_API_KEYS is not set)
RIPPR_DEFAULT_API_KEY=rippr-dev-key-12345
# Disable authentication for development (not recommended for production)
RIPPR_DISABLE_AUTH=false

# CORS Configuration
# Allowed origins for CORS (* allows all origins)
RIPPR_CORS_ORIGINS=*

# Rate Limiting
# Requests per minute per client
RIPPR_RATE_LIMIT=60

# Model Provider API Keys
# OpenAI
OPENAI_API_KEY=your-openai-api-key

# Anthropic
ANTHROPIC_API_KEY=your-anthropic-api-key

# Google/Vertex AI
GOOGLE_APPLICATION_CREDENTIALS=/path/to/service-account.json
GOOGLE_PROJECT_ID=your-google-project-id

# GitHub Copilot
GITHUB_COPILOT_OAUTH_TOKEN=your-github-oauth-token

# XAI
XAI_API_KEY=your-xai-api-key

# Codegen SDK
CODEGEN_API_TOKEN=your-codegen-api-token
CODEGEN_ORG_ID=your-org-id

# Logging
LOG_LEVEL=INFO
