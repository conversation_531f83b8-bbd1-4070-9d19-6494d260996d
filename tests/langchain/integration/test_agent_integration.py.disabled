"""Comprehensive integration tests for LangChain agent and graph functionality."""

import pytest
from unittest.mock import Mock, patch, MagicMock, AsyncMock
from typing import List, Dict, Any, AsyncGenerator
import asyncio

from langchain_core.messages import HumanMessage, AIMessage, ToolMessage
from langchain_core.tools import BaseTool
from langgraph.graph import StateGraph
from langgraph.prebuilt import ToolNode

from src.langchain.agent import create_agent
from src.langchain.graph import create_graph, AgentState
from src.langchain.llm import get_llm
from src.langchain import get_workspace_tools

from tests.fixtures import (
    mock_codebase, mock_llm, mock_env_vars, sample_tool_call
)


class TestAgentIntegration:
    """Test agent integration functionality."""

    def test_agent_creation_with_tools(self, mock_codebase, mock_llm, mock_env_vars):
        """Test agent creation with workspace tools."""
        with patch('src.langchain.agent.create_agent') as mock_create_agent:
            tools = get_workspace_tools(mock_codebase)
            mock_agent = Mock()
            mock_create_agent.return_value = mock_agent
            
            agent = create_agent(mock_llm, tools)
            
            assert agent is not None
            mock_create_agent.assert_called_once_with(mock_llm, tools)

    def test_agent_with_system_message(self, mock_codebase, mock_llm, mock_env_vars):
        """Test agent creation with custom system message."""
        with patch('src.langchain.agent.create_agent') as mock_create_agent:
            tools = get_workspace_tools(mock_codebase)
            system_message = "You are a helpful coding assistant."
            mock_agent = Mock()
            mock_create_agent.return_value = mock_agent
            
            agent = create_agent(mock_llm, tools, system_message=system_message)
            
            assert agent is not None
            mock_create_agent.assert_called_once_with(mock_llm, tools, system_message=system_message)

    @pytest.mark.asyncio
    async def test_agent_invoke_with_tools(self, mock_codebase, mock_llm, mock_env_vars):
        """Test agent invocation with tool calls."""
        with patch('src.langchain.agent.create_agent') as mock_create_agent:
            # Mock agent response with tool calls
            mock_agent = AsyncMock()
            mock_agent.ainvoke.return_value = {
                "messages": [
                    AIMessage(
                        content="I'll help you view that file.",
                        tool_calls=[{
                            "id": "call_123",
                            "name": "view_file",
                            "args": {"filepath": "main.py"}
                        }]
                    )
                ]
            }
            mock_create_agent.return_value = mock_agent
            
            tools = get_workspace_tools(mock_codebase)
            agent = create_agent(mock_llm, tools)
            
            result = await agent.ainvoke({
                "messages": [HumanMessage(content="Show me main.py")]
            })
            
            assert "messages" in result
            assert len(result["messages"]) > 0
            assert result["messages"][0].tool_calls is not None

    @pytest.mark.asyncio
    async def test_agent_error_handling(self, mock_codebase, mock_llm, mock_env_vars):
        """Test agent error handling."""
        with patch('src.langchain.agent.create_agent') as mock_create_agent:
            mock_agent = AsyncMock()
            mock_agent.ainvoke.side_effect = Exception("LLM error")
            mock_create_agent.return_value = mock_agent
            
            tools = get_workspace_tools(mock_codebase)
            agent = create_agent(mock_llm, tools)
            
            with pytest.raises(Exception) as exc_info:
                await agent.ainvoke({
                    "messages": [HumanMessage(content="Test message")]
                })
            
            assert "LLM error" in str(exc_info.value)


class TestGraphIntegration:
    """Test graph integration functionality."""

    def test_graph_creation(self, mock_codebase, mock_llm, mock_env_vars):
        """Test graph creation with agent and tools."""
        with patch('src.langchain.graph.create_graph') as mock_create_graph:
            mock_graph = Mock()
            mock_create_graph.return_value = mock_graph
            
            tools = get_workspace_tools(mock_codebase)
            graph = create_graph(mock_llm, tools)
            
            assert graph is not None
            mock_create_graph.assert_called_once_with(mock_llm, tools)

    def test_graph_state_structure(self):
        """Test AgentState structure."""
        state = AgentState(messages=[])
        
        assert hasattr(state, 'messages')
        assert isinstance(state.messages, list)

    @pytest.mark.asyncio
    async def test_graph_execution_flow(self, mock_codebase, mock_llm, mock_env_vars):
        """Test complete graph execution flow."""
        with patch('src.langchain.graph.create_graph') as mock_create_graph:
            # Mock graph that simulates agent -> tools -> agent flow
            mock_graph = AsyncMock()
            
            # First call: agent decides to use tool
            # Second call: tools execute
            # Third call: agent provides final response
            mock_graph.ainvoke.return_value = {
                "messages": [
                    HumanMessage(content="Show me main.py"),
                    AIMessage(
                        content="I'll view that file for you.",
                        tool_calls=[{
                            "id": "call_123",
                            "name": "view_file", 
                            "args": {"filepath": "main.py"}
                        }]
                    ),
                    ToolMessage(
                        content="File content: def main(): pass",
                        tool_call_id="call_123",
                        name="view_file"
                    ),
                    AIMessage(content="Here's the content of main.py: def main(): pass")
                ]
            }
            mock_create_graph.return_value = mock_graph
            
            tools = get_workspace_tools(mock_codebase)
            graph = create_graph(mock_llm, tools)
            
            result = await graph.ainvoke({
                "messages": [HumanMessage(content="Show me main.py")]
            })
            
            assert "messages" in result
            assert len(result["messages"]) == 4
            assert isinstance(result["messages"][0], HumanMessage)
            assert isinstance(result["messages"][1], AIMessage)
            assert isinstance(result["messages"][2], ToolMessage)
            assert isinstance(result["messages"][3], AIMessage)

    @pytest.mark.asyncio
    async def test_graph_with_multiple_tool_calls(self, mock_codebase, mock_llm, mock_env_vars):
        """Test graph handling multiple tool calls."""
        with patch('src.langchain.graph.create_graph') as mock_create_graph:
            mock_graph = AsyncMock()
            mock_graph.ainvoke.return_value = {
                "messages": [
                    HumanMessage(content="List files and show main.py"),
                    AIMessage(
                        content="I'll list the directory and then show main.py.",
                        tool_calls=[
                            {
                                "id": "call_123",
                                "name": "list_directory",
                                "args": {"dirpath": "./"}
                            },
                            {
                                "id": "call_456", 
                                "name": "view_file",
                                "args": {"filepath": "main.py"}
                            }
                        ]
                    ),
                    ToolMessage(
                        content="Files: main.py, utils.py",
                        tool_call_id="call_123",
                        name="list_directory"
                    ),
                    ToolMessage(
                        content="def main(): pass",
                        tool_call_id="call_456", 
                        name="view_file"
                    ),
                    AIMessage(content="Directory contains main.py and utils.py. Main.py content: def main(): pass")
                ]
            }
            mock_create_graph.return_value = mock_graph
            
            tools = get_workspace_tools(mock_codebase)
            graph = create_graph(mock_llm, tools)
            
            result = await graph.ainvoke({
                "messages": [HumanMessage(content="List files and show main.py")]
            })
            
            assert len(result["messages"]) == 5
            # Check that both tool calls were handled
            tool_messages = [msg for msg in result["messages"] if isinstance(msg, ToolMessage)]
            assert len(tool_messages) == 2

    @pytest.mark.asyncio 
    async def test_graph_streaming(self, mock_codebase, mock_llm, mock_env_vars):
        """Test graph streaming functionality."""
        with patch('src.langchain.graph.create_graph') as mock_create_graph:
            async def mock_astream(input_data):
                """Mock streaming response."""
                yield {"messages": [HumanMessage(content="Show me main.py")]}
                yield {
                    "messages": [
                        AIMessage(
                            content="I'll view that file.",
                            tool_calls=[{"id": "call_123", "name": "view_file", "args": {"filepath": "main.py"}}]
                        )
                    ]
                }
                yield {
                    "messages": [
                        ToolMessage(content="File content", tool_call_id="call_123", name="view_file")
                    ]
                }
                yield {
                    "messages": [AIMessage(content="Here's the file content")]
                }
            
            mock_graph = AsyncMock()
            mock_graph.astream.side_effect = mock_astream
            mock_create_graph.return_value = mock_graph
            
            tools = get_workspace_tools(mock_codebase)
            graph = create_graph(mock_llm, tools)
            
            chunks = []
            async for chunk in graph.astream({
                "messages": [HumanMessage(content="Show me main.py")]
            }):
                chunks.append(chunk)
            
            assert len(chunks) == 4
            assert all("messages" in chunk for chunk in chunks)


class TestLLMIntegration:
    """Test LLM integration functionality."""

    def test_get_llm_openai(self, mock_env_vars):
        """Test OpenAI LLM creation."""
        with patch('src.langchain.llm.get_llm') as mock_get_llm:
            mock_llm = Mock()
            mock_get_llm.return_value = mock_llm
            
            llm = get_llm(provider="openai", model="gpt-4")
            
            assert llm is not None
            mock_get_llm.assert_called_once_with(provider="openai", model="gpt-4")

    def test_get_llm_anthropic(self, mock_env_vars):
        """Test Anthropic LLM creation."""
        with patch('src.langchain.llm.get_llm') as mock_get_llm:
            mock_llm = Mock()
            mock_get_llm.return_value = mock_llm
            
            llm = get_llm(provider="anthropic", model="claude-3-sonnet")
            
            assert llm is not None
            mock_get_llm.assert_called_once_with(provider="anthropic", model="claude-3-sonnet")

    def test_get_llm_invalid_provider(self):
        """Test LLM creation with invalid provider."""
        with patch('src.langchain.llm.get_llm') as mock_get_llm:
            mock_get_llm.side_effect = ValueError("Invalid provider")
            
            with pytest.raises(ValueError) as exc_info:
                get_llm(provider="invalid", model="model")
            
            assert "Invalid provider" in str(exc_info.value)


class TestEndToEndWorkflows:
    """End-to-end workflow tests."""

    @pytest.mark.asyncio
    async def test_file_management_workflow(self, mock_codebase, mock_llm, mock_env_vars):
        """Test complete file management workflow."""
        with patch('src.langchain.graph.create_graph') as mock_create_graph:
            # Simulate: create file -> view file -> edit file -> commit
            mock_graph = AsyncMock()
            mock_graph.ainvoke.return_value = {
                "messages": [
                    HumanMessage(content="Create a new Python file with a hello world function"),
                    AIMessage(
                        content="I'll create a new Python file for you.",
                        tool_calls=[{
                            "id": "call_1",
                            "name": "create_file",
                            "args": {"filepath": "hello.py", "content": "def hello():\n    print('Hello, World!')"}
                        }]
                    ),
                    ToolMessage(
                        content="File created successfully",
                        tool_call_id="call_1",
                        name="create_file"
                    ),
                    AIMessage(
                        content="Let me view the created file to confirm.",
                        tool_calls=[{
                            "id": "call_2", 
                            "name": "view_file",
                            "args": {"filepath": "hello.py"}
                        }]
                    ),
                    ToolMessage(
                        content="def hello():\n    print('Hello, World!')",
                        tool_call_id="call_2",
                        name="view_file"
                    ),
                    AIMessage(content="File created successfully with hello world function!")
                ]
            }
            mock_create_graph.return_value = mock_graph
            
            tools = get_workspace_tools(mock_codebase)
            graph = create_graph(mock_llm, tools)
            
            result = await graph.ainvoke({
                "messages": [HumanMessage(content="Create a new Python file with a hello world function")]
            })
            
            # Verify workflow completion
            assert len(result["messages"]) == 6
            tool_calls = [msg for msg in result["messages"] if isinstance(msg, AIMessage) and hasattr(msg, 'tool_calls') and msg.tool_calls]
            assert len(tool_calls) == 2  # create_file and view_file

    @pytest.mark.asyncio
    async def test_search_and_edit_workflow(self, mock_codebase, mock_llm, mock_env_vars):
        """Test search and edit workflow."""
        with patch('src.langchain.graph.create_graph') as mock_create_graph:
            # Simulate: search for pattern -> edit found file -> commit
            mock_graph = AsyncMock()
            mock_graph.ainvoke.return_value = {
                "messages": [
                    HumanMessage(content="Find all TODO comments and replace them with FIXME"),
                    AIMessage(
                        content="I'll search for TODO comments first.",
                        tool_calls=[{
                            "id": "call_1",
                            "name": "search", 
                            "args": {"query": "TODO", "file_extensions": [".py"]}
                        }]
                    ),
                    ToolMessage(
                        content="Found TODO in main.py:15: # TODO: implement this",
                        tool_call_id="call_1",
                        name="search"
                    ),
                    AIMessage(
                        content="Now I'll replace TODO with FIXME in main.py.",
                        tool_calls=[{
                            "id": "call_2",
                            "name": "replacement_edit",
                            "args": {"filepath": "main.py", "old_str": "TODO", "new_str": "FIXME"}
                        }]
                    ),
                    ToolMessage(
                        content="Replacement made successfully",
                        tool_call_id="call_2", 
                        name="replacement_edit"
                    ),
                    AIMessage(content="Successfully replaced all TODO comments with FIXME!")
                ]
            }
            mock_create_graph.return_value = mock_graph
            
            tools = get_workspace_tools(mock_codebase)
            graph = create_graph(mock_llm, tools)
            
            result = await graph.ainvoke({
                "messages": [HumanMessage(content="Find all TODO comments and replace them with FIXME")]
            })
            
            assert len(result["messages"]) == 6
            # Verify both search and edit tools were used
            ai_messages = [msg for msg in result["messages"] if isinstance(msg, AIMessage)]
            tool_calls_used = []
            for msg in ai_messages:
                if hasattr(msg, 'tool_calls') and msg.tool_calls:
                    tool_calls_used.extend([call["name"] for call in msg.tool_calls])
            
            assert "search" in tool_calls_used
            assert "replacement_edit" in tool_calls_used

    @pytest.mark.asyncio
    async def test_github_integration_workflow(self, mock_codebase, mock_llm, mock_env_vars):
        """Test GitHub integration workflow."""
        with patch('src.langchain.graph.create_graph') as mock_create_graph:
            # Simulate: create PR -> add comment -> view PR
            mock_graph = AsyncMock()
            mock_graph.ainvoke.return_value = {
                "messages": [
                    HumanMessage(content="Create a PR for the recent changes"),
                    AIMessage(
                        content="I'll create a pull request for you.",
                        tool_calls=[{
                            "id": "call_1",
                            "name": "github_create_pr",
                            "args": {
                                "title": "Add new feature",
                                "body": "This PR adds the new feature we discussed",
                                "head_branch": "feature-branch",
                                "base_branch": "main"
                            }
                        }]
                    ),
                    ToolMessage(
                        content="PR #123 created successfully",
                        tool_call_id="call_1",
                        name="github_create_pr"
                    ),
                    AIMessage(content="Pull request #123 has been created successfully!")
                ]
            }
            mock_create_graph.return_value = mock_graph
            
            tools = get_workspace_tools(mock_codebase)
            graph = create_graph(mock_llm, tools)
            
            result = await graph.ainvoke({
                "messages": [HumanMessage(content="Create a PR for the recent changes")]
            })
            
            assert len(result["messages"]) == 4
            # Verify PR creation tool was used
            ai_messages = [msg for msg in result["messages"] if isinstance(msg, AIMessage) and hasattr(msg, 'tool_calls') and msg.tool_calls]
            assert len(ai_messages) == 1
            assert ai_messages[0].tool_calls[0]["name"] == "github_create_pr"

    @pytest.mark.asyncio
    async def test_error_recovery_workflow(self, mock_codebase, mock_llm, mock_env_vars):
        """Test error recovery in workflows."""
        with patch('src.langchain.graph.create_graph') as mock_create_graph:
            # Simulate: tool fails -> agent tries alternative approach
            mock_graph = AsyncMock()
            mock_graph.ainvoke.return_value = {
                "messages": [
                    HumanMessage(content="Show me the nonexistent.py file"),
                    AIMessage(
                        content="I'll try to view that file.",
                        tool_calls=[{
                            "id": "call_1", 
                            "name": "view_file",
                            "args": {"filepath": "nonexistent.py"}
                        }]
                    ),
                    ToolMessage(
                        content="Error: File not found",
                        tool_call_id="call_1",
                        name="view_file"
                    ),
                    AIMessage(
                        content="The file doesn't exist. Let me list the directory to show available files.",
                        tool_calls=[{
                            "id": "call_2",
                            "name": "list_directory", 
                            "args": {"dirpath": "./"}
                        }]
                    ),
                    ToolMessage(
                        content="Files: main.py, utils.py, README.md",
                        tool_call_id="call_2",
                        name="list_directory"
                    ),
                    AIMessage(content="The file 'nonexistent.py' doesn't exist. Available files are: main.py, utils.py, README.md")
                ]
            }
            mock_create_graph.return_value = mock_graph
            
            tools = get_workspace_tools(mock_codebase)
            graph = create_graph(mock_llm, tools)
            
            result = await graph.ainvoke({
                "messages": [HumanMessage(content="Show me the nonexistent.py file")]
            })
            
            assert len(result["messages"]) == 6
            # Verify agent recovered from error by trying alternative tool
            tool_messages = [msg for msg in result["messages"] if isinstance(msg, ToolMessage)]
            assert len(tool_messages) == 2
            assert "Error" in tool_messages[0].content
            assert "Files:" in tool_messages[1].content


class TestPerformanceAndScaling:
    """Test performance and scaling considerations."""

    @pytest.mark.asyncio
    async def test_concurrent_tool_execution(self, mock_codebase, mock_llm, mock_env_vars):
        """Test handling of concurrent tool executions."""
        with patch('src.langchain.graph.create_graph') as mock_create_graph:
            # Simulate multiple independent tool calls
            mock_graph = AsyncMock()
            mock_graph.ainvoke.return_value = {
                "messages": [
                    HumanMessage(content="Show me multiple files"),
                    AIMessage(
                        content="I'll show you several files.",
                        tool_calls=[
                            {"id": "call_1", "name": "view_file", "args": {"filepath": "main.py"}},
                            {"id": "call_2", "name": "view_file", "args": {"filepath": "utils.py"}},
                            {"id": "call_3", "name": "view_file", "args": {"filepath": "README.md"}}
                        ]
                    ),
                    ToolMessage(content="main.py content", tool_call_id="call_1", name="view_file"),
                    ToolMessage(content="utils.py content", tool_call_id="call_2", name="view_file"), 
                    ToolMessage(content="README.md content", tool_call_id="call_3", name="view_file"),
                    AIMessage(content="Here are the contents of all three files...")
                ]
            }
            mock_create_graph.return_value = mock_graph
            
            tools = get_workspace_tools(mock_codebase)
            graph = create_graph(mock_llm, tools)
            
            # Test that multiple tool calls can be handled
            result = await graph.ainvoke({
                "messages": [HumanMessage(content="Show me multiple files")]
            })
            
            tool_messages = [msg for msg in result["messages"] if isinstance(msg, ToolMessage)]
            assert len(tool_messages) == 3

    def test_tool_memory_usage(self, mock_codebase):
        """Test memory efficiency of tool creation."""
        # Create tools multiple times to test memory usage
        for _ in range(10):
            tools = get_workspace_tools(mock_codebase)
            assert len(tools) > 0
        
        # This test mainly ensures no memory leaks in tool creation
        # In a real scenario, you'd use memory profiling tools

    @pytest.mark.asyncio
    async def test_large_conversation_handling(self, mock_codebase, mock_llm, mock_env_vars):
        """Test handling of large conversation histories."""
        with patch('src.langchain.graph.create_graph') as mock_create_graph:
            # Simulate a large conversation with many messages
            large_conversation = []
            for i in range(50):
                large_conversation.append(HumanMessage(content=f"Request {i}"))
                large_conversation.append(AIMessage(content=f"Response {i}"))
            
            mock_graph = AsyncMock()
            mock_graph.ainvoke.return_value = {
                "messages": large_conversation + [
                    AIMessage(content="Final response after long conversation")
                ]
            }
            mock_create_graph.return_value = mock_graph
            
            tools = get_workspace_tools(mock_codebase)
            graph = create_graph(mock_llm, tools)
            
            result = await graph.ainvoke({
                "messages": large_conversation
            })
            
            # Verify the graph can handle large message histories
            assert len(result["messages"]) == 101  # 50*2 + 1 final message