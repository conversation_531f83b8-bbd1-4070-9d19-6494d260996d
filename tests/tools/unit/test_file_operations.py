"""Comprehensive unit tests for file operation tools."""

import pytest
from unittest.mock import Mock, patch
from pathlib import Path

from src.tools.view_file import view_file, ViewFileObservation, add_line_numbers
from src.tools.create_file import create_file, CreateFileObservation
from src.tools.edit_file import edit_file
from src.tools.delete_file import delete_file
from src.tools.rename_file import rename_file
from src.tools.list_directory import list_directory

from tests.fixtures import mock_codebase, sample_files, temp_workspace


class TestViewFile:
    """Test view_file tool functionality."""

    def test_view_file_success(self, mock_codebase):
        """Test successful file viewing."""
        result = view_file(mock_codebase, "main.py")
        
        assert isinstance(result, ViewFileObservation)
        assert result.status == "success"
        assert result.filepath == "main.py"
        assert "def main():" in result.content
        assert result.line_count > 0

    def test_view_file_with_line_numbers(self, mock_codebase):
        """Test file viewing with line numbers."""
        result = view_file(mock_codebase, "utils.py", line_numbers=True)
        
        assert result.status == "success"
        assert "|" in result.content  # Line number separator
        assert "1|" in result.content  # First line number

    def test_view_file_with_range(self, mock_codebase):
        """Test file viewing with line range."""
        result = view_file(mock_codebase, "utils.py", start_line=1, end_line=5)
        
        assert result.status == "success"
        # For small files, pagination fields are only set if file exceeds max_lines
        assert result.line_count is not None
        assert result.filepath == "utils.py"

    def test_view_file_not_found(self, mock_codebase):
        """Test viewing non-existent file."""
        mock_codebase.get_file.side_effect = ValueError("File not found")
        
        result = view_file(mock_codebase, "nonexistent.py")
        
        assert result.status == "error"
        assert "File not found" in result.error
        assert result.filepath == "nonexistent.py"

    def test_view_file_pagination(self, mock_codebase):
        """Test file viewing with pagination."""
        # Create a large file content
        large_content = "\n".join([f"Line {i}" for i in range(1000)])
        mock_file = Mock()
        mock_file.filepath = "large.py"
        mock_file.content = large_content
        # Override the specific mock for this test
        mock_codebase.get_file.side_effect = lambda path: mock_file if path == "large.py" else Mock()
        
        result = view_file(mock_codebase, "large.py", max_lines=100)
        
        assert result.status == "success"
        assert result.has_more is True
        assert result.max_lines_per_page == 100

    def test_add_line_numbers(self):
        """Test add_line_numbers utility function."""
        content = "line 1\nline 2\nline 3"
        numbered = add_line_numbers(content)
        
        assert "1|line 1" in numbered
        assert "2|line 2" in numbered
        assert "3|line 3" in numbered


class TestCreateFile:
    """Test create_file tool functionality."""

    def test_create_file_success(self, mock_codebase):
        """Test successful file creation."""
        mock_codebase.has_file.return_value = False
        
        result = create_file(mock_codebase, "new_file.py", "print('Hello')")
        
        assert isinstance(result, CreateFileObservation)
        assert result.status == "success"
        assert result.filepath == "new_file.py"
        mock_codebase.create_file.assert_called_once_with("new_file.py", content="print('Hello')")
        mock_codebase.commit.assert_called_once()

    def test_create_file_already_exists(self, mock_codebase):
        """Test creating file that already exists."""
        mock_codebase.has_file.return_value = True
        
        result = create_file(mock_codebase, "main.py", "new content")
        
        assert result.status == "error"
        assert "already exists" in result.error
        assert result.filepath == "main.py"

    def test_create_file_max_tokens_exceeded(self, mock_codebase):
        """Test creating file with max tokens exceeded."""
        result = create_file(mock_codebase, "large.py", "content", max_tokens=100)
        
        assert result.status == "error"
        assert "max output tokens" in result.error

    def test_create_file_exception(self, mock_codebase):
        """Test create file with exception."""
        mock_codebase.has_file.return_value = False
        mock_codebase.create_file.side_effect = Exception("Disk full")
        
        result = create_file(mock_codebase, "new_file.py", "content")
        
        assert result.status == "error"
        assert "Failed to create file" in result.error
        assert "Disk full" in result.error


class TestEditFile:
    """Test edit_file tool functionality."""

    def test_edit_file_success(self, mock_codebase):
        """Test successful file editing."""
        # Mock the edit_file function since it's complex
        with patch('src.tools.edit_file.edit_file') as mock_edit:
            mock_result = Mock()
            mock_result.status = "success"
            mock_result.filepath = "main.py"
            mock_edit.return_value = mock_result
            
            result = edit_file(mock_codebase, "main.py", "new content")
            
            assert result.status == "success"
            assert result.filepath == "main.py"

    def test_edit_file_not_found(self, mock_codebase):
        """Test editing non-existent file."""
        with patch('src.tools.edit_file.edit_file') as mock_edit:
            mock_result = Mock()
            mock_result.status = "error"
            mock_result.error = "File not found"
            mock_edit.return_value = mock_result
            
            result = edit_file(mock_codebase, "nonexistent.py", "content")
            
            assert result.status == "error"
            assert "File not found" in result.error


class TestDeleteFile:
    """Test delete_file tool functionality."""

    def test_delete_file_success(self, mock_codebase):
        """Test successful file deletion."""
        mock_codebase.has_file.return_value = True
        
        result = delete_file(mock_codebase, "utils.py")
        
        assert result.status == "success"
        assert result.filepath == "utils.py"
        mock_codebase.delete_file.assert_called_once_with("utils.py")

    def test_delete_file_not_found(self, mock_codebase):
        """Test deleting non-existent file."""
        mock_codebase.has_file.return_value = False
        
        result = delete_file(mock_codebase, "nonexistent.py")
        
        assert result.status == "error"
        assert "does not exist" in result.error

    def test_delete_file_exception(self, mock_codebase):
        """Test delete file with exception."""
        mock_codebase.has_file.return_value = True
        mock_codebase.delete_file.side_effect = Exception("Permission denied")
        
        result = delete_file(mock_codebase, "readonly.py")
        
        assert result.status == "error"
        assert "Failed to delete" in result.error


class TestRenameFile:
    """Test rename_file tool functionality."""

    def test_rename_file_success(self, mock_codebase):
        """Test successful file renaming."""
        mock_codebase.has_file.side_effect = lambda path: path == "old_name.py"
        
        result = rename_file(mock_codebase, "old_name.py", "new_name.py")
        
        assert result.status == "success"
        assert result.old_filepath == "old_name.py"
        assert result.new_filepath == "new_name.py"

    def test_rename_file_source_not_found(self, mock_codebase):
        """Test renaming non-existent file."""
        mock_codebase.has_file.return_value = False
        
        result = rename_file(mock_codebase, "nonexistent.py", "new_name.py")
        
        assert result.status == "error"
        assert "does not exist" in result.error

    def test_rename_file_target_exists(self, mock_codebase):
        """Test renaming to existing file."""
        mock_codebase.has_file.return_value = True
        
        result = rename_file(mock_codebase, "source.py", "target.py")
        
        assert result.status == "error"
        assert "already exists" in result.error


class TestListDirectory:
    """Test list_directory tool functionality."""

    def test_list_directory_success(self, mock_codebase):
        """Test successful directory listing."""
        mock_files = ["main.py", "utils.py", "README.md"]
        mock_codebase.list_files.return_value = mock_files
        
        result = list_directory(mock_codebase, ".")
        
        assert result.status == "success"
        assert result.path == "."
        assert len(result.files) == len(mock_files)
        assert all(file in mock_files for file in result.files)

    def test_list_directory_empty(self, mock_codebase):
        """Test listing empty directory."""
        mock_codebase.list_files.return_value = []
        
        result = list_directory(mock_codebase, "empty_dir")
        
        assert result.status == "success"
        assert result.path == "empty_dir"
        assert len(result.files) == 0

    def test_list_directory_exception(self, mock_codebase):
        """Test list directory with exception."""
        mock_codebase.list_files.side_effect = Exception("Permission denied")
        
        result = list_directory(mock_codebase, "protected_dir")
        
        assert result.status == "error"
        assert "Failed to list directory" in result.error


class TestFileOperationIntegration:
    """Integration tests for file operations."""

    def test_create_view_edit_delete_workflow(self, mock_codebase):
        """Test complete file operation workflow."""
        # Create file
        mock_codebase.has_file.return_value = False
        create_result = create_file(mock_codebase, "workflow_test.py", "initial content")
        assert create_result.status == "success"
        
        # Update mock to reflect file creation
        mock_codebase.has_file.return_value = True
        
        # View file
        view_result = view_file(mock_codebase, "workflow_test.py")
        assert view_result.status == "success"
        
        # Delete file
        delete_result = delete_file(mock_codebase, "workflow_test.py")
        assert delete_result.status == "success"

    def test_error_handling_consistency(self, mock_codebase):
        """Test that all file operations handle errors consistently."""
        # All operations should handle non-existent files gracefully
        mock_codebase.has_file.return_value = False
        mock_codebase.get_file.side_effect = ValueError("File not found")
        
        view_result = view_file(mock_codebase, "missing.py")
        delete_result = delete_file(mock_codebase, "missing.py")
        rename_result = rename_file(mock_codebase, "missing.py", "new.py")
        
        assert view_result.status == "error"
        assert delete_result.status == "error"
        assert rename_result.status == "error"