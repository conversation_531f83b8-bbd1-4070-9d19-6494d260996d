"""Comprehensive unit tests for search and edit operation tools."""

import pytest
from unittest.mock import Mock, patch, MagicMock
from typing import List, Dict, Any

from src.tools.search import search
from src.tools.search_files_by_name import search_files_by_name
from src.tools.semantic_search import semantic_search
from src.tools.replacement_edit import replacement_edit
from src.tools.global_replacement_edit import replacement_edit_global
from src.tools.semantic_edit import semantic_edit
from src.tools.relace_edit import relace_edit

from tests.fixtures import (
    mock_codebase, sample_files, sample_grep_results, 
    sample_semantic_search_results, mock_vector_store, mock_embeddings
)


class TestSearch:
    """Test search tool functionality."""

    def test_search_success(self, mock_codebase, sample_grep_results):
        """Test successful search operation."""
        with patch('src.tools.search.search') as mock_search:
            mock_result = Mock()
            mock_result.status = "success"
            mock_result.query = "def"
            mock_result.results = sample_grep_results
            mock_result.total_matches = len(sample_grep_results)
            mock_search.return_value = mock_result
            
            result = search(mock_codebase, "def")
            
            assert result.status == "success"
            assert result.query == "def"
            assert len(result.results) > 0
            assert result.total_matches == len(sample_grep_results)

    def test_search_no_matches(self, mock_codebase):
        """Test search with no matches."""
        with patch('src.tools.search.search') as mock_search:
            mock_result = Mock()
            mock_result.status = "success"
            mock_result.query = "nonexistent_pattern"
            mock_result.results = []
            mock_result.total_matches = 0
            mock_search.return_value = mock_result
            
            result = search(mock_codebase, "nonexistent_pattern")
            
            assert result.status == "success"
            assert result.query == "nonexistent_pattern"
            assert len(result.results) == 0
            assert result.total_matches == 0

    def test_search_with_file_filter(self, mock_codebase, sample_grep_results):
        """Test search with file type filter."""
        with patch('src.tools.search.search') as mock_search:
            mock_result = Mock()
            mock_result.status = "success"
            mock_result.query = "def"
            mock_result.file_pattern = "*.py"
            mock_result.results = [r for r in sample_grep_results if r["file"].endswith(".py")]
            mock_search.return_value = mock_result
            
            result = search(mock_codebase, "def", file_pattern="*.py")
            
            assert result.status == "success"
            assert all(r["file"].endswith(".py") for r in result.results)

    def test_search_case_sensitive(self, mock_codebase):
        """Test case-sensitive search."""
        with patch('src.tools.search.search') as mock_search:
            mock_result = Mock()
            mock_result.status = "success"
            mock_result.query = "DEF"
            mock_result.case_sensitive = True
            mock_result.results = []
            mock_search.return_value = mock_result
            
            result = search(mock_codebase, "DEF", case_sensitive=True)
            
            assert result.status == "success"
            assert result.case_sensitive is True

    def test_search_exception(self, mock_codebase):
        """Test search with exception."""
        with patch('src.tools.search.search') as mock_search:
            mock_result = Mock()
            mock_result.status = "error"
            mock_result.error = "Search failed"
            mock_search.return_value = mock_result
            
            result = search(mock_codebase, "pattern")
            
            assert result.status == "error"
            assert "Search failed" in result.error


class TestSearchFilesByName:
    """Test search_files_by_name tool functionality."""

    def test_search_files_by_name_success(self, mock_codebase):
        """Test successful file name search."""
        with patch('src.tools.search_files_by_name.search_files_by_name') as mock_search:
            mock_result = Mock()
            mock_result.status = "success"
            mock_result.pattern = "*.py"
            mock_result.files = ["main.py", "utils.py", "tests/test_utils.py"]
            mock_result.total_files = 3
            mock_search.return_value = mock_result
            
            result = search_files_by_name(mock_codebase, "*.py")
            
            assert result.status == "success"
            assert result.pattern == "*.py"
            assert len(result.files) == 3
            assert all(f.endswith(".py") for f in result.files)

    def test_search_files_exact_match(self, mock_codebase):
        """Test exact file name match."""
        with patch('src.tools.search_files_by_name.search_files_by_name') as mock_search:
            mock_result = Mock()
            mock_result.status = "success"
            mock_result.pattern = "main.py"
            mock_result.files = ["main.py"]
            mock_result.total_files = 1
            mock_search.return_value = mock_result
            
            result = search_files_by_name(mock_codebase, "main.py")
            
            assert result.status == "success"
            assert result.files == ["main.py"]

    def test_search_files_no_matches(self, mock_codebase):
        """Test file search with no matches."""
        with patch('src.tools.search_files_by_name.search_files_by_name') as mock_search:
            mock_result = Mock()
            mock_result.status = "success"
            mock_result.pattern = "*.nonexistent"
            mock_result.files = []
            mock_result.total_files = 0
            mock_search.return_value = mock_result
            
            result = search_files_by_name(mock_codebase, "*.nonexistent")
            
            assert result.status == "success"
            assert len(result.files) == 0


class TestSemanticSearch:
    """Test semantic_search tool functionality."""

    def test_semantic_search_success(self, mock_codebase, sample_semantic_search_results):
        """Test successful semantic search."""
        with patch('src.tools.semantic_search.semantic_search') as mock_search:
            mock_result = Mock()
            mock_result.status = "success"
            mock_result.query = "calculator function"
            mock_result.results = sample_semantic_search_results
            mock_result.total_results = len(sample_semantic_search_results)
            mock_search.return_value = mock_result
            
            result = semantic_search(mock_codebase, "calculator function")
            
            assert result.status == "success"
            assert result.query == "calculator function"
            assert len(result.results) > 0
            assert all("score" in r for r in result.results)

    def test_semantic_search_with_limit(self, mock_codebase, sample_semantic_search_results):
        """Test semantic search with result limit."""
        with patch('src.tools.semantic_search.semantic_search') as mock_search:
            limited_results = sample_semantic_search_results[:2]
            mock_result = Mock()
            mock_result.status = "success"
            mock_result.query = "function"
            mock_result.results = limited_results
            mock_result.limit = 2
            mock_search.return_value = mock_result
            
            result = semantic_search(mock_codebase, "function", limit=2)
            
            assert result.status == "success"
            assert len(result.results) == 2

    def test_semantic_search_no_vector_store(self, mock_codebase):
        """Test semantic search without vector store."""
        with patch('src.tools.semantic_search.semantic_search') as mock_search:
            mock_result = Mock()
            mock_result.status = "error"
            mock_result.error = "Vector store not available"
            mock_search.return_value = mock_result
            
            result = semantic_search(mock_codebase, "query")
            
            assert result.status == "error"
            assert "Vector store" in result.error


class TestReplacementEdit:
    """Test replacement_edit tool functionality."""

    def test_replacement_edit_success(self, mock_codebase):
        """Test successful replacement edit."""
        with patch('src.tools.replacement_edit.replacement_edit') as mock_edit:
            mock_result = Mock()
            mock_result.status = "success"
            mock_result.filepath = "main.py"
            mock_result.old_content = "old_text"
            mock_result.new_content = "new_text"
            mock_result.changes_made = 1
            mock_edit.return_value = mock_result
            
            result = replacement_edit(mock_codebase, "main.py", "old_text", "new_text")
            
            assert result.status == "success"
            assert result.filepath == "main.py"
            assert result.changes_made == 1

    def test_replacement_edit_no_matches(self, mock_codebase):
        """Test replacement edit with no matches."""
        with patch('src.tools.replacement_edit.replacement_edit') as mock_edit:
            mock_result = Mock()
            mock_result.status = "success"
            mock_result.filepath = "main.py"
            mock_result.old_content = "nonexistent_text"
            mock_result.new_content = "new_text"
            mock_result.changes_made = 0
            mock_edit.return_value = mock_result
            
            result = replacement_edit(mock_codebase, "main.py", "nonexistent_text", "new_text")
            
            assert result.status == "success"
            assert result.changes_made == 0

    def test_replacement_edit_file_not_found(self, mock_codebase):
        """Test replacement edit on non-existent file."""
        with patch('src.tools.replacement_edit.replacement_edit') as mock_edit:
            mock_result = Mock()
            mock_result.status = "error"
            mock_result.error = "File not found"
            mock_edit.return_value = mock_result
            
            result = replacement_edit(mock_codebase, "nonexistent.py", "old", "new")
            
            assert result.status == "error"
            assert "File not found" in result.error


class TestGlobalReplacementEdit:
    """Test replacement_edit_global tool functionality."""

    def test_global_replacement_edit_success(self, mock_codebase):
        """Test successful global replacement edit."""
        with patch('src.tools.global_replacement_edit.replacement_edit_global') as mock_edit:
            mock_result = Mock()
            mock_result.status = "success"
            mock_result.pattern = "old_pattern"
            mock_result.replacement = "new_pattern"
            mock_result.files_changed = ["main.py", "utils.py"]
            mock_result.total_changes = 5
            mock_edit.return_value = mock_result
            
            result = replacement_edit_global(mock_codebase, "old_pattern", "new_pattern")
            
            assert result.status == "success"
            assert len(result.files_changed) == 2
            assert result.total_changes == 5

    def test_global_replacement_edit_with_file_filter(self, mock_codebase):
        """Test global replacement edit with file filter."""
        with patch('src.tools.global_replacement_edit.replacement_edit_global') as mock_edit:
            mock_result = Mock()
            mock_result.status = "success"
            mock_result.pattern = "old_text"
            mock_result.replacement = "new_text"
            mock_result.file_pattern = "*.py"
            mock_result.files_changed = ["main.py"]
            mock_result.total_changes = 2
            mock_edit.return_value = mock_result
            
            result = replacement_edit_global(mock_codebase, "old_text", "new_text", file_pattern="*.py")
            
            assert result.status == "success"
            assert result.file_pattern == "*.py"

    def test_global_replacement_edit_no_changes(self, mock_codebase):
        """Test global replacement edit with no changes."""
        with patch('src.tools.global_replacement_edit.replacement_edit_global') as mock_edit:
            mock_result = Mock()
            mock_result.status = "success"
            mock_result.pattern = "nonexistent"
            mock_result.replacement = "new"
            mock_result.files_changed = []
            mock_result.total_changes = 0
            mock_edit.return_value = mock_result
            
            result = replacement_edit_global(mock_codebase, "nonexistent", "new")
            
            assert result.status == "success"
            assert len(result.files_changed) == 0
            assert result.total_changes == 0


class TestSemanticEdit:
    """Test semantic_edit tool functionality."""

    def test_semantic_edit_success(self, mock_codebase):
        """Test successful semantic edit."""
        with patch('src.tools.semantic_edit.semantic_edit') as mock_edit:
            mock_result = Mock()
            mock_result.status = "success"
            mock_result.filepath = "main.py"
            mock_result.instruction = "Add docstring"
            mock_result.changes_made = True
            mock_edit.return_value = mock_result
            
            result = semantic_edit(mock_codebase, "main.py", "Add docstring to main function")
            
            assert result.status == "success"
            assert result.filepath == "main.py"
            assert result.changes_made is True

    def test_semantic_edit_no_changes_needed(self, mock_codebase):
        """Test semantic edit when no changes are needed."""
        with patch('src.tools.semantic_edit.semantic_edit') as mock_edit:
            mock_result = Mock()
            mock_result.status = "success"
            mock_result.filepath = "main.py"
            mock_result.instruction = "Add type hints"
            mock_result.changes_made = False
            mock_result.reason = "Type hints already present"
            mock_edit.return_value = mock_result
            
            result = semantic_edit(mock_codebase, "main.py", "Add type hints")
            
            assert result.status == "success"
            assert result.changes_made is False
            assert "already present" in result.reason

    def test_semantic_edit_file_not_found(self, mock_codebase):
        """Test semantic edit on non-existent file."""
        with patch('src.tools.semantic_edit.semantic_edit') as mock_edit:
            mock_result = Mock()
            mock_result.status = "error"
            mock_result.error = "File not found"
            mock_edit.return_value = mock_result
            
            result = semantic_edit(mock_codebase, "nonexistent.py", "Add something")
            
            assert result.status == "error"
            assert "File not found" in result.error


class TestRelaceEdit:
    """Test relace_edit tool functionality."""

    def test_relace_edit_success(self, mock_codebase):
        """Test successful relace edit."""
        with patch('src.tools.relace_edit.relace_edit') as mock_edit:
            mock_result = Mock()
            mock_result.status = "success"
            mock_result.filepath = "main.py"
            mock_result.new_content = "Updated content"
            mock_edit.return_value = mock_result
            
            result = relace_edit(mock_codebase, "main.py", "Updated content")
            
            assert result.status == "success"
            assert result.filepath == "main.py"

    def test_relace_edit_file_not_found(self, mock_codebase):
        """Test relace edit on non-existent file."""
        with patch('src.tools.relace_edit.relace_edit') as mock_edit:
            mock_result = Mock()
            mock_result.status = "error"
            mock_result.error = "File not found"
            mock_edit.return_value = mock_result
            
            result = relace_edit(mock_codebase, "nonexistent.py", "content")
            
            assert result.status == "error"
            assert "File not found" in result.error


class TestSearchEditIntegration:
    """Integration tests for search and edit operations."""

    def test_search_and_replace_workflow(self, mock_codebase, sample_grep_results):
        """Test complete search and replace workflow."""
        # First search for pattern
        with patch('src.tools.search.search') as mock_search:
            mock_search_result = Mock()
            mock_search_result.status = "success"
            mock_search_result.query = "old_function"
            mock_search_result.results = sample_grep_results
            mock_search.return_value = mock_search_result
            
            search_result = search(mock_codebase, "old_function")
            assert search_result.status == "success"
        
        # Then replace in found files
        with patch('src.tools.replacement_edit.replacement_edit') as mock_edit:
            mock_edit_result = Mock()
            mock_edit_result.status = "success"
            mock_edit_result.changes_made = 1
            mock_edit.return_value = mock_edit_result
            
            for result in search_result.results:
                edit_result = replacement_edit(
                    mock_codebase, 
                    result["file"], 
                    "old_function", 
                    "new_function"
                )
                assert edit_result.status == "success"

    def test_semantic_search_and_edit_workflow(self, mock_codebase, sample_semantic_search_results):
        """Test semantic search followed by semantic edit."""
        # Semantic search
        with patch('src.tools.semantic_search.semantic_search') as mock_search:
            mock_search_result = Mock()
            mock_search_result.status = "success"
            mock_search_result.results = sample_semantic_search_results
            mock_search.return_value = mock_search_result
            
            search_result = semantic_search(mock_codebase, "functions needing documentation")
            assert search_result.status == "success"
        
        # Semantic edit based on search results
        with patch('src.tools.semantic_edit.semantic_edit') as mock_edit:
            mock_edit_result = Mock()
            mock_edit_result.status = "success"
            mock_edit_result.changes_made = True
            mock_edit.return_value = mock_edit_result
            
            for result in search_result.results:
                edit_result = semantic_edit(
                    mock_codebase,
                    result["file_path"],
                    "Add comprehensive docstring"
                )
                assert edit_result.status == "success"
                assert edit_result.changes_made is True

    def test_error_propagation_in_workflows(self, mock_codebase):
        """Test error handling in search-edit workflows."""
        # Search fails
        with patch('src.tools.search.search') as mock_search:
            mock_search_result = Mock()
            mock_search_result.status = "error"
            mock_search_result.error = "Search failed"
            mock_search.return_value = mock_search_result
            
            search_result = search(mock_codebase, "pattern")
            assert search_result.status == "error"
        
        # Edit should not proceed if search failed
        # This tests defensive programming in workflows