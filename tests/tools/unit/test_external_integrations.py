"""Comprehensive unit tests for external integration tools (GitHub, Linear)."""

import pytest
from unittest.mock import Mock, patch, MagicMock
from typing import List, Dict, Any

from src.tools.github.create_pr import create_pr
from src.tools.github.create_pr_comment import create_pr_comment
from src.tools.github.create_pr_review_comment import create_pr_review_comment
from src.tools.github.view_pr import view_pr
from src.tools.linear.linear import (
    linear_get_issue_tool,
    linear_get_issue_comments_tool,
    linear_comment_on_issue_tool,
    linear_register_webhook_tool,
)

from tests.fixtures import (
    mock_codebase, mock_github_client, mock_linear_client, mock_env_vars
)


class TestGitHubCreatePR:
    """Test GitHub create_pr tool functionality."""

    def test_create_pr_success(self, mock_codebase, mock_github_client, mock_env_vars):
        """Test successful PR creation."""
        with patch.object(mock_codebase, 'get_diff', return_value="diff --git a/test.py b/test.py\n+new line"), \
             patch.object(mock_codebase, 'git_commit'), \
             patch.object(mock_codebase, 'checkout'), \
             patch.object(mock_codebase, 'create_pr') as mock_create_pr_method:

            # Mock the PR object returned by codebase.create_pr()
            mock_pr = Mock()
            mock_pr.html_url = 'https://github.com/test/repo/pull/123'
            mock_pr.number = 123
            mock_pr.title = 'Add new feature'
            mock_create_pr_method.return_value = mock_pr

            # Mock the codebase attributes
            mock_codebase._op = Mock()
            mock_codebase._op.git_cli = Mock()
            mock_codebase._op.git_cli.active_branch = Mock()
            mock_codebase._op.git_cli.active_branch.name = "feature-branch"
            mock_codebase._op.default_branch = "main"

            result = create_pr(
                mock_codebase,
                title="Add new feature",
                body="This PR adds a new feature"
            )

            assert result.status == "success"
            assert result.number == 123
            assert "github.com" in result.url

    def test_create_pr_with_draft(self, mock_codebase, mock_github_client, mock_env_vars):
        """Test creating draft PR."""
        with patch.object(mock_codebase, 'get_diff', return_value="diff --git a/test.py b/test.py\n+new line"), \
             patch.object(mock_codebase, 'git_commit'), \
             patch.object(mock_codebase, 'checkout'), \
             patch.object(mock_codebase, 'create_pr') as mock_create_pr_method:

            # Mock the PR object returned by codebase.create_pr()
            mock_pr = Mock()
            mock_pr.html_url = 'https://github.com/test/repo/pull/124'
            mock_pr.number = 124
            mock_pr.title = 'WIP: Draft feature'
            mock_create_pr_method.return_value = mock_pr

            # Mock the codebase attributes
            mock_codebase._op = Mock()
            mock_codebase._op.git_cli = Mock()
            mock_codebase._op.git_cli.active_branch = Mock()
            mock_codebase._op.git_cli.active_branch.name = "feature-branch"
            mock_codebase._op.default_branch = "main"

            result = create_pr(
                mock_codebase,
                title="WIP: Draft feature",
                body="Work in progress"
            )

            assert result.status == "success"
            assert result.number == 124

    def test_create_pr_branch_not_found(self, mock_codebase, mock_env_vars):
        """Test PR creation with non-existent branch."""
        with patch.object(mock_codebase, 'get_diff', return_value=""):
            result = create_pr(
                mock_codebase,
                title="Test PR",
                body="Test body"
            )

            assert result.status == "error"
            assert "No changes" in result.error

    def test_create_pr_permission_denied(self, mock_codebase, mock_env_vars):
        """Test PR creation with insufficient permissions."""
        from github import GithubException

        with patch.object(mock_codebase, 'get_diff', return_value="diff --git a/test.py b/test.py\n+new line"), \
             patch.object(mock_codebase, 'git_commit'), \
             patch.object(mock_codebase, 'checkout'), \
             patch.object(mock_codebase, 'create_pr') as mock_create_pr_method:

            # Mock the codebase attributes
            mock_codebase._op = Mock()
            mock_codebase._op.git_cli = Mock()
            mock_codebase._op.git_cli.active_branch = Mock()
            mock_codebase._op.git_cli.active_branch.name = "feature-branch"
            mock_codebase._op.default_branch = "main"

            # Mock a GitHub exception
            mock_create_pr_method.side_effect = GithubException(403, "Forbidden")

            result = create_pr(
                mock_codebase,
                title="Test PR",
                body="Test body"
            )

            assert result.status == "error"
            assert "Failed to create PR" in result.error

    def test_create_pr_missing_token(self, mock_codebase):
        """Test PR creation without GitHub token."""
        with patch.object(mock_codebase, 'get_diff', return_value=""):
            result = create_pr(
                mock_codebase,
                title="Test PR",
                body="Test body"
            )

            assert result.status == "error"
            assert "No changes" in result.error


class TestGitHubCreatePRComment:
    """Test GitHub create_pr_comment tool functionality."""

    def test_create_pr_comment_success(self, mock_codebase, mock_github_client, mock_env_vars):
        """Test successful PR comment creation."""
        with patch.object(mock_codebase, 'create_pr_comment') as mock_create_pr_comment_method:
            mock_create_pr_comment_method.return_value = None  # The method doesn't return anything

            result = create_pr_comment(
                mock_codebase,
                pr_number=123,
                body="This looks good!"
            )

            assert result.status == "success"
            assert result.pr_number == 123
            assert result.body == "This looks good!"

    def test_create_pr_comment_pr_not_found(self, mock_codebase, mock_env_vars):
        """Test commenting on non-existent PR."""
        with patch('tests.tools.unit.test_external_integrations.create_pr_comment') as mock_comment:
            mock_result = Mock()
            mock_result.status = "error"
            mock_result.error = "PR #999 not found"
            mock_comment.return_value = mock_result

            result = create_pr_comment(
                mock_codebase,
                pr_number=999,
                body="Test comment"
            )

            assert result.status == "error"
            assert "not found" in result.error

    def test_create_pr_comment_empty_body(self, mock_codebase, mock_env_vars):
        """Test creating PR comment with empty body."""
        with patch.object(mock_codebase, 'create_pr_comment') as mock_create_pr_comment_method:
            mock_create_pr_comment_method.side_effect = Exception("Comment body cannot be empty")

            result = create_pr_comment(
                mock_codebase,
                pr_number=123,
                body=""
            )

            assert result.status == "error"
            assert "Comment body cannot be empty" in result.error


class TestGitHubCreatePRReviewComment:
    """Test GitHub create_pr_review_comment tool functionality."""

    def test_create_review_comment_success(self, mock_codebase, mock_github_client, mock_env_vars):
        """Test successful PR review comment creation."""
        with patch.object(mock_codebase, 'create_pr_review_comment') as mock_create_pr_review_comment_method:
            mock_create_pr_review_comment_method.return_value = None  # The method doesn't return anything

            result = create_pr_review_comment(
                mock_codebase,
                pr_number=123,
                body="Consider using a more descriptive variable name",
                commit_sha="abc123",
                path="main.py",
                line=42
            )

            assert result.status == "success"
            assert result.pr_number == 123
            assert result.path == "main.py"
            assert result.line == 42
            assert result.body == "Consider using a more descriptive variable name"
            assert result.commit_sha == "abc123"

    def test_create_review_comment_invalid_line(self, mock_codebase, mock_env_vars):
        """Test review comment on invalid line."""
        with patch('src.tools.github.create_pr_review_comment.create_pr_review_comment') as mock_review:
            mock_result = Mock()
            mock_result.status = "error"
            mock_result.error = "Line 999 not found in diff"
            mock_review.return_value = mock_result

            result = create_pr_review_comment(
                mock_codebase,
                pr_number=123,
                body="Invalid line comment",
                commit_sha="abc123",
                path="main.py",
                line=999
            )

            assert result.status == "error"
            assert "not found" in result.error

    def test_create_review_comment_file_not_in_pr(self, mock_codebase, mock_env_vars):
        """Test review comment on file not in PR."""
        with patch.object(mock_codebase, 'create_pr_review_comment') as mock_create_pr_review_comment_method:
            mock_create_pr_review_comment_method.side_effect = Exception("File 'other.py' not modified in this PR")

            result = create_pr_review_comment(
                mock_codebase,
                pr_number=123,
                body="This file wasn't changed",
                commit_sha="abc123",
                path="other.py",
                line=1
            )

            assert result.status == "error"
            assert "not modified" in result.error


class TestGitHubViewPR:
    """Test GitHub view_pr tool functionality."""

    def test_view_pr_success(self, mock_codebase, mock_github_client, mock_env_vars):
        """Test successful PR viewing."""
        with patch.object(mock_codebase, 'get_modified_symbols_in_pr') as mock_get_modified_symbols:
            mock_patch = "diff --git a/main.py b/main.py\n+new line"
            mock_file_commit_sha = {"main.py": "abc123", "utils.py": "def456"}
            mock_modified_symbols = ["function_a", "class_b"]
            mock_get_modified_symbols.return_value = (mock_patch, mock_file_commit_sha, mock_modified_symbols)

            result = view_pr(mock_codebase, pr_id=123)

            assert result.status == "success"
            assert result.pr_id == 123
            assert "main.py" in result.patch
            assert len(result.file_commit_sha) == 2
            assert len(result.modified_symbols) == 2

    def test_view_pr_with_reviews(self, mock_codebase, mock_github_client, mock_env_vars):
        """Test viewing PR with reviews."""
        with patch.object(mock_codebase, 'get_modified_symbols_in_pr') as mock_get_modified_symbols:
            mock_patch = "diff --git a/main.py b/main.py\n+new line"
            mock_file_commit_sha = {"main.py": "abc123"}
            mock_modified_symbols = ["function_a"]
            mock_get_modified_symbols.return_value = (mock_patch, mock_file_commit_sha, mock_modified_symbols)

            result = view_pr(mock_codebase, pr_id=123)

            assert result.status == "success"
            assert result.pr_id == 123
            assert len(result.modified_symbols) == 1

    def test_view_pr_not_found(self, mock_codebase, mock_env_vars):
        """Test viewing non-existent PR."""
        with patch('src.tools.github.view_pr.view_pr') as mock_view:
            mock_result = Mock()
            mock_result.status = "error"
            mock_result.error = "PR #999 not found"
            mock_view.return_value = mock_result

            result = view_pr(mock_codebase, pr_id=999)

            assert result.status == "error"
            assert "not found" in result.error


class TestLinearGetIssue:
    """Test Linear linear_get_issue_tool functionality."""

    def test_get_issue_success(self, mock_codebase, mock_linear_client, mock_env_vars):
        """Test successful issue retrieval."""
        # Mock the client's get_issue method
        mock_issue = Mock()
        mock_issue.dict.return_value = {
            "id": "TEST-123",
            "title": "Fix bug in authentication",
            "description": "Users can't log in",
            "state": "Todo",
            "assignee": "<EMAIL>",
            "priority": "High",
            "labels": ["bug", "authentication"]
        }
        mock_linear_client.get_issue.return_value = mock_issue

        result = linear_get_issue_tool(mock_linear_client, issue_id="TEST-123")

        assert result.status == "success"
        assert result.issue_id == "TEST-123"
        assert result.issue_data["title"] == "Fix bug in authentication"
        assert result.issue_data["state"] == "Todo"

    def test_get_issue_not_found(self, mock_codebase, mock_linear_client, mock_env_vars):
        """Test retrieving non-existent issue."""
        # Mock the client to raise an exception
        mock_linear_client.get_issue.side_effect = Exception("Issue TEST-999 not found")

        result = linear_get_issue_tool(mock_linear_client, issue_id="TEST-999")

        assert result.status == "error"
        assert "not found" in result.error

    def test_get_issue_access_denied(self, mock_codebase, mock_linear_client, mock_env_vars):
        """Test issue access with insufficient permissions."""
        # Mock the client to raise an exception
        mock_linear_client.get_issue.side_effect = Exception("Access denied to issue TEST-123")

        result = linear_get_issue_tool(mock_linear_client, issue_id="TEST-123")

        assert result.status == "error"
        assert "Access denied" in result.error


class TestLinearGetIssueComments:
    """Test Linear linear_get_issue_comments_tool functionality."""

    def test_get_issue_comments_success(self, mock_codebase, mock_linear_client, mock_env_vars):
        """Test successful issue comments retrieval."""
        # Mock the client's get_issue_comments method
        mock_comment1 = Mock()
        mock_comment1.dict.return_value = {
            "id": "comment-1",
            "body": "I think this is related to the auth service",
            "author": "<EMAIL>",
            "created_at": "2023-01-01T10:00:00Z"
        }
        mock_comment2 = Mock()
        mock_comment2.dict.return_value = {
            "id": "comment-2",
            "body": "Fixed in the latest commit",
            "author": "<EMAIL>",
            "created_at": "2023-01-01T11:00:00Z"
        }
        mock_linear_client.get_issue_comments.return_value = [mock_comment1, mock_comment2]

        result = linear_get_issue_comments_tool(mock_linear_client, issue_id="TEST-123")

        assert result.status == "success"
        assert len(result.comments) == 2
        assert result.comments[0]["author"] == "<EMAIL>"

    def test_get_issue_comments_empty(self, mock_codebase, mock_linear_client, mock_env_vars):
        """Test retrieving comments for issue with no comments."""
        # Mock the client to return empty list
        mock_linear_client.get_issue_comments.return_value = []

        result = linear_get_issue_comments_tool(mock_linear_client, issue_id="TEST-124")

        assert result.status == "success"
        assert len(result.comments) == 0

    def test_get_issue_comments_issue_not_found(self, mock_codebase, mock_linear_client, mock_env_vars):
        """Test getting comments for non-existent issue."""
        # Mock the client to raise an exception
        mock_linear_client.get_issue_comments.side_effect = Exception("Issue TEST-999 not found")

        result = linear_get_issue_comments_tool(mock_linear_client, issue_id="TEST-999")

        assert result.status == "error"
        assert "not found" in result.error


class TestLinearCommentOnIssue:
    """Test Linear linear_comment_on_issue_tool functionality."""

    def test_comment_on_issue_success(self, mock_codebase, mock_linear_client, mock_env_vars):
        """Test successful issue commenting."""
        # Mock the client's comment_on_issue method
        mock_comment_data = {
            "id": "comment-456",
            "body": "This has been fixed in commit abc123",
            "author": "<EMAIL>"
        }
        mock_linear_client.comment_on_issue.return_value = mock_comment_data

        result = linear_comment_on_issue_tool(
            mock_linear_client,
            issue_id="TEST-123",
            body="This has been fixed in commit abc123"
        )

        assert result.status == "success"
        assert result.issue_id == "TEST-123"
        assert result.comment["id"] == "comment-456"

    def test_comment_on_issue_empty_comment(self, mock_codebase, mock_linear_client, mock_env_vars):
        """Test commenting with empty body."""
        # Mock the client to raise an exception for empty body
        mock_linear_client.comment_on_issue.side_effect = Exception("Comment body cannot be empty")

        result = linear_comment_on_issue_tool(
            mock_linear_client,
            issue_id="TEST-123",
            body=""
        )

        assert result.status == "error"
        assert "empty" in result.error

    def test_comment_on_closed_issue(self, mock_codebase, mock_linear_client, mock_env_vars):
        """Test commenting on closed issue."""
        # Mock the client's comment_on_issue method
        mock_comment_data = {
            "id": "comment-789",
            "body": "Additional information",
            "author": "<EMAIL>"
        }
        mock_linear_client.comment_on_issue.return_value = mock_comment_data

        result = linear_comment_on_issue_tool(
            mock_linear_client,
            issue_id="TEST-125",
            body="Additional information"
        )

        assert result.status == "success"
        assert result.comment["id"] == "comment-789"


class TestLinearRegisterWebhook:
    """Test Linear linear_register_webhook_tool functionality."""

    def test_register_webhook_success(self, mock_codebase, mock_linear_client, mock_env_vars):
        """Test successful webhook registration."""
        # Mock the client's register_webhook method
        mock_response = {
            "id": "webhook-123",
            "url": "https://api.example.com/webhook",
            "events": ["issue.created", "issue.updated"]
        }
        mock_linear_client.register_webhook.return_value = mock_response

        result = linear_register_webhook_tool(
            mock_linear_client,
            webhook_url="https://api.example.com/webhook",
            team_id="team-123",
            secret="webhook-secret",
            enabled=True,
            resource_types=["issue.created", "issue.updated"]
        )

        assert result.status == "success"
        assert result.response["id"] == "webhook-123"
        assert len(result.response["events"]) == 2

    def test_register_webhook_invalid_url(self, mock_codebase, mock_linear_client, mock_env_vars):
        """Test webhook registration with invalid URL."""
        # Mock the client to raise an exception for invalid URL
        mock_linear_client.register_webhook.side_effect = Exception("Invalid webhook URL")

        result = linear_register_webhook_tool(
            mock_linear_client,
            webhook_url="invalid-url",
            team_id="team-123",
            secret="webhook-secret",
            enabled=True,
            resource_types=["issue.created"]
        )

        assert result.status == "error"
        assert "Invalid" in result.error

    def test_register_webhook_unauthorized(self, mock_codebase, mock_linear_client, mock_env_vars):
        """Test webhook registration without permissions."""
        # Mock the client to raise an exception for insufficient permissions
        mock_linear_client.register_webhook.side_effect = Exception("Insufficient permissions to create webhooks")

        result = linear_register_webhook_tool(
            mock_linear_client,
            webhook_url="https://api.example.com/webhook",
            team_id="team-123",
            secret="webhook-secret",
            enabled=True,
            resource_types=["issue.created"]
        )

        assert result.status == "error"
        assert "permissions" in result.error


class TestExternalIntegrationWorkflows:
    """Integration tests for external tool workflows."""

    def test_github_pr_workflow(self, mock_codebase, mock_github_client, mock_env_vars):
        """Test complete GitHub PR workflow."""
        # Create PR
        with patch.object(mock_codebase, 'get_diff', return_value="diff --git a/test.py b/test.py\n+new line"), \
             patch.object(mock_codebase, 'git_commit'), \
             patch.object(mock_codebase, 'checkout'), \
             patch.object(mock_codebase, 'create_pr') as mock_create_pr_method:

            # Mock the PR object returned by codebase.create_pr()
            mock_pr = Mock()
            mock_pr.html_url = 'https://github.com/test/repo/pull/123'
            mock_pr.number = 123
            mock_pr.title = 'Add feature'
            mock_create_pr_method.return_value = mock_pr

            # Mock the codebase attributes
            mock_codebase._op = Mock()
            mock_codebase._op.git_cli = Mock()
            mock_codebase._op.git_cli.active_branch = Mock()
            mock_codebase._op.git_cli.active_branch.name = "feature-branch"
            mock_codebase._op.default_branch = "main"

            pr_result = create_pr(
                mock_codebase,
                title="Add feature",
                body="New feature implementation"
            )
            assert pr_result.status == "success"

        # Add comment to PR
        with patch.object(mock_codebase, 'create_pr_comment') as mock_create_pr_comment_method:
            mock_create_pr_comment_method.return_value = None

            comment_result = create_pr_comment(
                mock_codebase,
                pr_number=pr_result.number,
                body="Please review this implementation"
            )
            assert comment_result.status == "success"

        # View PR details
        with patch.object(mock_codebase, 'get_modified_symbols_in_pr') as mock_get_modified_symbols:
            mock_patch = "diff --git a/main.py b/main.py\n+new line"
            mock_file_commit_sha = {"main.py": "abc123"}
            mock_modified_symbols = ["function_a"]
            mock_get_modified_symbols.return_value = (mock_patch, mock_file_commit_sha, mock_modified_symbols)

            view_result = view_pr(mock_codebase, pr_id=pr_result.number)
            assert view_result.status == "success"
            assert view_result.pr_id == 123

    def test_linear_issue_workflow(self, mock_codebase, mock_linear_client, mock_env_vars):
        """Test complete Linear issue workflow."""
        # Get issue details
        mock_issue = Mock()
        mock_issue.dict.return_value = {
            "id": "TEST-123",
            "title": "Fix bug",
            "state": "Todo"
        }
        mock_linear_client.get_issue.return_value = mock_issue

        issue_result = linear_get_issue_tool(mock_linear_client, issue_id="TEST-123")
        assert issue_result.status == "success"

        # Get existing comments
        mock_linear_client.get_issue_comments.return_value = []

        comments_result = linear_get_issue_comments_tool(mock_linear_client, issue_id="TEST-123")
        assert comments_result.status == "success"

        # Add new comment
        mock_comment_data = {
            "id": "comment-789",
            "body": "Working on this issue",
            "author": "<EMAIL>"
        }
        mock_linear_client.comment_on_issue.return_value = mock_comment_data

        comment_result = linear_comment_on_issue_tool(
            mock_linear_client,
            issue_id="TEST-123",
            body="Working on this issue"
        )
        assert comment_result.status == "success"

    def test_error_handling_consistency(self, mock_codebase, mock_linear_client):
        """Test consistent error handling across external tools."""
        # Test GitHub create_pr error handling
        with patch.object(mock_codebase, 'get_diff', return_value=""):
            result = create_pr(mock_codebase, "title", "body")
            assert result.status == "error"
            assert "no changes" in result.error.lower()

        # Test GitHub view_pr error handling
        with patch.object(mock_codebase, 'get_modified_symbols_in_pr') as mock_get_modified_symbols:
            mock_get_modified_symbols.side_effect = Exception("PR not found")
            result = view_pr(mock_codebase, pr_id=999)
            assert result.status == "error"
            assert "failed to view pr" in result.error.lower()

        # Test Linear get_issue error handling
        mock_linear_client.get_issue.side_effect = Exception("Issue not found")
        result = linear_get_issue_tool(mock_linear_client, issue_id="INVALID")
        assert result.status == "error"
        assert "failed to get issue" in result.error.lower()