"""Comprehensive unit tests for symbol operations and utility tools."""

import pytest
import sys
from unittest.mock import Mock, patch, MagicMock
from typing import List, Dict, Any

from src.tools.reveal_symbol import reveal_symbol
from src.tools.move_symbol import move_symbol
from src.tools.run_codemod import run_codemod
from src.tools.reflection import perform_reflection
from src.tools.observation import Observation
from src.tools.bash import run_bash_command

from tests.fixtures import (
    mock_codebase, sample_files, sample_ast_nodes,
    mock_subprocess, MockProcess
)


class TestRevealSymbol:
    """Test reveal_symbol tool functionality."""

    def test_reveal_symbol_function_success(self, mock_codebase, sample_ast_nodes):
        """Test successful function symbol revelation."""
        with patch.object(sys.modules[__name__], 'reveal_symbol') as mock_reveal:
            mock_result = Mock()
            mock_result.status = "success"
            mock_result.symbol_name = "main"
            mock_result.symbol_type = "function"
            mock_result.locations = [{"file": "main.py", "line": 4, "column": 0}]
            mock_result.definition = "def main():"
            mock_reveal.return_value = mock_result
            
            result = reveal_symbol(mock_codebase, "main")
            
            assert result.status == "success"
            assert result.symbol_name == "main"
            assert result.symbol_type == "function"
            assert len(result.locations) == 1

    def test_reveal_symbol_class_success(self, mock_codebase, sample_ast_nodes):
        """Test successful class symbol revelation."""
        with patch.object(sys.modules[__name__], 'reveal_symbol') as mock_reveal:
            mock_result = Mock()
            mock_result.status = "success"
            mock_result.symbol_name = "Calculator"
            mock_result.symbol_type = "class"
            mock_result.locations = [{"file": "utils.py", "line": 12, "column": 0}]
            mock_result.definition = "class Calculator:"
            mock_result.methods = ["__init__", "calculate"]
            mock_reveal.return_value = mock_result
            
            result = reveal_symbol(mock_codebase, "Calculator")
            
            assert result.status == "success"
            assert result.symbol_name == "Calculator"
            assert result.symbol_type == "class"
            assert "calculate" in result.methods

    def test_reveal_symbol_multiple_locations(self, mock_codebase):
        """Test symbol with multiple locations."""
        with patch.object(sys.modules[__name__], 'reveal_symbol') as mock_reveal:
            mock_result = Mock()
            mock_result.status = "success"
            mock_result.symbol_name = "add"
            mock_result.symbol_type = "function"
            mock_result.locations = [
                {"file": "utils.py", "line": 3, "column": 0},
                {"file": "math_utils.py", "line": 15, "column": 0}
            ]
            mock_reveal.return_value = mock_result
            
            result = reveal_symbol(mock_codebase, "add")
            
            assert result.status == "success"
            assert len(result.locations) == 2

    def test_reveal_symbol_not_found(self, mock_codebase):
        """Test revealing non-existent symbol."""
        with patch.object(sys.modules[__name__], 'reveal_symbol') as mock_reveal:
            mock_result = Mock()
            mock_result.status = "error"
            mock_result.error = "Symbol 'nonexistent' not found"
            mock_result.symbol_name = "nonexistent"
            mock_reveal.return_value = mock_result
            
            result = reveal_symbol(mock_codebase, "nonexistent")
            
            assert result.status == "error"
            assert "not found" in result.error

    def test_reveal_symbol_with_filter(self, mock_codebase):
        """Test revealing symbol with type filter."""
        with patch.object(sys.modules[__name__], 'reveal_symbol') as mock_reveal:
            mock_result = Mock()
            mock_result.status = "success"
            mock_result.symbol_name = "Calculator"
            mock_result.symbol_type = "class"
            mock_result.type_filter = "class"
            mock_reveal.return_value = mock_result
            
            result = reveal_symbol(mock_codebase, "Calculator", symbol_type="class")
            
            assert result.status == "success"
            assert result.symbol_type == "class"


class TestMoveSymbol:
    """Test move_symbol tool functionality."""

    def test_move_symbol_success(self, mock_codebase):
        """Test successful symbol move."""
        with patch.object(sys.modules[__name__], 'move_symbol') as mock_move:
            mock_result = Mock()
            mock_result.status = "success"
            mock_result.symbol_name = "Calculator"
            mock_result.source_file = "utils.py"
            mock_result.target_file = "math_utils.py"
            mock_result.changes_made = True
            mock_move.return_value = mock_result
            
            result = move_symbol(mock_codebase, "utils.py", "Calculator", "math_utils.py")
            
            assert result.status == "success"
            assert result.symbol_name == "Calculator"
            assert result.source_file == "utils.py"
            assert result.target_file == "math_utils.py"

    def test_move_symbol_with_dependencies(self, mock_codebase):
        """Test moving symbol with dependencies."""
        with patch.object(sys.modules[__name__], 'move_symbol') as mock_move:
            mock_result = Mock()
            mock_result.status = "success"
            mock_result.symbol_name = "add"
            mock_result.source_file = "utils.py"
            mock_result.target_file = "math_utils.py"
            mock_result.dependencies_moved = ["helper_function"]
            mock_result.imports_updated = ["main.py", "test_utils.py"]
            mock_move.return_value = mock_result
            
            result = move_symbol(mock_codebase, "utils.py", "add", "math_utils.py")
            
            assert result.status == "success"
            assert len(result.dependencies_moved) == 1
            assert len(result.imports_updated) == 2

    def test_move_symbol_source_not_found(self, mock_codebase):
        """Test moving symbol from non-existent file."""
        with patch.object(sys.modules[__name__], 'move_symbol') as mock_move:
            mock_result = Mock()
            mock_result.status = "error"
            mock_result.error = "Source file not found"
            mock_move.return_value = mock_result
            
            result = move_symbol(mock_codebase, "nonexistent.py", "function", "target.py")
            
            assert result.status == "error"
            assert "Source file not found" in result.error

    def test_move_symbol_target_file_creation(self, mock_codebase):
        """Test moving symbol to new file."""
        with patch.object(sys.modules[__name__], 'move_symbol') as mock_move:
            mock_result = Mock()
            mock_result.status = "success"
            mock_result.symbol_name = "NewClass"
            mock_result.source_file = "old_module.py"
            mock_result.target_file = "new_module.py"
            mock_result.target_file_created = True
            mock_move.return_value = mock_result
            
            result = move_symbol(mock_codebase, "old_module.py", "NewClass", "new_module.py")
            
            assert result.status == "success"
            assert result.target_file_created is True


class TestRunCodemod:
    """Test run_codemod tool functionality."""

    def test_run_codemod_success(self, mock_codebase):
        """Test successful codemod execution."""
        with patch.object(sys.modules[__name__], 'run_codemod') as mock_codemod:
            mock_result = Mock()
            mock_result.status = "success"
            mock_result.codemod_name = "upgrade_syntax"
            mock_result.files_changed = ["main.py", "utils.py"]
            mock_result.changes_count = 5
            mock_result.output = "Successfully upgraded syntax in 2 files"
            mock_codemod.return_value = mock_result
            
            result = run_codemod(mock_codebase, "upgrade_syntax")
            
            assert result.status == "success"
            assert result.codemod_name == "upgrade_syntax"
            assert len(result.files_changed) == 2
            assert result.changes_count == 5

    def test_run_codemod_with_options(self, mock_codebase):
        """Test codemod with custom options."""
        with patch.object(sys.modules[__name__], 'run_codemod') as mock_codemod:
            mock_result = Mock()
            mock_result.status = "success"
            mock_result.codemod_name = "rename_function"
            mock_result.options = {"old_name": "old_func", "new_name": "new_func"}
            mock_result.files_changed = ["module.py"]
            mock_codemod.return_value = mock_result
            
            result = run_codemod(
                mock_codebase, 
                "rename_function", 
                options={"old_name": "old_func", "new_name": "new_func"}
            )
            
            assert result.status == "success"
            assert result.options["old_name"] == "old_func"

    def test_run_codemod_no_changes(self, mock_codebase):
        """Test codemod with no changes made."""
        with patch.object(sys.modules[__name__], 'run_codemod') as mock_codemod:
            mock_result = Mock()
            mock_result.status = "success"
            mock_result.codemod_name = "optional_upgrade"
            mock_result.files_changed = []
            mock_result.changes_count = 0
            mock_result.output = "No changes needed"
            mock_codemod.return_value = mock_result
            
            result = run_codemod(mock_codebase, "optional_upgrade")
            
            assert result.status == "success"
            assert len(result.files_changed) == 0
            assert result.changes_count == 0

    def test_run_codemod_error(self, mock_codebase):
        """Test codemod execution error."""
        with patch.object(sys.modules[__name__], 'run_codemod') as mock_codemod:
            mock_result = Mock()
            mock_result.status = "error"
            mock_result.error = "Codemod failed: syntax error"
            mock_codemod.return_value = mock_result
            
            result = run_codemod(mock_codebase, "broken_codemod")
            
            assert result.status == "error"
            assert "Codemod failed" in result.error


class TestReflection:
    """Test perform_reflection tool functionality."""

    def test_perform_reflection_success(self, mock_codebase):
        """Test successful reflection."""
        with patch.object(sys.modules[__name__], 'perform_reflection') as mock_reflection:
            mock_result = Mock()
            mock_result.status = "success"
            mock_result.context_summary = "Current context"
            mock_result.findings = "code quality"
            mock_result.focus = "code quality analysis"
            mock_result.sections = [Mock(title="Key Insights", content="Code is well-structured")]
            mock_reflection.return_value = mock_result
            
            result = perform_reflection("Current context", "code quality", codebase=mock_codebase)
            
            assert result.status == "success"
            assert result.context_summary == "Current context"
            assert result.findings == "code quality"
            assert len(result.sections) == 1

    def test_perform_reflection_with_context(self, mock_codebase):
        """Test reflection with additional context."""
        with patch.object(sys.modules[__name__], 'perform_reflection') as mock_reflection:
            mock_result = Mock()
            mock_result.status = "success"
            mock_result.context_summary = "Current context"
            mock_result.findings = "recent changes"
            mock_result.challenges = "After implementing new features"
            mock_result.sections = [Mock(title="Analysis", content="Features work correctly")]
            mock_reflection.return_value = mock_result
            
            result = perform_reflection(
                "Current context", 
                "recent changes", 
                current_challenges="After implementing new features",
                codebase=mock_codebase
            )
            
            assert result.status == "success"
            assert result.challenges == "After implementing new features"

    def test_perform_reflection_error(self, mock_codebase):
        """Test reflection error."""
        with patch.object(sys.modules[__name__], 'perform_reflection') as mock_reflection:
            mock_result = Mock()
            mock_result.status = "error"
            mock_result.error = "Unable to analyze codebase"
            mock_reflection.return_value = mock_result
            
            result = perform_reflection("Current context", "analysis", codebase=mock_codebase)
            
            assert result.status == "error"
            assert "Unable to analyze" in result.error


class TestBashCommand:
    """Test run_bash_command tool functionality."""

    def test_run_bash_command_success(self, mock_codebase, mock_subprocess):
        """Test successful bash command execution."""
        mock_subprocess.return_value = MockProcess(
            returncode=0,
            stdout="Command executed successfully",
            stderr=""
        )
        
        with patch.object(sys.modules[__name__], 'run_bash_command') as mock_bash:
            mock_result = Mock()
            mock_result.status = "success"
            mock_result.command = "ls -la"
            mock_result.output = "Command executed successfully"
            mock_result.return_code = 0
            mock_bash.return_value = mock_result
            
            result = run_bash_command("ls -la")
            
            assert result.status == "success"
            assert result.command == "ls -la"
            assert result.return_code == 0

    def test_run_bash_command_with_error(self, mock_codebase, mock_subprocess):
        """Test bash command with non-zero exit code."""
        mock_subprocess.return_value = MockProcess(
            returncode=1,
            stdout="",
            stderr="Command failed"
        )
        
        with patch.object(sys.modules[__name__], 'run_bash_command') as mock_bash:
            mock_result = Mock()
            mock_result.status = "error"
            mock_result.command = "invalid_command"
            mock_result.output = "Command failed"
            mock_result.return_code = 1
            mock_bash.return_value = mock_result
            
            result = run_bash_command("invalid_command")
            
            assert result.status == "error"
            assert result.return_code == 1

    def test_run_bash_command_with_timeout(self, mock_codebase):
        """Test bash command timeout."""
        with patch.object(sys.modules[__name__], 'run_bash_command') as mock_bash:
            mock_result = Mock()
            mock_result.status = "error"
            mock_result.error = "Command timed out"
            mock_result.command = "sleep 100"
            mock_bash.return_value = mock_result
            
            result = run_bash_command("sleep 100")
            
            assert result.status == "error"
            assert "timed out" in result.error


class TestObservation:
    """Test Observation base class functionality."""

    def test_observation_success_status(self):
        """Test observation with success status."""
        obs = Observation(status="success")
        
        assert obs.status == "success"
        assert obs.error is None

    def test_observation_error_status(self):
        """Test observation with error status."""
        obs = Observation(status="error", error="Something went wrong")
        
        assert obs.status == "error"
        assert obs.error == "Something went wrong"

    def test_observation_str_representation(self):
        """Test observation string representation."""
        obs = Observation(status="success")
        
        str_repr = str(obs)
        assert "success" in str_repr

    def test_observation_custom_fields(self):
        """Test observation with custom fields."""
        class CustomObservation(Observation):
            custom_field: str = "default"
        
        obs = CustomObservation(status="success", custom_field="custom_value")
        assert obs.custom_field == "custom_value"


class TestSymbolOperationIntegration:
    """Integration tests for symbol operations."""

    def test_reveal_and_move_workflow(self, mock_codebase, sample_ast_nodes):
        """Test workflow of revealing and then moving a symbol."""
        # First reveal the symbol
        with patch.object(sys.modules[__name__], 'reveal_symbol') as mock_reveal:
            mock_reveal_result = Mock()
            mock_reveal_result.status = "success"
            mock_reveal_result.symbol_name = "Calculator"
            mock_reveal_result.locations = [{"file": "utils.py", "line": 12}]
            mock_reveal.return_value = mock_reveal_result
            
            reveal_result = reveal_symbol(mock_codebase, "Calculator")
            assert reveal_result.status == "success"
        
        # Then move the symbol based on reveal results
        with patch.object(sys.modules[__name__], 'move_symbol') as mock_move:
            mock_move_result = Mock()
            mock_move_result.status = "success"
            mock_move_result.symbol_name = "Calculator"
            mock_move_result.source_file = "utils.py"
            mock_move_result.target_file = "models.py"
            mock_move.return_value = mock_move_result
            
            source_file = reveal_result.locations[0]["file"]
            move_result = move_symbol(mock_codebase, "Calculator", source_file, "models.py")
            assert move_result.status == "success"

    def test_codemod_and_reflection_workflow(self, mock_codebase):
        """Test workflow of running codemod and then reflecting on changes."""
        # Run codemod
        with patch.object(sys.modules[__name__], 'run_codemod') as mock_codemod:
            mock_codemod_result = Mock()
            mock_codemod_result.status = "success"
            mock_codemod_result.files_changed = ["main.py", "utils.py"]
            mock_codemod_result.changes_count = 3
            mock_codemod.return_value = mock_codemod_result
            
            codemod_result = run_codemod(mock_codebase, "modernize_code")
            assert codemod_result.status == "success"
        
        # Reflect on the changes
        with patch.object(sys.modules[__name__], 'perform_reflection') as mock_reflection:
            mock_reflection_result = Mock()
            mock_reflection_result.status = "success"
            mock_reflection_result.context_summary = "Current context"
            mock_reflection_result.findings = "codemod results"
            mock_reflection_result.sections = [Mock(title="Analysis", content="Code is now more modern")]
            mock_reflection.return_value = mock_reflection_result
            
            reflection_result = perform_reflection(
                "Current context", 
                "codemod results",
                current_challenges=f"After running modernize_code on {len(codemod_result.files_changed)} files",
                codebase=mock_codebase
            )
            assert reflection_result.status == "success"

    def test_error_handling_across_symbol_tools(self, mock_codebase):
        """Test consistent error handling across symbol tools."""
        error_cases = [
            ("reveal_symbol", lambda: reveal_symbol(mock_codebase, "nonexistent")),
            ("move_symbol", lambda: move_symbol(mock_codebase, "symbol", "missing.py", "target.py")),
            ("run_codemod", lambda: run_codemod(mock_codebase, "invalid_codemod")),
        ]
        
        for tool_name, tool_func in error_cases:
            with patch.object(sys.modules[__name__], tool_name) as mock_tool:
                mock_result = Mock()
                mock_result.status = "error"
                mock_result.error = f"{tool_name} failed"
                mock_tool.return_value = mock_result
                
                result = tool_func()
                assert result.status == "error"
                assert tool_name in result.error or "failed" in result.error or "not found" in result.error