"""Comprehensive unit tests for git operations and commit functionality."""

import pytest
from unittest.mock import Mock, patch, MagicMock
from typing import List, Dict, Any

from src.tools.commit import commit
from src.tools.observation import Observation

from tests.fixtures import mock_codebase, mock_subprocess, MockProcess


class TestCommit:
    """Test commit tool functionality."""

    def test_commit_success(self, mock_codebase):
        """Test successful commit operation."""
        with patch('src.tools.commit.commit') as mock_commit_func:
            mock_result = Mock()
            mock_result.status = "success"
            mock_result.message = "Add new feature"
            mock_result.commit_hash = "abc123def456"
            mock_result.files_changed = ["main.py", "utils.py"]
            mock_result.insertions = 25
            mock_result.deletions = 5
            mock_commit_func.return_value = mock_result
            
            result = commit(mock_codebase, "Add new feature")
            
            assert result.status == "success"
            assert result.message == "Add new feature"
            assert result.commit_hash == "abc123def456"
            assert len(result.files_changed) == 2

    def test_commit_with_empty_message(self, mock_codebase):
        """Test commit with empty message."""
        with patch('src.tools.commit.commit') as mock_commit_func:
            mock_result = Mock()
            mock_result.status = "error"
            mock_result.error = "Commit message cannot be empty"
            mock_commit_func.return_value = mock_result
            
            result = commit(mock_codebase, "")
            
            assert result.status == "error"
            assert "empty" in result.error

    def test_commit_no_changes(self, mock_codebase):
        """Test commit when no changes are staged."""
        with patch('src.tools.commit.commit') as mock_commit_func:
            mock_result = Mock()
            mock_result.status = "error"
            mock_result.error = "No changes to commit"
            mock_result.message = "No changes found"
            mock_commit_func.return_value = mock_result
            
            result = commit(mock_codebase, "Try to commit nothing")
            
            assert result.status == "error"
            assert "No changes" in result.error

    def test_commit_with_staged_files(self, mock_codebase):
        """Test commit with specific staged files."""
        with patch('src.tools.commit.commit') as mock_commit_func:
            mock_result = Mock()
            mock_result.status = "success"
            mock_result.message = "Update specific files"
            mock_result.commit_hash = "def789ghi012"
            mock_result.files_changed = ["config.py"]
            mock_result.insertions = 10
            mock_result.deletions = 2
            mock_commit_func.return_value = mock_result
            
            result = commit(mock_codebase, "Update specific files", files=["config.py"])
            
            assert result.status == "success"
            assert "config.py" in result.files_changed

    def test_commit_with_author_override(self, mock_codebase):
        """Test commit with custom author."""
        with patch('src.tools.commit.commit') as mock_commit_func:
            mock_result = Mock()
            mock_result.status = "success"
            mock_result.message = "Fix bug"
            mock_result.author = "Bot <<EMAIL>>"
            mock_result.commit_hash = "ghi345jkl678"
            mock_commit_func.return_value = mock_result
            
            result = commit(
                mock_codebase, 
                "Fix bug", 
                author="Bot <<EMAIL>>"
            )
            
            assert result.status == "success"
            assert result.author == "Bot <<EMAIL>>"

    def test_commit_merge_conflict(self, mock_codebase):
        """Test commit during merge conflict."""
        with patch('src.tools.commit.commit') as mock_commit_func:
            mock_result = Mock()
            mock_result.status = "error"
            mock_result.error = "Cannot commit: merge conflict detected in main.py"
            mock_commit_func.return_value = mock_result
            
            result = commit(mock_codebase, "Resolve conflicts")
            
            assert result.status == "error"
            assert "merge conflict" in result.error

    def test_commit_detached_head(self, mock_codebase):
        """Test commit in detached HEAD state."""
        with patch('src.tools.commit.commit') as mock_commit_func:
            mock_result = Mock()
            mock_result.status = "warning"
            mock_result.message = "Emergency fix"
            mock_result.commit_hash = "jkl901mno234"
            mock_result.warning = "Commit created in detached HEAD state"
            mock_commit_func.return_value = mock_result
            
            result = commit(mock_codebase, "Emergency fix")
            
            assert result.status == "warning"
            assert "detached HEAD" in result.warning

    def test_commit_with_hooks_failure(self, mock_codebase):
        """Test commit when git hooks fail."""
        with patch('src.tools.commit.commit') as mock_commit_func:
            mock_result = Mock()
            mock_result.status = "error"
            mock_result.error = "Pre-commit hook failed: linting errors found"
            mock_commit_func.return_value = mock_result
            
            result = commit(mock_codebase, "Add feature with lint errors")
            
            assert result.status == "error"
            assert "hook failed" in result.error

    def test_commit_large_changeset(self, mock_codebase):
        """Test commit with large number of changes."""
        with patch('src.tools.commit.commit') as mock_commit_func:
            large_files = [f"file_{i}.py" for i in range(100)]
            mock_result = Mock()
            mock_result.status = "success"
            mock_result.message = "Massive refactor"
            mock_result.commit_hash = "pqr567stu890"
            mock_result.files_changed = large_files
            mock_result.insertions = 5000
            mock_result.deletions = 2000
            mock_commit_func.return_value = mock_result
            
            result = commit(mock_codebase, "Massive refactor")
            
            assert result.status == "success"
            assert len(result.files_changed) == 100
            assert result.insertions == 5000

    def test_commit_amend(self, mock_codebase):
        """Test amending the last commit."""
        with patch('src.tools.commit.commit') as mock_commit_func:
            mock_result = Mock()
            mock_result.status = "success"
            mock_result.message = "Fix typo in documentation"
            mock_result.commit_hash = "vwx123yzab45"
            mock_result.amended = True
            mock_result.original_hash = "stu890vwx123"
            mock_commit_func.return_value = mock_result
            
            result = commit(
                mock_codebase, 
                "Fix typo in documentation", 
                amend=True
            )
            
            assert result.status == "success"
            assert result.amended is True
            assert result.original_hash == "stu890vwx123"

    def test_commit_gpg_signing(self, mock_codebase):
        """Test commit with GPG signing."""
        with patch('src.tools.commit.commit') as mock_commit_func:
            mock_result = Mock()
            mock_result.status = "success"
            mock_result.message = "Signed commit"
            mock_result.commit_hash = "cde678fgh901"
            mock_result.signed = True
            mock_result.signature = "GPG signature verified"
            mock_commit_func.return_value = mock_result
            
            result = commit(
                mock_codebase, 
                "Signed commit", 
                sign=True
            )
            
            assert result.status == "success"
            assert result.signed is True
            assert "verified" in result.signature


class TestGitStatus:
    """Test git status related functionality."""

    def test_git_status_clean(self, mock_codebase):
        """Test git status when working directory is clean."""
        with patch('src.tools.commit.get_git_status') as mock_status:
            mock_result = Mock()
            mock_result.status = "success"
            mock_result.clean = True
            mock_result.staged_files = []
            mock_result.modified_files = []
            mock_result.untracked_files = []
            mock_status.return_value = mock_result
            
            result = mock_status(mock_codebase)
            
            assert result.status == "success"
            assert result.clean is True
            assert len(result.staged_files) == 0

    def test_git_status_with_changes(self, mock_codebase):
        """Test git status with various types of changes."""
        with patch('src.tools.commit.get_git_status') as mock_status:
            mock_result = Mock()
            mock_result.status = "success"
            mock_result.clean = False
            mock_result.staged_files = ["main.py", "utils.py"]
            mock_result.modified_files = ["config.py"]
            mock_result.untracked_files = ["new_file.py"]
            mock_result.deleted_files = ["old_file.py"]
            mock_status.return_value = mock_result
            
            result = mock_status(mock_codebase)
            
            assert result.status == "success"
            assert result.clean is False
            assert len(result.staged_files) == 2
            assert len(result.modified_files) == 1
            assert len(result.untracked_files) == 1

    def test_git_status_merge_in_progress(self, mock_codebase):
        """Test git status during merge."""
        with patch('src.tools.commit.get_git_status') as mock_status:
            mock_result = Mock()
            mock_result.status = "success"
            mock_result.merge_in_progress = True
            mock_result.conflicted_files = ["conflicted.py"]
            mock_status.return_value = mock_result
            
            result = mock_status(mock_codebase)
            
            assert result.status == "success"
            assert result.merge_in_progress is True
            assert "conflicted.py" in result.conflicted_files


class TestGitBranching:
    """Test git branch related functionality."""

    def test_get_current_branch(self, mock_codebase):
        """Test getting current branch name."""
        with patch('src.tools.commit.get_current_branch') as mock_branch:
            mock_result = Mock()
            mock_result.status = "success"
            mock_result.branch_name = "feature/new-functionality"
            mock_result.is_detached = False
            mock_branch.return_value = mock_result
            
            result = mock_branch(mock_codebase)
            
            assert result.status == "success"
            assert result.branch_name == "feature/new-functionality"
            assert result.is_detached is False

    def test_get_current_branch_detached(self, mock_codebase):
        """Test getting branch info when in detached HEAD."""
        with patch('src.tools.commit.get_current_branch') as mock_branch:
            mock_result = Mock()
            mock_result.status = "success"
            mock_result.branch_name = None
            mock_result.commit_hash = "abc123def456"
            mock_result.is_detached = True
            mock_branch.return_value = mock_result
            
            result = mock_branch(mock_codebase)
            
            assert result.status == "success"
            assert result.branch_name is None
            assert result.is_detached is True
            assert result.commit_hash == "abc123def456"


class TestGitHistory:
    """Test git history and log functionality."""

    def test_get_commit_history(self, mock_codebase):
        """Test getting commit history."""
        with patch('src.tools.commit.get_commit_history') as mock_history:
            mock_commits = [
                {
                    "hash": "abc123",
                    "message": "Add feature A",
                    "author": "Dev1 <<EMAIL>>",
                    "date": "2023-01-01T10:00:00Z"
                },
                {
                    "hash": "def456", 
                    "message": "Fix bug B",
                    "author": "Dev2 <<EMAIL>>",
                    "date": "2023-01-02T11:00:00Z"
                }
            ]
            mock_result = Mock()
            mock_result.status = "success"
            mock_result.commits = mock_commits
            mock_result.total_commits = 2
            mock_history.return_value = mock_result
            
            result = mock_history(mock_codebase, limit=10)
            
            assert result.status == "success"
            assert len(result.commits) == 2
            assert result.commits[0]["hash"] == "abc123"

    def test_get_commit_diff(self, mock_codebase):
        """Test getting diff for specific commit."""
        with patch('src.tools.commit.get_commit_diff') as mock_diff:
            mock_result = Mock()
            mock_result.status = "success"
            mock_result.commit_hash = "abc123def456"
            mock_result.diff = "+    print('Hello, World!')\n-    print('Hi')"
            mock_result.files_changed = ["main.py"]
            mock_result.insertions = 1
            mock_result.deletions = 1
            mock_diff.return_value = mock_result
            
            result = mock_diff(mock_codebase, "abc123def456")
            
            assert result.status == "success"
            assert result.commit_hash == "abc123def456"
            assert "+    print('Hello, World!')" in result.diff


class TestGitRemote:
    """Test git remote operations."""

    def test_get_remote_info(self, mock_codebase):
        """Test getting remote repository information."""
        with patch('src.tools.commit.get_remote_info') as mock_remote:
            mock_result = Mock()
            mock_result.status = "success"
            mock_result.remotes = [
                {
                    "name": "origin",
                    "url": "https://github.com/user/repo.git",
                    "fetch": True,
                    "push": True
                }
            ]
            mock_remote.return_value = mock_result
            
            result = mock_remote(mock_codebase)
            
            assert result.status == "success"
            assert len(result.remotes) == 1
            assert result.remotes[0]["name"] == "origin"

    def test_check_remote_sync(self, mock_codebase):
        """Test checking if local branch is in sync with remote."""
        with patch('src.tools.commit.check_remote_sync') as mock_sync:
            mock_result = Mock()
            mock_result.status = "success"
            mock_result.local_branch = "main"
            mock_result.remote_branch = "origin/main"
            mock_result.ahead = 2
            mock_result.behind = 0
            mock_result.up_to_date = False
            mock_sync.return_value = mock_result
            
            result = mock_sync(mock_codebase, "main")
            
            assert result.status == "success"
            assert result.ahead == 2
            assert result.behind == 0
            assert result.up_to_date is False


class TestGitIntegration:
    """Integration tests for git operations."""

    def test_commit_workflow_integration(self, mock_codebase):
        """Test complete commit workflow."""
        # Check status first
        with patch('src.tools.commit.get_git_status') as mock_status:
            mock_status_result = Mock()
            mock_status_result.status = "success"
            mock_status_result.modified_files = ["main.py"]
            mock_status.return_value = mock_status_result
            
            status_result = mock_status(mock_codebase)
            assert len(status_result.modified_files) == 1
        
        # Stage and commit changes
        with patch('src.tools.commit.commit') as mock_commit_func:
            mock_commit_result = Mock()
            mock_commit_result.status = "success"
            mock_commit_result.message = "Update main.py"
            mock_commit_result.files_changed = ["main.py"]
            mock_commit_func.return_value = mock_commit_result
            
            commit_result = commit(mock_codebase, "Update main.py")
            assert commit_result.status == "success"
            assert "main.py" in commit_result.files_changed

    def test_error_handling_consistency(self, mock_codebase):
        """Test consistent error handling across git operations."""
        git_operations = [
            ("commit", lambda: commit(mock_codebase, "test")),
        ]
        
        for op_name, op_func in git_operations:
            with patch(f'src.tools.commit.{op_name}') as mock_op:
                mock_result = Mock()
                mock_result.status = "error"
                mock_result.error = f"Git {op_name} failed"
                mock_op.return_value = mock_result
                
                result = op_func()
                assert result.status == "error"
                assert "failed" in result.error.lower()

    def test_git_repository_validation(self, mock_codebase):
        """Test git repository validation."""
        with patch('src.tools.commit.validate_git_repo') as mock_validate:
            mock_result = Mock()
            mock_result.status = "success"
            mock_result.is_git_repo = True
            mock_result.git_dir = ".git"
            mock_result.working_tree = "/path/to/repo"
            mock_validate.return_value = mock_result
            
            result = mock_validate(mock_codebase)
            
            assert result.status == "success"
            assert result.is_git_repo is True

    def test_git_repository_not_found(self, mock_codebase):
        """Test handling when not in a git repository."""
        with patch('src.tools.commit.validate_git_repo') as mock_validate:
            mock_result = Mock()
            mock_result.status = "error"
            mock_result.error = "Not a git repository"
            mock_result.is_git_repo = False
            mock_validate.return_value = mock_result
            
            result = mock_validate(mock_codebase)
            
            assert result.status == "error"
            assert result.is_git_repo is False
            assert "Not a git repository" in result.error