"""Test fixtures and utilities for rippr testing."""

import os
import tempfile
import shutil
from pathlib import Path
from typing import Generator, Dict, Any, Optional
from unittest.mock import Mock, MagicMock, patch
import pytest

from codegen.sdk.core.codebase import Codebase
from codegen.sdk.core.file import File


@pytest.fixture
def temp_workspace() -> Generator[Path, None, None]:
    """Create a temporary workspace directory for testing."""
    with tempfile.TemporaryDirectory() as temp_dir:
        workspace_path = Path(temp_dir)
        yield workspace_path


@pytest.fixture
def sample_files() -> Dict[str, str]:
    """Sample file contents for testing."""
    return {
        "main.py": '''#!/usr/bin/env python3
"""Main module for the application."""

def main():
    """Main function."""
    print("Hello, World!")
    return 0

if __name__ == "__main__":
    main()
''',
        "utils.py": '''"""Utility functions."""

def add(a, b):
    """Add two numbers."""
    return a + b

def multiply(a, b):
    """Multiply two numbers."""
    return a * b

class Calculator:
    """A simple calculator class."""
    
    def __init__(self):
        self.history = []
    
    def calculate(self, operation, a, b):
        """Perform calculation and store in history."""
        if operation == "add":
            result = add(a, b)
        elif operation == "multiply":
            result = multiply(a, b)
        else:
            raise ValueError(f"Unknown operation: {operation}")
        
        self.history.append((operation, a, b, result))
        return result
''',
        "README.md": '''# Test Project

This is a test project for validating tool functionality.

## Features

- Main module
- Utility functions
- Calculator class

## Usage

```python
python main.py
```
''',
        "tests/test_utils.py": '''"""Tests for utility functions."""

import pytest
from utils import add, multiply, Calculator


def test_add():
    """Test addition function."""
    assert add(2, 3) == 5
    assert add(-1, 1) == 0


def test_multiply():
    """Test multiplication function."""
    assert multiply(2, 3) == 6
    assert multiply(-1, 5) == -5


def test_calculator():
    """Test Calculator class."""
    calc = Calculator()
    assert calc.calculate("add", 2, 3) == 5
    assert len(calc.history) == 1
''',
        "config.json": '''{"project": "test", "version": "1.0.0"}''',
        "docs/api.md": '''# API Documentation

## Functions

### add(a, b)
Adds two numbers.

### multiply(a, b)
Multiplies two numbers.
''',
    }


@pytest.fixture
def mock_codebase(temp_workspace: Path, sample_files: Dict[str, str]) -> Codebase:
    """Create a real codebase with sample files."""
    import subprocess
    
    # Initialize git repository
    subprocess.run(["git", "init"], cwd=temp_workspace, check=True, capture_output=True)
    subprocess.run(["git", "config", "user.name", "Test User"], cwd=temp_workspace, check=True, capture_output=True)
    subprocess.run(["git", "config", "user.email", "<EMAIL>"], cwd=temp_workspace, check=True, capture_output=True)
    
    # Create actual files in temp workspace
    for filepath, content in sample_files.items():
        full_path = temp_workspace / filepath
        full_path.parent.mkdir(parents=True, exist_ok=True)
        full_path.write_text(content)
    
    # Create real Codebase instance
    codebase = Codebase(str(temp_workspace))
    
    return codebase


def _create_mock_file(files_dict: Dict[str, Mock], filepath: str, content: str) -> Mock:
    """Helper to create a new mock file."""
    mock_file = Mock(spec=File)
    mock_file.filepath = filepath
    mock_file.content = content
    files_dict[filepath] = mock_file
    return mock_file


@pytest.fixture
def mock_github_client():
    """Mock GitHub client for testing GitHub tools."""
    client = Mock()
    
    # Mock repository
    repo = Mock()
    repo.create_pull.return_value = Mock(number=123, html_url="https://github.com/test/repo/pull/123")
    repo.get_pull.return_value = Mock(
        number=123,
        title="Test PR",
        body="Test PR body",
        state="open",
        head=Mock(sha="abc123"),
        base=Mock(ref="main")
    )
    
    # Mock pull request
    pr = Mock()
    pr.create_issue_comment.return_value = Mock(id=456)
    pr.create_review_comment.return_value = Mock(id=789)
    repo.get_pull.return_value = pr
    
    client.get_repo.return_value = repo
    return client


@pytest.fixture
def mock_linear_client():
    """Mock Linear client for testing Linear tools."""
    client = Mock()
    
    # Mock issue
    issue = Mock()
    issue.id = "TEST-123"
    issue.title = "Test Issue"
    issue.description = "Test issue description"
    issue.state = Mock(name="Todo")
    issue.assignee = Mock(name="Test User")
    
    # Mock comments
    comment = Mock()
    comment.id = "comment-456"
    comment.body = "Test comment"
    comment.user = Mock(name="Test User")
    
    client.issue.return_value = issue
    client.issue_comments.return_value = [comment]
    client.create_comment.return_value = comment
    
    return client


@pytest.fixture
def mock_env_vars():
    """Mock environment variables for testing."""
    env_vars = {
        "GITHUB_TOKEN": "test_github_token",
        "LINEAR_API_KEY": "test_linear_key",
        "OPENAI_API_KEY": "test_openai_key",
        "ANTHROPIC_API_KEY": "test_anthropic_key",
    }
    
    with patch.dict(os.environ, env_vars, clear=False):
        yield env_vars


@pytest.fixture
def mock_llm():
    """Mock LLM for testing LangChain components."""
    llm = Mock()
    llm.invoke.return_value = Mock(content="Test response from LLM")
    llm.ainvoke.return_value = Mock(content="Test async response from LLM")
    return llm


@pytest.fixture
def sample_tool_call():
    """Sample tool call data for testing."""
    return {
        "id": "call_123456",
        "function": {
            "name": "test_tool",
            "arguments": '{"param1": "value1", "param2": "value2"}'
        },
        "type": "function"
    }


@pytest.fixture
def sample_error_cases():
    """Common error cases for testing."""
    return {
        "file_not_found": {
            "filepath": "nonexistent.py",
            "error": "File not found"
        },
        "permission_denied": {
            "filepath": "readonly.py",
            "error": "Permission denied"
        },
        "invalid_syntax": {
            "content": "def broken_function(\n    # missing closing parenthesis",
            "error": "SyntaxError"
        },
        "network_error": {
            "error": "Connection timeout"
        }
    }


class MockProcess:
    """Mock process for testing bash commands."""
    
    def __init__(self, returncode: int = 0, stdout: str = "", stderr: str = ""):
        self.returncode = returncode
        self.stdout = stdout
        self.stderr = stderr
    
    def communicate(self):
        return self.stdout.encode(), self.stderr.encode()


@pytest.fixture
def mock_subprocess():
    """Mock subprocess for testing bash commands."""
    with patch('subprocess.run') as mock_run:
        mock_run.return_value = MockProcess()
        yield mock_run


@pytest.fixture
def sample_semantic_search_results():
    """Sample semantic search results for testing."""
    return [
        {
            "file_path": "main.py",
            "line_number": 5,
            "content": "def main():",
            "score": 0.95
        },
        {
            "file_path": "utils.py", 
            "line_number": 12,
            "content": "class Calculator:",
            "score": 0.87
        },
        {
            "file_path": "utils.py",
            "line_number": 3,
            "content": "def add(a, b):",
            "score": 0.82
        }
    ]


@pytest.fixture
def sample_grep_results():
    """Sample grep search results for testing."""
    return [
        {
            "file": "main.py",
            "line_number": 1,
            "line": "#!/usr/bin/env python3",
            "match": "python"
        },
        {
            "file": "utils.py",
            "line_number": 3,
            "line": "def add(a, b):",
            "match": "def"
        },
        {
            "file": "tests/test_utils.py",
            "line_number": 8,
            "line": "def test_add():",
            "match": "def"
        }
    ]


@pytest.fixture
def mock_vector_store():
    """Mock vector store for testing semantic operations."""
    store = Mock()
    store.similarity_search.return_value = [
        Mock(page_content="Sample content 1", metadata={"file": "main.py", "line": 5}),
        Mock(page_content="Sample content 2", metadata={"file": "utils.py", "line": 12}),
    ]
    return store


@pytest.fixture
def mock_embeddings():
    """Mock embeddings for testing semantic operations."""
    embeddings = Mock()
    embeddings.embed_query.return_value = [0.1, 0.2, 0.3, 0.4, 0.5]
    embeddings.embed_documents.return_value = [
        [0.1, 0.2, 0.3, 0.4, 0.5],
        [0.2, 0.3, 0.4, 0.5, 0.6],
    ]
    return embeddings


@pytest.fixture
def sample_ast_nodes():
    """Sample AST nodes for testing symbol operations."""
    return {
        "functions": [
            {"name": "main", "line": 4, "file": "main.py"},
            {"name": "add", "line": 3, "file": "utils.py"},
            {"name": "multiply", "line": 7, "file": "utils.py"},
        ],
        "classes": [
            {"name": "Calculator", "line": 12, "file": "utils.py"},
        ],
        "variables": [
            {"name": "history", "line": 15, "file": "utils.py"},
        ]
    }


@pytest.fixture
def mock_langsmith():
    """Mock LangSmith for testing tracing."""
    with patch('langsmith.Client') as mock_client:
        client = Mock()
        client.create_run.return_value = Mock(id="run_123")
        client.update_run.return_value = None
        mock_client.return_value = client
        yield client


@pytest.fixture(autouse=True)
def suppress_warnings():
    """Suppress common warnings during testing."""
    import warnings
    warnings.filterwarnings("ignore", category=DeprecationWarning)
    warnings.filterwarnings("ignore", category=PendingDeprecationWarning)
    warnings.filterwarnings("ignore", category=UserWarning, module="langchain")