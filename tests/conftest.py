"""Pytest configuration file for the project."""

import os
import sys
import pytest

# Add the project root to the sys.path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

# Import all fixtures to make them available to tests
from tests.fixtures import *


@pytest.fixture
def temp_env_vars():
    """Create a fixture that can be used to temporarily set environment variables."""
    original_env = os.environ.copy()
    
    def _set_env(**kwargs):
        for key, value in kwargs.items():
            os.environ[key] = value
    
    yield _set_env
    
    # Restore original environment
    os.environ.clear()
    os.environ.update(original_env)