"""Basic tests to ensure the package is properly installed and can be imported."""
import pytest
from src import __version__


def test_version():
    """Test that the version is a string."""
    assert isinstance(__version__, str)
    assert __version__ != ""


def test_rippr_import():
    """Test that the rippr package can be imported."""
    try:
        import rippr
        assert rippr is not None
        assert hasattr(rippr, "__version__")
    except ImportError as e:
        pytest.fail(f"Failed to import rippr package: {e}")


def test_agents_import():
    """Test that the agents module can be imported."""
    try:
        from rippr import agents
        assert agents is not None
    except ImportError as e:
        pytest.skip(f"Failed to import agents module: {e}")


def test_langchain_import():
    """Test that the langchain module can be imported."""
    try:
        from rippr import langchain
        assert langchain is not None
    except ImportError as e:
        pytest.skip(f"Failed to import langchain module: {e}")


def test_tools_import():
    """Test that the tools module can be imported."""
    try:
        from rippr import tools
        assert tools is not None
    except ImportError as e:
        pytest.skip(f"Failed to import tools module: {e}")


def test_lsp_import():
    """Test that the lsp module can be imported."""
    try:
        from rippr import lsp
        assert lsp is not None
    except ImportError as e:
        pytest.skip(f"Failed to import lsp module: {e}")


def test_mcp_import():
    """Test that the mcp module can be imported."""
    try:
        from rippr import mcp
        assert mcp is not None
    except ImportError as e:
        pytest.skip(f"Failed to import mcp module: {e}")