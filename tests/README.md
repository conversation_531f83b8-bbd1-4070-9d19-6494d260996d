# Rippr Agent Testing Suite

Comprehensive testing infrastructure for the Rippr code agent framework, covering all tools, LangChain integrations, and agent functionality.

## Overview

This testing suite provides complete validation of:
- **Core Tools**: File operations, search, editing, symbol manipulation
- **External Integrations**: GitHub, Linear, Git operations  
- **Lang<PERSON>hain Components**: Tool wrappers, agents, graphs, LLM integrations
- **End-to-End Workflows**: Complete agent interactions and task execution
- **Performance & Reliability**: Load testing, error handling, edge cases

## Test Structure

```
tests/
├── fixtures.py                    # Shared test fixtures and utilities
├── conftest.py                    # Pytest configuration
├── run_comprehensive_tests.py     # Main test runner
├── tools/                         # Tool-specific tests
│   ├── unit/                      # Unit tests for individual tools
│   │   ├── test_file_operations.py
│   │   ├── test_search_operations.py
│   │   ├── test_symbol_operations.py
│   │   ├── test_external_integrations.py
│   │   └── test_git_operations.py
│   └── integration/               # Tool integration tests
├── langchain/                     # LangChain integration tests
│   ├── unit/                      # LangChain tool wrapper tests
│   │   └── test_langchain_tools.py
│   └── integration/               # Agent and graph tests
│       └── test_agent_integration.py
└── README.md                      # This file
```

## Quick Start

### Prerequisites

- Python 3.12+
- All dependencies from `pyproject.toml`
- Git repository (for git operation tests)

### Running All Tests

```bash
# Run complete test suite
python tests/run_comprehensive_tests.py

# With verbose output
python tests/run_comprehensive_tests.py --verbose

# Specific categories only
python tests/run_comprehensive_tests.py --categories unit_tools_file_ops unit_langchain_tools
```

### Running Specific Tests

```bash
# Individual test files
pytest tests/tools/unit/test_file_operations.py -v

# Specific test classes
pytest tests/tools/unit/test_file_operations.py::TestViewFile -v

# Individual test methods
pytest tests/tools/unit/test_file_operations.py::TestViewFile::test_view_file_success -v
```

## Test Categories

### 1. Unit Tests - Tools (`tests/tools/unit/`)

#### File Operations (`test_file_operations.py`)
Tests for core file manipulation tools:
- **ViewFile**: Content viewing, pagination, line numbers
- **CreateFile**: File creation, conflict detection, validation
- **EditFile**: Content editing, error handling
- **DeleteFile**: File removal, safety checks
- **RenameFile**: File renaming, conflict resolution
- **ListDirectory**: Directory listing, depth control

```python
# Example: Test file viewing with pagination
def test_view_file_with_pagination(self, mock_codebase):
    result = view_file(mock_codebase, "large_file.py", max_lines=100)
    assert result.status == "success"
    assert result.has_more is True
```

#### Search Operations (`test_search_operations.py`)
Tests for search and pattern matching:
- **Search**: Regex and text search, filtering
- **SearchFilesByName**: File pattern matching
- **SemanticSearch**: Vector-based content search
- **ReplacementEdit**: Find and replace operations
- **GlobalReplacementEdit**: Multi-file replacements
- **SemanticEdit**: AI-powered code editing

#### Symbol Operations (`test_symbol_operations.py`)
Tests for code symbol manipulation:
- **RevealSymbol**: Symbol location and definition
- **MoveSymbol**: Symbol refactoring and relocation
- **RunCodemod**: Automated code transformations
- **Reflection**: Code analysis and insights
- **BashCommand**: Shell command execution

#### External Integrations (`test_external_integrations.py`)
Tests for third-party service integrations:
- **GitHub**: PR creation, comments, reviews, viewing
- **Linear**: Issue management, comments, webhooks
- Authentication and permission handling
- Error scenarios and rate limiting

#### Git Operations (`test_git_operations.py`)
Tests for version control operations:
- **Commit**: Change staging and committing
- **GitStatus**: Working directory status
- **GitHistory**: Commit history and diffs
- **GitBranching**: Branch management
- **GitRemote**: Remote repository operations

### 2. Unit Tests - LangChain (`tests/langchain/unit/`)

#### LangChain Tool Wrappers (`test_langchain_tools.py`)
Tests for LangChain tool integration:
- Tool initialization and configuration
- Message rendering and artifact handling
- Error propagation and recovery
- Schema validation and type checking
- Tool composition and chaining

```python
# Example: Test LangChain tool wrapper
def test_view_file_tool_run_success(self, mock_codebase):
    tool = ViewFileTool(mock_codebase)
    result = tool._run(tool_call_id="test_id", filepath="main.py")
    assert isinstance(result, ToolMessage)
```

### 3. Integration Tests (`tests/langchain/integration/`)

#### Agent Integration (`test_agent_integration.py`)
End-to-end tests for agent functionality:
- **Agent Creation**: Tool binding and configuration
- **Conversation Flow**: Multi-turn interactions
- **Tool Execution**: Automatic tool selection and usage
- **Error Recovery**: Graceful failure handling
- **Streaming**: Real-time response generation

```python
# Example: Test complete workflow
@pytest.mark.asyncio
async def test_file_management_workflow(self, mock_codebase, mock_llm):
    graph = create_graph(mock_llm, get_workspace_tools(mock_codebase))
    result = await graph.ainvoke({
        "messages": [HumanMessage(content="Create a hello world file")]
    })
    # Verify create_file and view_file tools were used
```

## Test Fixtures and Utilities

### Core Fixtures (`fixtures.py`)

- **mock_codebase**: Simulated codebase with sample files
- **mock_github_client**: GitHub API client mock
- **mock_linear_client**: Linear API client mock
- **mock_llm**: Language model mock for agent testing
- **sample_files**: Realistic code examples for testing

### Environment Setup

```python
@pytest.fixture
def mock_env_vars():
    """Mock environment variables for testing."""
    with patch.dict(os.environ, {
        "GITHUB_TOKEN": "test_token",
        "LINEAR_API_KEY": "test_key"
    }):
        yield
```

## Running Tests

### Development Workflow

1. **Fast Unit Tests** (for development):
   ```bash
   pytest tests/tools/unit/ -x --ff
   ```

2. **Integration Tests** (before commits):
   ```bash
   pytest tests/langchain/integration/ -v
   ```

3. **Full Test Suite** (CI/CD):
   ```bash
   python tests/run_comprehensive_tests.py
   ```

### Test Configuration

#### pytest.ini Options
```ini
[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = "test_*.py"
python_functions = "test_*"
addopts = "--strict-markers --disable-warnings"
markers = [
    "slow: marks tests as slow",
    "integration: marks tests as integration tests",
    "external: marks tests requiring external services"
]
```

#### Coverage Configuration
```bash
# Generate coverage report
pytest --cov=src --cov-report=html --cov-report=term-missing

# View coverage in browser
open htmlcov/index.html
```

## Test Data and Mocking

### Sample Test Data

The test suite includes realistic sample files:
- **main.py**: Simple Python application
- **utils.py**: Utility functions and classes
- **README.md**: Documentation example
- **config.json**: Configuration file
- **test files**: Complete test examples

### Mocking Strategy

- **External APIs**: Mock GitHub/Linear clients with realistic responses
- **File System**: In-memory file system simulation
- **LLM Calls**: Deterministic responses for consistent testing
- **Git Operations**: Mock git commands and repository state

## Performance Testing

### Benchmark Tests
```python
def test_search_performance(benchmark, mock_codebase):
    """Benchmark search operation performance."""
    result = benchmark(search, mock_codebase, "function")
    assert result.status == "success"
```

### Load Testing
```python
@pytest.mark.asyncio
async def test_concurrent_tool_execution():
    """Test handling multiple simultaneous tool calls."""
    # Simulate high concurrency scenarios
```

## Common Testing Patterns

### Tool Testing Pattern
```python
class TestToolName:
    def test_tool_success(self, mock_codebase):
        result = tool_function(mock_codebase, valid_params)
        assert result.status == "success"
        assert result.expected_field == expected_value
    
    def test_tool_error_handling(self, mock_codebase):
        result = tool_function(mock_codebase, invalid_params)
        assert result.status == "error"
        assert "expected error message" in result.error
```

### Agent Testing Pattern
```python
@pytest.mark.asyncio
async def test_agent_workflow(self, mock_codebase, mock_llm):
    tools = get_workspace_tools(mock_codebase)
    graph = create_graph(mock_llm, tools)
    
    result = await graph.ainvoke({
        "messages": [HumanMessage(content="test request")]
    })
    
    assert len(result["messages"]) > 1
    # Verify expected tool usage
```

## Troubleshooting

### Common Issues

1. **Import Errors**
   ```bash
   # Ensure PYTHONPATH includes src/
   export PYTHONPATH="${PYTHONPATH}:$(pwd)/src"
   ```

2. **Mock Configuration**
   ```python
   # Reset mocks between tests
   @pytest.fixture(autouse=True)
   def reset_mocks():
       mock_object.reset_mock()
   ```

3. **Async Test Issues**
   ```python
   # Use proper async fixtures
   @pytest.mark.asyncio
   async def test_async_function():
       result = await async_function()
   ```

### Debug Mode

Run tests with debug output:
```bash
pytest -s -vv --tb=long tests/specific_test.py::TestClass::test_method
```

## Contributing

### Adding New Tests

1. **Create test file** following naming convention `test_*.py`
2. **Use fixtures** from `fixtures.py` for consistent setup
3. **Follow patterns** established in existing tests
4. **Add documentation** for complex test scenarios
5. **Update test runner** if adding new categories

### Test Quality Guidelines

- **Test Independence**: Each test should run independently
- **Clear Assertions**: Use descriptive assertion messages
- **Mock External Dependencies**: Don't rely on external services
- **Cover Edge Cases**: Test error conditions and boundary cases
- **Performance Awareness**: Mark slow tests appropriately

### Code Coverage Goals

- **Core Tools**: 95%+ coverage
- **LangChain Integration**: 90%+ coverage  
- **Agent Functionality**: 85%+ coverage
- **External Integrations**: 80%+ coverage (due to mocking limitations)

## CI/CD Integration

### GitHub Actions Example
```yaml
- name: Run Tests
  run: |
    python tests/run_comprehensive_tests.py --categories unit_tools unit_langchain
    
- name: Upload Coverage
  uses: codecov/codecov-action@v3
  with:
    file: ./coverage.xml
```

### Test Results

The test runner generates:
- **JUnit XML**: `test-results/*.xml` for CI integration
- **Coverage Reports**: `htmlcov/` directory with detailed coverage
- **JSON Results**: `test-results/comprehensive_results.json` with detailed metrics

## Support

For testing issues or questions:
1. Check existing test patterns in similar files
2. Review fixture documentation in `fixtures.py`
3. Run specific failing tests with verbose output
4. Consult the main project documentation

The testing infrastructure is designed to be comprehensive, maintainable, and developer-friendly. It provides confidence in the agent's functionality while supporting rapid development and refactoring.