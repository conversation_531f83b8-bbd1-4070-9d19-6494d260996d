# Rippr API Setup Guide

This guide will help you set up the Rippr API system that allows any frontend to connect to your code agents.

## Overview

The Rippr API provides a REST interface with the following features:

- **Agent Management**: Create, configure, and manage different types of agents
- **Chat Interface**: Send messages and receive responses with streaming support
- **Tool Execution**: Direct access to individual codebase tools
- **File Operations**: CRUD operations on codebase files
- **Search Operations**: Search across the codebase
- **WebSocket Support**: Real-time streaming responses
- **Authentication**: API key-based authentication
- **Rate Limiting**: Configurable rate limiting
- **CORS Support**: Cross-origin resource sharing

## Quick Start

### 1. Install Dependencies

```bash
# Install the API dependencies
pip install fastapi uvicorn websockets python-multipart slowapi
```

### 2. Configure Environment

Copy the example environment file and configure your settings:

```bash
cp .env.example .env
```

Edit `.env` with your configuration:

```env
# API Server
RIPPR_API_HOST=0.0.0.0
RIPPR_API_PORT=8000

# Authentication
RIPPR_DEFAULT_API_KEY=your-secure-api-key-here

# Model Provider Keys
OPENAI_API_KEY=your-openai-key
ANTHROPIC_API_KEY=your-anthropic-key
# ... other provider keys
```

### 3. Start the API Server

#### Option A: Using the CLI

```bash
# Start with default settings
rippr --api-server

# Custom host and port
rippr --api-server --api-host 127.0.0.1 --api-port 3000

# Development mode with auto-reload
python src/api/cli.py --reload
```

#### Option B: Direct Python

```bash
python -m src.api.server
```

#### Option C: Using uvicorn directly

```bash
uvicorn src.api.server:create_app --factory --host 0.0.0.0 --port 8000 --reload
```

### 4. Access the API

- **API Documentation**: http://localhost:8000/docs
- **Alternative Docs**: http://localhost:8000/redoc
- **Health Check**: http://localhost:8000/health

## API Endpoints

### Authentication

All endpoints (except `/health` and docs) require API key authentication:

```bash
curl -H "Authorization: Bearer your-api-key" http://localhost:8000/api/v1/agents/
```

### Agent Management

#### Create an Agent

```bash
curl -X POST "http://localhost:8000/api/v1/agents/" \
  -H "Authorization: Bearer your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "My Code Agent",
    "config": {
      "agent_type": "chat",
      "model_provider": "copilot",
      "model_name": "gpt-4.1",
      "codebase_path": ".",
      "memory": true,
      "interactive": true
    }
  }'
```

#### List Agents

```bash
curl -H "Authorization: Bearer your-api-key" \
  http://localhost:8000/api/v1/agents/
```

### Chat Interface

#### Send a Message

```bash
curl -X POST "http://localhost:8000/api/v1/chat/{agent_id}" \
  -H "Authorization: Bearer your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "message": "Analyze this codebase and tell me about its structure",
    "thread_id": "optional-thread-id"
  }'
```

#### Streaming Chat

```bash
curl -X POST "http://localhost:8000/api/v1/chat/{agent_id}/stream" \
  -H "Authorization: Bearer your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "message": "Explain this function",
    "thread_id": "thread-123"
  }'
```

### Tool Execution

#### List Available Tools

```bash
curl -H "Authorization: Bearer your-api-key" \
  http://localhost:8000/api/v1/tools/
```

#### Execute a Tool

```bash
curl -X POST "http://localhost:8000/api/v1/tools/execute" \
  -H "Authorization: Bearer your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "tool_name": "view_file",
    "parameters": {
      "filepath": "src/main.py",
      "start_line": 1,
      "end_line": 50
    }
  }'
```

#### File Operations

```bash
# View a file
curl -X POST "http://localhost:8000/api/v1/tools/file/view?filepath=src/main.py" \
  -H "Authorization: Bearer your-api-key"

# Edit a file
curl -X POST "http://localhost:8000/api/v1/tools/file/edit" \
  -H "Authorization: Bearer your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "filepath": "src/example.py",
    "content": "print(\"Hello, World!\")"
  }'

# Search the codebase
curl -X POST "http://localhost:8000/api/v1/tools/search" \
  -H "Authorization: Bearer your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "function",
    "file_extensions": [".py"],
    "use_regex": false
  }'
```

### WebSocket Chat

Connect to the WebSocket endpoint for real-time chat:

```javascript
const ws = new WebSocket('ws://localhost:8000/api/v1/ws/chat/{agent_id}?api_key=your-api-key');

ws.onopen = function() {
    // Send a message
    ws.send(JSON.stringify({
        message: "Hello, agent!",
        thread_id: "thread-123"
    }));
};

ws.onmessage = function(event) {
    const data = JSON.parse(event.data);
    console.log('Received:', data);
};
```

## Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `RIPPR_API_HOST` | Server host | `0.0.0.0` |
| `RIPPR_API_PORT` | Server port | `8000` |
| `RIPPR_API_RELOAD` | Enable auto-reload | `false` |
| `RIPPR_API_WORKERS` | Number of workers | `1` |
| `RIPPR_API_KEYS` | Valid API keys (comma-separated) | - |
| `RIPPR_DEFAULT_API_KEY` | Default API key | `rippr-dev-key-12345` |
| `RIPPR_DISABLE_AUTH` | Disable authentication | `false` |
| `RIPPR_CORS_ORIGINS` | Allowed CORS origins | `*` |
| `RIPPR_RATE_LIMIT` | Requests per minute | `60` |

### Security Considerations

1. **API Keys**: Use strong, unique API keys in production
2. **CORS**: Restrict CORS origins to your frontend domains
3. **Rate Limiting**: Adjust rate limits based on your needs
4. **HTTPS**: Use HTTPS in production (configure reverse proxy)
5. **Authentication**: Never disable authentication in production

## Docker Deployment

### Build and Run

```bash
# Build the image
docker build -f Dockerfile.api -t rippr-api .

# Run the container
docker run -p 8000:8000 --env-file .env rippr-api
```

### Using Docker Compose

```bash
# Start the API server
docker-compose -f docker-compose.api.yml up -d

# With production reverse proxy
docker-compose -f docker-compose.api.yml --profile production up -d
```

## Frontend Integration Examples

### JavaScript/TypeScript

```typescript
class RipprAPIClient {
    constructor(private baseURL: string, private apiKey: string) {}
    
    async createAgent(config: AgentConfig): Promise<Agent> {
        const response = await fetch(`${this.baseURL}/api/v1/agents/`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${this.apiKey}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(config)
        });
        return response.json();
    }
    
    async sendMessage(agentId: string, message: string): Promise<ChatResponse> {
        const response = await fetch(`${this.baseURL}/api/v1/chat/${agentId}`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${this.apiKey}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ message })
        });
        return response.json();
    }
}
```

### Python Client

```python
import requests

class RipprAPIClient:
    def __init__(self, base_url: str, api_key: str):
        self.base_url = base_url
        self.headers = {"Authorization": f"Bearer {api_key}"}
    
    def create_agent(self, config: dict) -> dict:
        response = requests.post(
            f"{self.base_url}/api/v1/agents/",
            headers=self.headers,
            json=config
        )
        return response.json()
    
    def send_message(self, agent_id: str, message: str) -> dict:
        response = requests.post(
            f"{self.base_url}/api/v1/chat/{agent_id}",
            headers=self.headers,
            json={"message": message}
        )
        return response.json()
```

## Troubleshooting

### Common Issues

1. **Import Errors**: Make sure FastAPI dependencies are installed
2. **Authentication Errors**: Check your API key configuration
3. **CORS Errors**: Configure CORS origins for your frontend domain
4. **Rate Limiting**: Adjust rate limits if you're hitting them frequently

### Debug Mode

Start the server with debug logging:

```bash
LOG_LEVEL=DEBUG python src/api/cli.py --reload
```

### Health Check

Check if the API is running:

```bash
curl http://localhost:8000/health
```

## Next Steps

1. **Frontend Development**: Use the API to build your frontend interface
2. **Custom Tools**: Add custom tools to extend functionality
3. **Monitoring**: Set up monitoring and logging for production
4. **Scaling**: Use multiple workers or container orchestration for scaling
5. **Security**: Implement additional security measures as needed
