# Vertex AI Integration with LiteLLM

This document explains how to use Vertex AI through LiteLLM in the rippr framework.

## Prerequisites

1. **Google Cloud Project**: You need a Google Cloud Project with Vertex AI API enabled
2. **Authentication**: Set up authentication using one of the methods below
3. **LiteLLM**: The framework automatically installs LiteLLM as a dependency

## Authentication Setup

### Option 1: Service Account Key (Recommended for Development)

1. Create a service account in your Google Cloud Project
2. Download the service account JSON key file
3. Set the environment variable:
   ```bash
   export GOOGLE_APPLICATION_CREDENTIALS="/path/to/your/service-account-key.json"
   export VERTEX_PROJECT="your-gcp-project-id"
   export VERTEX_LOCATION="us-central1"  # Optional, defaults to us-central1
   ```

### Option 2: Default Credentials (For Production/GCP Environment)

If running on Google Cloud (Compute Engine, Cloud Run, etc.), you can use default credentials:

```bash
export VERTEX_PROJECT="your-gcp-project-id"
export VERTEX_LOCATION="us-central1"  # Optional, defaults to us-central1
```

## Usage Examples

### Basic Usage

```python
from src.langchain.agent import create_codebase_agent
from codegen import Codebase

# Initialize codebase
codebase = Codebase.from_repo("your-org/your-repo", language="python")

# Create agent with Vertex AI
agent = create_codebase_agent(
    codebase=codebase,
    model_provider="vertex",
    model_name="gemini-2.5-pro",  # or "gemini-1.5-flash", "claude-3-sonnet", etc.
    temperature=0.2
)

# Use the agent
result = agent.run("Analyze the codebase and suggest improvements")
```

### Available Models

Through LiteLLM, you can use various models on Vertex AI:

- **Gemini Models**: `gemini-2.5-pro`, `gemini-2.5-flash`
- **Claude Models**: `claude-3.5-sonnet`, `claude-3-haiku`, `claude-3-opus`
- **Other Models**: Check LiteLLM documentation for the latest supported models

### Advanced Configuration

```python
agent = create_codebase_agent(
    codebase=codebase,
    model_provider="vertex",
    model_name="gemini-2.5-pro",
    temperature=0.2,
    top_p=0.9,
    top_k=40,
    max_tokens=8192
)
```

## Environment Variables Reference

| Variable | Required | Default | Description |
|----------|----------|---------|-------------|
| `VERTEX_PROJECT` | Yes | - | Your Google Cloud Project ID |
| `VERTEX_LOCATION` | No | `us-central1` | Vertex AI region |
| `GOOGLE_APPLICATION_CREDENTIALS` | Conditional | - | Path to service account JSON (required if not using default credentials) |

## Troubleshooting

### Common Issues

1. **Authentication Error**: Make sure your service account has the necessary permissions:
   - Vertex AI User (`roles/aiplatform.user`)
   - Service Account Token Creator (`roles/iam.serviceAccountTokenCreator`)

2. **Project Not Found**: Verify your `VERTEX_PROJECT` environment variable is set correctly

3. **Region Issues**: Some models may not be available in all regions. Try `us-central1` or `us-east1`

4. **Quota Limits**: Check your Vertex AI quotas in the Google Cloud Console

### Testing Your Setup

```python
from src.langchain.llm import LLM

# Test basic connectivity
llm = LLM(model_provider="vertex", model_name="gemini-2.5-pro-preview-05-06")
response = llm.invoke("Hello, how are you?")
print(response.content)
```

## Benefits of Using Vertex AI

1. **Enterprise Security**: Built on Google Cloud's secure infrastructure
2. **Model Variety**: Access to multiple model families (Gemini, Claude, etc.)
3. **Scalability**: Automatic scaling based on demand
4. **Cost Control**: Pay-per-use pricing with detailed billing
5. **Compliance**: Meets various compliance standards (SOC 2, ISO 27001, etc.)

## Next Steps

- Explore different models to find the best fit for your use case
- Set up monitoring and logging for production usage
- Consider using Vertex AI's fine-tuning capabilities for specialized tasks

