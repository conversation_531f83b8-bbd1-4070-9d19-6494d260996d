# Logging Configuration

Rippr provides flexible logging options to control the verbosity of agent execution output.

## Logging Modes

### 1. Condensed Logging (Default)

Provides clean, readable output with progress indicators:

```python
from src.agents.code_agent import CodeAgent
from codegen import Codebase

codebase = Codebase(".")
agent = CodeAgent(codebase, condensed_logging=True)  # Default
result = agent.run("Update the README file")
```

**Output Example:**
```
🚀 Starting task: Update the README file
============================================================
🔧 [1.1s] Calling view_file with 1 args (file: README.md)
📋 [1.2s] view_file: [VIEW FILE]: README.md (95 lines total)
🔧 [2.0s] Calling edit_file with 3375 chars of input
📋 [2.1s] edit_file: File edited successfully
💭 [2.5s] Reasoning: I have updated the README file with...
✅ Completed in 3.0s after 4 steps
📝 Result: Task completed successfully
============================================================
```

### 2. Debug Mode (Verbose)

Shows full LangGraph checkpoint logging and message details:

```python
agent = CodeAgent(codebase, debug=True, condensed_logging=False)
result = agent.run("Update the README file")
```

**Output Example:**
```
[1:checkpoint] State at the beginning of step 1:
{'messages': [HumanMessage(content='Update the README file', additional_kwargs={}, response_metadata={}, id='...')]}

[1:reasoner] Calling reasoner with input: {'messages': [...]}
[1:reasoner] Output: AIMessage(content='I need to view the current README file...', tool_calls=[...])

[2:tools] Calling tools with input: {'messages': [...]}
... (thousands of lines of detailed state information)
```

### 3. Silent Mode

Minimal output, only shows final results:

```python
agent = CodeAgent(codebase, debug=False, condensed_logging=False)
result = agent.run("Update the README file")
print(result)  # Only the final result
```

## Configuration Options

### CodeAgent Parameters

- `debug: bool = False` - Enable verbose LangGraph checkpoint logging
- `condensed_logging: bool = True` - Enable condensed, readable logging

### Logging Combinations

| debug | condensed_logging | Output |
|-------|------------------|---------|
| False | True | Condensed logging (recommended) |
| True | False | Full verbose logging |
| False | False | Silent mode |
| True | True | Condensed logging (debug ignored) |

## TaskAgent Integration

The TaskAgent automatically uses condensed logging:

```python
from src.agents.task_agent import TaskAgent

task_agent = TaskAgent(code_agent, condensed_logging=True)
result = task_agent.run_task("Update documentation")
```

## Environment Variables

You can also control logging through environment variables:

```bash
export RIPPR_DEBUG=true          # Enable debug mode
export RIPPR_CONDENSED=false     # Disable condensed logging
```

## Custom Logging

For advanced use cases, you can provide your own logger:

```python
from src.agents.loggers import ExternalLogger

class MyLogger(ExternalLogger):
    def log(self, data):
        # Custom logging logic
        print(f"Custom: {data}")

agent = CodeAgent(codebase, logger=MyLogger())
```

## Troubleshooting

### Too Much Output
If you're seeing overwhelming checkpoint messages like `[25:checkpoint] State at the end of step 25:`, ensure:
- `debug=False` (default)
- `condensed_logging=True` (default)

### Too Little Output
If you need more detail for debugging:
- Set `debug=True` for full LangGraph logging
- Or examine the condensed output which shows tool calls and reasoning

### Performance
- Condensed logging has minimal performance impact
- Debug mode can slow execution due to extensive logging
- Silent mode is fastest but provides no progress feedback
