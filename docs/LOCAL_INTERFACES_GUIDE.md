# Rippr Local Interfaces Guide

This guide covers the various local interface options for Rippr, designed specifically for local development with real-time feedback and streaming capabilities.

## 🎯 Why Local Interfaces?

For local development, you often want:
- **Real-time streaming** responses as the agent thinks
- **Low latency** without HTTP overhead
- **Simple setup** without authentication complexity
- **Direct integration** with your development workflow
- **Immediate feedback** during development

## 🚀 Available Local Interfaces

### 1. **WebSocket Server** (Recommended for Frontends)

Perfect for building custom frontends with real-time streaming.

#### Start the Server
```bash
# Using the main CLI
rippr --local-server

# Custom host and port
rippr --local-server --local-host localhost --local-port 8765

# Direct execution
python src/local_interface/websocket_server.py
```

#### Features
- **Real-time bidirectional communication**
- **Streaming responses** with chunked output
- **Multiple agent support**
- **Simple JSON protocol**
- **Auto-reconnection support**

#### Usage Example
```javascript
const ws = new WebSocket('ws://localhost:8765');

// Create an agent
ws.send(JSON.stringify({
    type: 'create_agent',
    config: {
        type: 'chat',
        model_provider: 'copilot',
        codebase_path: '.'
    }
}));

// Send a message
ws.send(JSON.stringify({
    type: 'send_message',
    agent_id: 'your-agent-id',
    message: 'Analyze this codebase'
}));

// Receive streaming chunks
ws.onmessage = function(event) {
    const data = JSON.parse(event.data);
    if (data.type === 'response_chunk') {
        console.log('Chunk:', data.chunk);
    }
};
```

#### HTML Interface
Open `src/local_interface/local_ui.html` in your browser for a ready-to-use interface:

```bash
# Start the WebSocket server
rippr --local-server

# Open the HTML interface
open src/local_interface/local_ui.html
```

### 2. **Simple Python Interface** (Recommended for Scripting)

Perfect for quick scripting and automation tasks.

#### Start the Interface
```bash
# Interactive mode
rippr --simple-interface

# Single message
python src/local_interface/simple_interface.py -m "Analyze this codebase"

# Interactive mode with custom settings
python src/local_interface/simple_interface.py -i --agent-type code --model-provider vertex
```

#### Features
- **Command-line interface** with interactive mode
- **Multiple agent management**
- **Direct Python integration**
- **Streaming output** support
- **Simple commands** for agent switching

#### Usage Example
```python
from src.local_interface.simple_interface import LocalAgentInterface

# Initialize
interface = LocalAgentInterface(".")

# Create agents
chat_agent_id = interface.create_chat_agent("my_chat", "copilot")
code_agent_id = interface.create_code_agent("my_code", "vertex")

# Switch between agents
interface.switch_agent("my_chat")
response = interface.chat("What is this codebase about?")

interface.switch_agent("my_code")
response = interface.chat("Show me the main entry points")

# Interactive mode
interface.interactive_mode()
```

#### Interactive Commands
```bash
/help                           # Show help
/agents                         # List all agents
/switch <name>                  # Switch to agent
/create chat <name> [provider]  # Create chat agent
/create code <name> [provider]  # Create code agent
/exit                           # Exit
```

### 3. **Jupyter Notebook Interface** (Recommended for Experimentation)

Perfect for experimentation, prototyping, and iterative development.

#### Setup
```bash
# Install Jupyter if not already installed
pip install jupyter ipywidgets

# Start Jupyter
jupyter notebook

# Open the example notebook
# examples/local_notebook_interface.ipynb
```

#### Features
- **Interactive widgets** for chat interface
- **Direct agent access** in cells
- **Tool integration** examples
- **Agent comparison** capabilities
- **Rich output** with Markdown and HTML

#### Usage Example
```python
# In a Jupyter cell
from src.agents.chat_agent import ChatAgent
from codegen import Codebase

# Create agent
codebase = Codebase(".")
agent = ChatAgent(codebase=codebase, model_provider="copilot")

# Use directly
response = agent.run("Analyze this codebase")
print(response)

# Or use the interactive widget (see notebook for full example)
```

### 4. **Direct CLI Integration** (Default)

The standard CLI with enhanced local features.

#### Usage
```bash
# Standard interactive mode
rippr

# With streaming output
rippr --realtime-streaming

# Single command with streaming
rippr --command "Analyze this codebase" --realtime-streaming

# Custom agent and model
rippr --agent code --model-provider vertex --realtime-streaming
```

## 🔧 Configuration

### Environment Variables
```bash
# Model provider API keys
export OPENAI_API_KEY="your-key"
export ANTHROPIC_API_KEY="your-key"
export GITHUB_COPILOT_OAUTH_TOKEN="your-token"

# Codegen SDK (if using)
export CODEGEN_API_TOKEN="your-token"
export CODEGEN_ORG_ID="your-org-id"
```

### Model Providers
- **copilot** - GitHub Copilot (recommended for chat)
- **openai** - OpenAI GPT models
- **anthropic** - Anthropic Claude models
- **vertex** - Google Vertex AI (recommended for code)
- **google** - Google Gemini models
- **xai** - xAI Grok models

## 🎨 Building Custom Frontends

### WebSocket Protocol

#### Message Types

**Client to Server:**
```json
{
  "type": "create_agent",
  "config": {
    "type": "chat|code",
    "model_provider": "copilot|openai|anthropic|vertex",
    "model_name": "optional-specific-model",
    "codebase_path": "."
  }
}

{
  "type": "send_message",
  "agent_id": "agent-uuid",
  "message": "Your message here"
}

{
  "type": "list_agents"
}

{
  "type": "ping"
}
```

**Server to Client:**
```json
{
  "type": "connected",
  "message": "Connected to Rippr Local Agent Server",
  "timestamp": "2024-01-01T00:00:00"
}

{
  "type": "agent_created",
  "agent_id": "agent-uuid",
  "agent_type": "chat",
  "model_provider": "copilot"
}

{
  "type": "response_start",
  "agent_id": "agent-uuid",
  "message": "original-message"
}

{
  "type": "response_chunk",
  "agent_id": "agent-uuid",
  "chunk": "partial response text",
  "is_final": false
}

{
  "type": "response_complete",
  "agent_id": "agent-uuid",
  "full_response": "complete response"
}

{
  "type": "error",
  "message": "error description"
}
```

### Example Frontend Implementations

#### React Component
```jsx
import { useState, useEffect } from 'react';

function RipprChat() {
  const [ws, setWs] = useState(null);
  const [messages, setMessages] = useState([]);
  const [currentResponse, setCurrentResponse] = useState('');

  useEffect(() => {
    const websocket = new WebSocket('ws://localhost:8765');
    
    websocket.onmessage = (event) => {
      const data = JSON.parse(event.data);
      
      if (data.type === 'response_chunk') {
        setCurrentResponse(prev => prev + data.chunk);
        
        if (data.is_final) {
          setMessages(prev => [...prev, {
            type: 'agent',
            content: currentResponse + data.chunk
          }]);
          setCurrentResponse('');
        }
      }
    };
    
    setWs(websocket);
    return () => websocket.close();
  }, []);

  const sendMessage = (message) => {
    ws.send(JSON.stringify({
      type: 'send_message',
      agent_id: 'your-agent-id',
      message: message
    }));
    
    setMessages(prev => [...prev, {
      type: 'user',
      content: message
    }]);
  };

  return (
    <div>
      {/* Your chat UI here */}
    </div>
  );
}
```

#### Python Client
```python
import asyncio
import websockets
import json

async def chat_client():
    uri = "ws://localhost:8765"
    
    async with websockets.connect(uri) as websocket:
        # Create agent
        await websocket.send(json.dumps({
            "type": "create_agent",
            "config": {
                "type": "chat",
                "model_provider": "copilot"
            }
        }))
        
        # Listen for responses
        async for message in websocket:
            data = json.loads(message)
            
            if data["type"] == "agent_created":
                agent_id = data["agent_id"]
                
                # Send a message
                await websocket.send(json.dumps({
                    "type": "send_message",
                    "agent_id": agent_id,
                    "message": "Hello, agent!"
                }))
            
            elif data["type"] == "response_chunk":
                print(data["chunk"], end="", flush=True)
                
                if data["is_final"]:
                    print()  # New line

asyncio.run(chat_client())
```

## 🚀 Quick Start Examples

### 1. **Web Development**
```bash
# Terminal 1: Start WebSocket server
rippr --local-server

# Terminal 2: Serve your frontend
cd your-frontend-project
npm start  # or your preferred dev server

# Connect to ws://localhost:8765 from your frontend
```

### 2. **Python Scripting**
```bash
# Quick interactive session
rippr --simple-interface

# Or programmatic usage
python -c "
from src.local_interface.simple_interface import LocalAgentInterface
interface = LocalAgentInterface('.')
interface.create_chat_agent('test', 'copilot')
print(interface.chat('What is this project about?'))
"
```

### 3. **Jupyter Experimentation**
```bash
# Start Jupyter
jupyter notebook

# Open examples/local_notebook_interface.ipynb
# Run the cells to get an interactive chat interface
```

### 4. **CLI with Streaming**
```bash
# Real-time streaming output
rippr --realtime-streaming

# Single command with streaming
rippr --command "Analyze this codebase and suggest improvements" --realtime-streaming
```

## 🔍 Troubleshooting

### Common Issues

1. **WebSocket Connection Failed**
   ```bash
   # Check if server is running
   netstat -an | grep 8765
   
   # Try different port
   rippr --local-server --local-port 8766
   ```

2. **Agent Creation Failed**
   ```bash
   # Check API keys
   echo $OPENAI_API_KEY
   echo $ANTHROPIC_API_KEY
   
   # Try different model provider
   # In WebSocket: {"type": "create_agent", "config": {"model_provider": "copilot"}}
   ```

3. **Import Errors**
   ```bash
   # Install missing dependencies
   pip install websockets  # For WebSocket server
   pip install jupyter ipywidgets  # For notebook interface
   ```

### Debug Mode

Enable debug logging:
```bash
# Set environment variable
export RIPPR_DEBUG=true

# Or use Python logging
python -c "
import logging
logging.basicConfig(level=logging.DEBUG)
# Your code here
"
```

## 🎯 Best Practices

1. **For Real-time UIs**: Use WebSocket server with chunked streaming
2. **For Scripting**: Use Simple Python Interface
3. **For Experimentation**: Use Jupyter Notebook Interface
4. **For Quick Tasks**: Use CLI with streaming
5. **For Development**: Combine multiple interfaces as needed

## 🔮 Next Steps

1. **Build Custom Frontend**: Use the WebSocket protocol to build your ideal interface
2. **Integrate with IDE**: Create plugins for your favorite editor
3. **Automate Workflows**: Use the Python interface for automation scripts
4. **Experiment**: Use Jupyter notebooks for iterative development
5. **Share**: Create reusable interface components for your team
