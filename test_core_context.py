#!/usr/bin/env python3
"""
Focused test for core Context Intelligence components

Tests the working components without LSP dependencies.
"""

import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_context_intelligence_demo():
    """Run a comprehensive demo of the context intelligence system"""
    print("🎯 Context Intelligence Demo")
    print("=" * 50)
    
    try:
        from codegen import Codebase
        from context_intelligence import (
            ContextIntelligenceLayer,
            TaskClassifier,
            SymbolAnalyzer,
            EnhancedAgentTools
        )
        
        # Load codebase
        print("📁 Loading codebase...")
        codebase = Codebase(".")
        print(f"   ✅ Loaded {len(codebase.symbols)} symbols")
        
        # Initialize components
        print("\n🧠 Initializing Context Intelligence...")
        context_layer = ContextIntelligenceLayer(codebase)
        enhanced_tools = EnhancedAgentTools(codebase)
        print("   ✅ Context Intelligence initialized")
        
        # Demo scenarios
        scenarios = [
            "fix authentication bug",
            "add new user registration feature",
            "refactor database connection code",
            "find rate limiting implementation",
            "analyze error handling patterns"
        ]
        
        print(f"\n🤖 Testing {len(scenarios)} agent scenarios...")
        
        for i, scenario in enumerate(scenarios, 1):
            print(f"\n--- Scenario {i}: '{scenario}' ---")
            
            # Extract context
            context = context_layer.extract_context(scenario)
            
            print(f"📊 Analysis:")
            print(f"   Intent: {context.task.intent}")
            print(f"   Domain: {context.task.domain}")
            print(f"   Confidence: {context.confidence:.2f}")
            print(f"   Entities: {context.task.entities}")
            print(f"   Impact Scope: {context.impact_scope}")
            
            if context.relevant_symbols:
                print(f"   Relevant Symbols: {len(context.relevant_symbols)}")
                for symbol in context.relevant_symbols[:3]:
                    print(f"     • {symbol.symbol_name} ({symbol.domain})")
            
            if context.suggested_actions:
                print(f"   Suggested Actions:")
                for action in context.suggested_actions[:2]:
                    print(f"     • {action}")
            
            # Test enhanced search
            search_result = enhanced_tools.enhanced_search(scenario)
            print(f"🔍 Enhanced Search: {len(search_result)} characters")
            
            # Show a snippet of the search result
            lines = search_result.split('\n')
            for line in lines[:3]:
                if line.strip():
                    print(f"   {line}")
        
        print(f"\n✅ All {len(scenarios)} scenarios completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_performance_benchmarks():
    """Test performance characteristics"""
    print("\n⚡ Performance Benchmarks")
    print("=" * 30)
    
    try:
        import time
        from context_intelligence import TaskClassifier, ContextIntelligenceLayer
        from codegen import Codebase
        
        # Test task classification speed
        classifier = TaskClassifier()
        
        print("🧪 Task Classification Speed:")
        start_time = time.time()
        for i in range(100):
            classifier.analyze_task(f"fix bug number {i} in authentication")
        end_time = time.time()
        
        avg_time = (end_time - start_time) / 100 * 1000
        print(f"   Average: {avg_time:.2f}ms per analysis")
        print(f"   Target: <100ms ({'✅ PASS' if avg_time < 100 else '❌ FAIL'})")
        
        # Test context extraction speed
        codebase = Codebase(".")
        context_layer = ContextIntelligenceLayer(codebase)
        
        print("\n🧪 Context Extraction Speed:")
        test_queries = [
            "fix authentication bug",
            "add user feature",
            "refactor code",
            "find symbol",
            "analyze performance"
        ]
        
        start_time = time.time()
        for query in test_queries * 10:  # 50 total queries
            context_layer.extract_context(query)
        end_time = time.time()
        
        avg_time = (end_time - start_time) / 50 * 1000
        print(f"   Average: {avg_time:.2f}ms per extraction")
        print(f"   Target: <500ms ({'✅ PASS' if avg_time < 500 else '❌ FAIL'})")
        
        return True
        
    except Exception as e:
        print(f"❌ Performance test failed: {e}")
        return False

def test_real_world_scenarios():
    """Test with real-world agent scenarios"""
    print("\n🌍 Real-World Scenarios")
    print("=" * 30)
    
    try:
        from codegen import Codebase
        from context_intelligence import ContextIntelligenceLayer, EnhancedAgentTools
        
        codebase = Codebase(".")
        context_layer = ContextIntelligenceLayer(codebase)
        enhanced_tools = EnhancedAgentTools(codebase)
        
        # Real scenarios an agent might encounter
        real_scenarios = [
            {
                "query": "The user authentication is failing with a 401 error",
                "expected_intent": "fix_bug",
                "expected_domain": "authentication"
            },
            {
                "query": "I need to implement OAuth2 login functionality",
                "expected_intent": "add_feature", 
                "expected_domain": "authentication"
            },
            {
                "query": "Where is the database connection configured?",
                "expected_intent": "find_code",
                "expected_domain": "data_access"
            },
            {
                "query": "The API response time is too slow, need to optimize",
                "expected_intent": "refactor",
                "expected_domain": "api"
            }
        ]
        
        passed = 0
        total = len(real_scenarios)
        
        for i, scenario in enumerate(real_scenarios, 1):
            print(f"\n🧪 Scenario {i}: {scenario['query']}")
            
            context = context_layer.extract_context(scenario['query'])
            
            # Check intent
            intent_correct = context.task.intent == scenario['expected_intent']
            domain_correct = context.task.domain == scenario['expected_domain']
            
            print(f"   Intent: {context.task.intent} ({'✅' if intent_correct else '❌'})")
            print(f"   Domain: {context.task.domain} ({'✅' if domain_correct else '❌'})")
            print(f"   Confidence: {context.confidence:.2f}")
            
            if intent_correct and domain_correct:
                passed += 1
            
            # Test enhanced tools
            search_result = enhanced_tools.enhanced_search(scenario['query'])
            print(f"   Enhanced search: {len(search_result)} chars")
        
        print(f"\n📊 Results: {passed}/{total} scenarios classified correctly")
        return passed == total
        
    except Exception as e:
        print(f"❌ Real-world scenarios test failed: {e}")
        return False

def main():
    """Run focused tests"""
    print("🚀 Context Intelligence Focused Validation")
    print("=" * 60)
    
    tests = [
        ("Context Intelligence Demo", test_context_intelligence_demo),
        ("Performance Benchmarks", test_performance_benchmarks),
        ("Real-World Scenarios", test_real_world_scenarios)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "="*60)
    print("📊 Final Results")
    print("="*60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 Context Intelligence Layer is fully functional!")
        print("✨ Ready for autonomous agent integration")
    else:
        print(f"\n⚠️  {total - passed} test(s) failed")
    
    return 0 if passed == total else 1

if __name__ == "__main__":
    exit(main()) 