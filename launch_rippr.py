#!/usr/bin/env python3
"""
Rippr Launcher - Choose the right interface for your needs.

This script helps you select and launch the most appropriate Rippr interface
for your specific use case.
"""

import os
import sys
import subprocess
from pathlib import Path


def print_banner():
    """Print the Rippr banner."""
    print("""
🚀 Rippr Launcher
================

Choose the interface that best fits your needs:
""")


def print_options():
    """Print available interface options."""
    options = [
        {
            "key": "1",
            "name": "CLI Interactive (Default)",
            "description": "Standard command-line interface with interactive chat",
            "best_for": "Quick tasks, general usage",
            "command": "rippr"
        },
        {
            "key": "2", 
            "name": "CLI with Streaming",
            "description": "Command-line with real-time streaming output",
            "best_for": "Seeing agent thinking process",
            "command": "rippr --realtime-streaming"
        },
        {
            "key": "3",
            "name": "Simple Python Interface",
            "description": "Python-based interactive interface",
            "best_for": "Scripting, automation, multiple agents",
            "command": "rippr --simple-interface"
        },
        {
            "key": "4",
            "name": "WebSocket Server + HTML UI",
            "description": "Local WebSocket server with web interface",
            "best_for": "Custom frontends, real-time streaming",
            "command": "rippr --local-server"
        },
        {
            "key": "5",
            "name": "Jupyter Notebook",
            "description": "Interactive notebook with widgets",
            "best_for": "Experimentation, prototyping, analysis",
            "command": "jupyter notebook examples/local_notebook_interface.ipynb"
        },
        {
            "key": "6",
            "name": "REST API Server",
            "description": "Full REST API with authentication",
            "best_for": "Production frontends, external integrations",
            "command": "rippr --api-server"
        }
    ]
    
    for option in options:
        print(f"{option['key']}. {option['name']}")
        print(f"   📝 {option['description']}")
        print(f"   🎯 Best for: {option['best_for']}")
        print(f"   💻 Command: {option['command']}")
        print()
    
    return options


def get_user_choice(options):
    """Get user's choice."""
    while True:
        try:
            choice = input("Enter your choice (1-6) or 'q' to quit: ").strip().lower()
            
            if choice == 'q':
                print("👋 Goodbye!")
                sys.exit(0)
            
            # Find matching option
            for option in options:
                if option['key'] == choice:
                    return option
            
            print("❌ Invalid choice. Please enter 1-6 or 'q' to quit.")
            
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            sys.exit(0)
        except EOFError:
            print("\n👋 Goodbye!")
            sys.exit(0)


def check_dependencies(option):
    """Check if required dependencies are available."""
    if option['key'] == '4':  # WebSocket server
        try:
            import websockets
        except ImportError:
            print("❌ WebSocket server requires 'websockets' package")
            print("💡 Install with: pip install websockets")
            return False
    
    elif option['key'] == '5':  # Jupyter
        try:
            import jupyter
            import ipywidgets
        except ImportError:
            print("❌ Jupyter interface requires 'jupyter' and 'ipywidgets' packages")
            print("💡 Install with: pip install jupyter ipywidgets")
            return False
    
    elif option['key'] == '6':  # REST API
        try:
            import fastapi
            import uvicorn
        except ImportError:
            print("❌ REST API server requires 'fastapi' and 'uvicorn' packages")
            print("💡 Install with: pip install fastapi uvicorn")
            return False
    
    return True


def launch_interface(option):
    """Launch the selected interface."""
    print(f"\n🚀 Launching {option['name']}...")
    print(f"💻 Command: {option['command']}")
    
    # Check dependencies
    if not check_dependencies(option):
        return False
    
    # Special handling for different interfaces
    if option['key'] == '4':  # WebSocket server + HTML UI
        print("\n📋 Instructions:")
        print("1. WebSocket server will start on ws://localhost:8765")
        print("2. Open src/local_interface/local_ui.html in your browser")
        print("3. Or connect your custom frontend to the WebSocket")
        print("\nStarting server...")
        
    elif option['key'] == '5':  # Jupyter
        print("\n📋 Instructions:")
        print("1. Jupyter will open in your browser")
        print("2. Navigate to examples/local_notebook_interface.ipynb")
        print("3. Run the cells to start the interactive interface")
        print("\nStarting Jupyter...")
        
    elif option['key'] == '6':  # REST API
        print("\n📋 Instructions:")
        print("1. API server will start on http://localhost:8000")
        print("2. Visit http://localhost:8000/docs for interactive documentation")
        print("3. Use API key: rippr-dev-key-12345 (for development)")
        print("\nStarting API server...")
    
    # Execute the command
    try:
        if option['key'] == '5':  # Jupyter - special handling
            # Check if notebook exists
            notebook_path = Path("examples/local_notebook_interface.ipynb")
            if not notebook_path.exists():
                print(f"❌ Notebook not found: {notebook_path}")
                print("💡 Make sure you're running from the project root directory")
                return False
            
            subprocess.run(["jupyter", "notebook", str(notebook_path)], check=True)
        else:
            # Parse and execute command
            cmd_parts = option['command'].split()
            subprocess.run(cmd_parts, check=True)
            
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to launch interface: {e}")
        return False
    except FileNotFoundError:
        print(f"❌ Command not found: {option['command'].split()[0]}")
        print("💡 Make sure all dependencies are installed and the project is set up correctly")
        return False
    except KeyboardInterrupt:
        print("\n🛑 Interface stopped by user")
        return True
    
    return True


def show_help():
    """Show additional help information."""
    print("""
📚 Additional Information:

🔧 Setup:
   - Make sure you have the required API keys in your environment
   - Run 'pip install -e .' to install the project
   - Copy .env.example to .env and configure your settings

🔑 API Keys:
   - OPENAI_API_KEY for OpenAI models
   - ANTHROPIC_API_KEY for Anthropic models  
   - GITHUB_COPILOT_OAUTH_TOKEN for GitHub Copilot
   - CODEGEN_API_TOKEN for Codegen SDK (optional)

📖 Documentation:
   - docs/LOCAL_INTERFACES_GUIDE.md - Detailed guide for local interfaces
   - docs/API_SETUP_GUIDE.md - REST API documentation
   - examples/ - Example implementations

🆘 Troubleshooting:
   - Check that you're in the project root directory
   - Verify all dependencies are installed
   - Check API key configuration
   - See documentation for detailed troubleshooting
""")


def main():
    """Main launcher function."""
    print_banner()
    
    # Check if we're in the right directory
    if not Path("src").exists() or not Path("pyproject.toml").exists():
        print("❌ Please run this script from the Rippr project root directory")
        print("💡 The directory should contain 'src/' and 'pyproject.toml'")
        sys.exit(1)
    
    # Show options
    options = print_options()
    
    # Add help option
    print("h. Show additional help and setup information")
    print("q. Quit")
    print()
    
    while True:
        choice = input("Enter your choice (1-6, h for help, q to quit): ").strip().lower()
        
        if choice == 'q':
            print("👋 Goodbye!")
            break
        elif choice == 'h':
            show_help()
            continue
        
        # Find matching option
        selected_option = None
        for option in options:
            if option['key'] == choice:
                selected_option = option
                break
        
        if selected_option:
            success = launch_interface(selected_option)
            if success:
                print(f"\n✅ {selected_option['name']} completed successfully!")
            else:
                print(f"\n❌ Failed to launch {selected_option['name']}")
            
            # Ask if user wants to try another interface
            try_again = input("\nWould you like to try another interface? (y/n): ").strip().lower()
            if try_again != 'y':
                break
        else:
            print("❌ Invalid choice. Please enter 1-6, h for help, or q to quit.")


if __name__ == "__main__":
    main()
