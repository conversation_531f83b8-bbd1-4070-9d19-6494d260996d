#!/usr/bin/env python3
"""
Command-line interface for Rippr - A powerful code agent framework.
This module is the main entry point when installed as a package.
"""

import argparse
import os
import sys
from typing import Optional
from uuid import uuid4

# Try to import dotenv for environment variable management
try:
    from dotenv import load_dotenv
    load_dotenv()
    load_dotenv(".env.local")
except ImportError:
    pass

def main():
    """Main CLI entry point."""
    parser = argparse.ArgumentParser(
        description="Rippr - Interactive CLI for code agents",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  rippr                                         # Start interactive chat mode
  rippr --test                                  # Test CLI initialization without starting interactive mode
  rippr --command "analyze this codebase"       # Run a single command
  rippr --agent code --model copilot gpt-4.1    # Use code agent with copilot
  rippr --path /path/to/project                 # Specify codebase path
        """
    )
    
    parser.add_argument(
        "--path", "-p",
        default=".",
        help="Path to the codebase (default: current directory)"
    )
    
    parser.add_argument(
        "--agent", "-a",
        choices=["chat", "code"],
        default="chat",
        help="Type of agent to use (default: chat)"
    )
    
    parser.add_argument(
        "--model-provider", "-mp",
        choices=["vertex", "google", "openai", "anthropic", "copilot"],
        default="copilot",
        help="Model provider to use (default: copilot)"
    )
    
    parser.add_argument(
        "--model-name", "-mn",
        default="gpt-4.1",
        help="Model name to use (default: gpt-4.1)"
    )
    
    parser.add_argument(
        "--command", "-c",
        help="Run a single command and exit (non-interactive mode)"
    )
    
    parser.add_argument(
        "--debug",
        action="store_true",
        help="Enable debug mode"
    )
    
    parser.add_argument(
        "--test",
        action="store_true",
        help="Test CLI initialization and exit (useful for debugging)"
    )
    
    parser.add_argument(
        "--interactive",
        action="store_true",
        default=True,
        help="Use interactive system message for more conversational experience (default: True)"
    )
    
    parser.add_argument(
        "--standard",
        action="store_true",
        help="Use standard system message instead of interactive mode"
    )
    
    args = parser.parse_args()
    
    # Import here to avoid circular imports and speed up initial CLI loading
    try:
        from src.cli import RipprCLI
        
        # Determine interactive mode
        interactive_mode = args.interactive and not args.standard
        
        # Initialize CLI
        cli = RipprCLI(
            codebase_path=args.path,
            model_provider=args.model_provider,
            model_name=args.model_name
        )
        
        # Create agent
        cli.create_agent(args.agent, interactive=interactive_mode)
        
        # Run in appropriate mode
        if args.test:
            print("✅ CLI test completed successfully!")
            print("🎯 Interactive mode is ready to use. Run without --test to start chatting.")
        elif args.command:
            cli.run_single_command(args.command)
        else:
            cli.interactive_chat()
    
    except ImportError as e:
        print(f"❌ Error: {e}")
        print("💡 Make sure rippr is installed correctly.")
        print("💡 Try: pip install -e .")
        sys.exit(1)


if __name__ == "__main__":
    main()