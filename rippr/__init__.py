"""
Rippr - A powerful code agent framework.
This package provides an interface to advanced AI-powered coding assistants.
"""

__version__ = "0.1.0"

# Import and expose key classes and functions for easy access
try:
    # Import core modules from src for backward compatibility
    from src.agents.chat_agent import ChatAgent
    from src.agents.code_agent import CodeAgent
    from src.langchain.agent import create_codebase_agent
    from src.copilot.client import Copilot<PERSON>hat
    
    # Define what's available via rippr.module
    from src.agents import agent, chat_agent, code_agent, task_agent
    from src.langchain import agent as langchain_agent, llm, prompts, tools
    from src.mcp import codebase_agent, codebase_tools
    from src.tools import (
        apply_patch, bash, create_file, edit_file, list_directory, 
        search, view_file, graph_search, reveal_symbol, reflection
    )
except ImportError as e:
    import warnings
    warnings.warn(f"Some modules could not be imported: {e}")

__all__ = [
    "__version__",
    "ChatAgent",
    "CodeAgent",
    "CopilotChat",
    "create_codebase_agent",
]