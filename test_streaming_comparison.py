#!/usr/bin/env python3
"""
Test script to demonstrate the difference between buffered and real-time streaming.
"""

import os
import sys

# Add the project root to the Python path
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

try:
    from dotenv import load_dotenv
    load_dotenv()
    load_dotenv(".env.local")
except ImportError:
    pass

from codegen import Codebase
from src.agents.chat_agent import ChatAgent

def test_streaming_modes():
    """Test both streaming modes to show the difference."""
    print("🚀 Initializing test...")
    
    # Load codebase
    codebase = Codebase(".")
    
    # Create agent
    agent = ChatAgent(
        codebase,
        model_provider="vertex",
        model_name="gemini-2.5-pro-preview-05-06",
        memory=True,
        condensed_logging=True,
        short_format=True
    )
    
    test_command = "list the files in the src directory"
    
    print("\n" + "="*60)
    print("🐌 BUFFERED MODE (old behavior)")
    print("="*60)
    print("Notice how everything appears at once at the end...")
    
    # Test with buffered mode (stream_output=False)
    response1 = agent.run(test_command, stream_output=False)
    print(f"Final result: {response1}")
    
    print("\n" + "="*60)
    print("⚡ REAL-TIME STREAMING MODE (new behavior)")
    print("="*60)
    print("Notice how tool calls and responses appear immediately...")
    
    # Test with real-time streaming (stream_output=True)
    response2 = agent.run(test_command, stream_output=True)
    
    print("\n" + "="*60)
    print("✅ Test completed!")
    print("="*60)
    print("The difference:")
    print("• Buffered mode: All output appears at once after processing")
    print("• Real-time mode: Tool calls and responses appear immediately")
    print("• Real-time mode provides better user experience and feedback")

if __name__ == "__main__":
    test_streaming_modes() 