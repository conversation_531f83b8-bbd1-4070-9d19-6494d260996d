#!/usr/bin/env python3
"""Test script to verify Vertex AI configuration."""

import os
import sys
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from dotenv import load_dotenv
    load_dotenv()
    load_dotenv(".env.local")
except ImportError:
    pass

from src.langchain.llm import LLM

def test_vertex_ai():
    """Test Vertex AI configuration."""
    print("🧪 Testing Vertex AI configuration...")
    
    # Check environment variables
    print(f"GOOGLE_APPLICATION_CREDENTIALS: {os.getenv('GOOGLE_APPLICATION_CREDENTIALS')}")
    print(f"VERTEX_PROJECT: {os.getenv('VERTEX_PROJECT')}")
    print(f"VERTEX_LOCATION: {os.getenv('VERTEX_LOCATION')}")
    
    try:
        # Create LLM instance
        llm = LLM(
            model_provider="vertex",
            model_name="gemini-2.0-flash-001",
            temperature=0.1,
            max_tokens=100
        )
        print("✅ LLM instance created successfully")
        
        # Test a simple completion
        from langchain_core.messages import HumanMessage
        messages = [HumanMessage(content="Hello, can you say 'test successful'?")]
        
        print("🚀 Testing completion...")
        response = llm.invoke(messages)
        print(f"✅ Response: {response.content}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_vertex_ai()
    sys.exit(0 if success else 1) 