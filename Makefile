.PHONY: setup install test test-cov test-verbose lint format clean dist check help dev

PYTHON = python3
UV = uv
PIP = $(UV) pip

help:
	@echo "Available commands:"
	@echo "  setup       - Create virtual environment and install dependencies"
	@echo "  install     - Install the package in development mode"
	@echo "  test        - Run tests using pytest"
	@echo "  test-cov    - Run tests with coverage report"
	@echo "  test-verbose - Run tests in verbose mode"
	@echo "  lint        - Run linting using ruff"
	@echo "  format      - Format code using ruff"
	@echo "  clean       - Clean up temporary files and build artifacts"
	@echo "  dist        - Build distribution packages"
	@echo "  check       - Run all checks (lint, test)"
	@echo "  dev         - Quick install for development"

setup:
	$(UV) venv .venv

install:
	$(PIP) install -e ".[dev]"

dev: install
	@echo "Development environment set up successfully!"

test:
	$(PYTHON) -m pytest

test-cov:
	$(PYTHON) -m pytest --cov=src --cov=rippr --cov-report=term --cov-report=html

test-verbose:
	$(PYTHON) -m pytest -vv

lint:
	$(PYTHON) -m ruff check .

format:
	$(PYTHON) -m ruff format .

check: lint test
	@echo "All checks passed!"

dist:
	$(PYTHON) -m build

clean:
	rm -rf .pytest_cache .coverage .mypy_cache .ruff_cache
	rm -rf dist build *.egg-info
	find . -type d -name __pycache__ -exec rm -rf {} +
	find . -type f -name "*.pyc" -delete