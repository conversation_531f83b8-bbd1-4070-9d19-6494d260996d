# This file was autogenerated by uv via the following command:
#    uv pip compile pyproject.toml -o uv.lock
annotated-types==0.7.0
    # via pydantic
anthropic==0.51.0
    # via rippr (pyproject.toml)
anyio==4.9.0
    # via
    #   anthropic
    #   httpx
    #   openai
cachetools==5.5.2
    # via google-auth
certifi==2025.4.26
    # via
    #   httpcore
    #   httpx
    #   requests
charset-normalizer==3.4.2
    # via requests
distro==1.9.0
    # via
    #   anthropic
    #   openai
google-ai-generativelanguage==0.6.15
    # via google-generativeai
google-api-core==2.24.2
    # via
    #   google-ai-generativelanguage
    #   google-api-python-client
    #   google-generativeai
google-api-python-client==2.169.0
    # via google-generativeai
google-auth==2.40.2
    # via
    #   google-ai-generativelanguage
    #   google-api-core
    #   google-api-python-client
    #   google-auth-httplib2
    #   google-generativeai
google-auth-httplib2==0.2.0
    # via google-api-python-client
google-generativeai==0.8.5
    # via rippr (pyproject.toml)
googleapis-common-protos==1.70.0
    # via
    #   google-api-core
    #   grpcio-status
grpcio==1.71.0
    # via
    #   google-api-core
    #   grpcio-status
grpcio-status==1.71.0
    # via google-api-core
h11==0.16.0
    # via httpcore
httpcore==1.0.9
    # via httpx
httplib2==0.22.0
    # via
    #   google-api-python-client
    #   google-auth-httplib2
httpx==0.28.1
    # via
    #   anthropic
    #   langgraph-sdk
    #   langsmith
    #   openai
idna==3.10
    # via
    #   anyio
    #   httpx
    #   requests
jiter==0.10.0
    # via
    #   anthropic
    #   openai
jsonpatch==1.33
    # via langchain-core
jsonpointer==3.0.0
    # via jsonpatch
langchain==0.3.25
    # via rippr (pyproject.toml)
langchain-core==0.3.60
    # via
    #   rippr (pyproject.toml)
    #   langchain
    #   langchain-openai
    #   langchain-text-splitters
    #   langgraph
    #   langgraph-checkpoint
    #   langgraph-prebuilt
langchain-openai==0.3.17
    # via rippr (pyproject.toml)
langchain-text-splitters==0.3.8
    # via langchain
langgraph==0.4.5
    # via rippr (pyproject.toml)
langgraph-checkpoint==2.0.26
    # via
    #   langgraph
    #   langgraph-prebuilt
langgraph-prebuilt==0.1.8
    # via
    #   rippr (pyproject.toml)
    #   langgraph
langgraph-sdk==0.1.70
    # via langgraph
langsmith==0.3.42
    # via
    #   rippr (pyproject.toml)
    #   langchain
    #   langchain-core
openai==1.81.0
    # via
    #   rippr (pyproject.toml)
    #   langchain-openai
orjson==3.10.18
    # via
    #   langgraph-sdk
    #   langsmith
ormsgpack==1.9.1
    # via langgraph-checkpoint
packaging==24.2
    # via
    #   langchain-core
    #   langsmith
proto-plus==1.26.1
    # via
    #   google-ai-generativelanguage
    #   google-api-core
protobuf==5.29.4
    # via
    #   google-ai-generativelanguage
    #   google-api-core
    #   google-generativeai
    #   googleapis-common-protos
    #   grpcio-status
    #   proto-plus
pyasn1==0.6.1
    # via
    #   pyasn1-modules
    #   rsa
pyasn1-modules==0.4.2
    # via google-auth
pydantic==2.11.4
    # via
    #   rippr (pyproject.toml)
    #   anthropic
    #   google-generativeai
    #   langchain
    #   langchain-core
    #   langgraph
    #   langsmith
    #   openai
pydantic-core==2.33.2
    # via pydantic
pyparsing==3.2.3
    # via httplib2
pyyaml==6.0.2
    # via
    #   langchain
    #   langchain-core
regex==2024.11.6
    # via tiktoken
requests==2.32.3
    # via
    #   google-api-core
    #   langchain
    #   langsmith
    #   requests-toolbelt
    #   tiktoken
requests-toolbelt==1.0.0
    # via langsmith
rsa==4.9.1
    # via google-auth
sniffio==1.3.1
    # via
    #   anthropic
    #   anyio
    #   openai
sqlalchemy==2.0.41
    # via langchain
tenacity==9.1.2
    # via langchain-core
tiktoken==0.9.0
    # via
    #   rippr (pyproject.toml)
    #   langchain-openai
tqdm==4.67.1
    # via
    #   google-generativeai
    #   openai
typing-extensions==4.13.2
    # via
    #   anthropic
    #   anyio
    #   google-generativeai
    #   langchain-core
    #   openai
    #   pydantic
    #   pydantic-core
    #   sqlalchemy
    #   typing-inspection
typing-inspection==0.4.1
    # via pydantic
uritemplate==4.1.1
    # via google-api-python-client
urllib3==2.4.0
    # via requests
xxhash==3.5.0
    # via langgraph
zstandard==0.23.0
    # via langsmith
