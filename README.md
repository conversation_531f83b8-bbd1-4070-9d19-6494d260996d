# Rippr

A powerful code agent framework for intelligent programming assistance with advanced AI model integration.

## What is <PERSON><PERSON><PERSON><PERSON>?

Rippr is an innovative framework designed to empower developers with intelligent, AI-driven code agents. It provides a robust and extensible platform for automating various programming tasks, enhancing code quality, and streamlining development workflows. By leveraging advanced AI capabilities, multiple model providers, and a comprehensive tool-based approach, Rippr significantly boosts developer productivity and code maintainability across diverse programming languages.

## 🚀 Recent Updates

### Advanced GitHub Copilot Integration
- **Claude 3.7 Sonnet Support**: Full integration with Claude 3.7 Sonnet via GitHub Copilot API
- **Automatic Error Recovery**: Intelligent 500 error fallback mechanism for seamless tool usage
- **Enhanced Authentication**: Streamlined OAuth flow with automatic token management
- **Robust Tool Support**: Comprehensive tool binding with automatic fallback for unsupported operations

### Multi-Model Provider Support
- **GitHub Copilot**: Claude 3.7 Sonnet, Claude 3.5 Sonnet, GPT-4 models
- **Google AI**: Gemini 2.5 Flash Preview and other Gemini models
- **Anthropic**: Direct Claude model access
- **OpenAI**: GPT-4o, GPT-4 Turbo, and other OpenAI models
- **Vertex AI**: Enterprise-grade AI through LiteLLM integration (Gemini, Claude, and more)

### Quick Start
```bash
# Install the package
pip install -e .

# Run with GitHub Copilot (recommended)
rippr

# Run with a specific model provider
rippr --model-provider google --model-name gemini-2.5-flash-preview-05-20

# Run in code agent mode
rippr --agent code
```

## Motivation

In the rapidly evolving landscape of software development, developers often face challenges related to code quality, repetitive tasks, and efficient code manipulation. Rippr aims to address these challenges by providing a robust and extensible framework for building intelligent code agents. It leverages advanced AI capabilities and a tool-based approach to automate and streamline various programming tasks, ultimately enhancing developer productivity and code maintainability.

## ✨ Features

### 🤖 Advanced AI Integration
- **Multi-Model Support**: Seamlessly switch between GitHub Copilot, Google AI, Anthropic, and OpenAI models
- **Intelligent Error Recovery**: Automatic fallback mechanisms ensure continuous operation even when specific model features fail
- **Context-Aware Code Generation**: Generate high-quality, context-aware code snippets, functions, or entire modules
- **Smart Tool Selection**: Adaptive tool usage based on model capabilities and task requirements

### 🛠️ Comprehensive Tool Ecosystem
- **File Operations**: Create, edit, delete, rename, and move files with intelligent conflict resolution
- **Code Analysis**: Search, analyze, and understand codebases with semantic understanding
- **Git Integration**: Automated commit creation, branch management, and pull request workflows
- **GitHub Integration**: Native GitHub API integration for issues, PRs, and repository management
- **Bash Execution**: Safe command execution with proper error handling and logging

### 🏗️ Architecture & Integration
- **Language Server Protocol (LSP)**: Rich IDE integration with auto-completion, diagnostics, and refactoring
- **MCP (Model Context Protocol)**: Extensible server architecture for AI agent capabilities
- **LangChain Framework**: Advanced AI orchestration with memory management and conversation flow
- **Modular Design**: Easily extensible agent architecture for custom task automation
- **Multi-Language Support**: Language-agnostic design supporting Python, JavaScript, and more

### 🔧 Developer Experience
- **Condensed Logging**: Clean, readable output with detailed debugging when needed
- **Flexible Configuration**: Easy model switching and parameter tuning
- **Comprehensive Testing**: Extensive test suite ensuring reliability across all integrations
- **Documentation**: Clear examples and setup instructions for all supported workflows

## 📦 Installation

### Prerequisites
- Python 3.12 or higher
- Git
- GitHub account (for Copilot integration)

### Setup

#### Option 1: Install from PyPI (coming soon)
```bash
pip install rippr
```

#### Option 2: Install from source
```bash
# Clone the repository
git clone https://github.com/H0ARK/rippr.git
cd rippr

# Install using the setup script (recommended)
chmod +x setup.sh
./setup.sh

# Or install manually
pip install -e .  # Install in development mode
# OR
pip install .     # Install as a regular package
```

#### Option 3: Install with development dependencies
```bash
pip install -e ".[dev]"  # Includes testing and development tools
```

### Environment Variables
You can set the following environment variables for API access:
```bash
# Create a .env file or set in your environment
export GITHUB_COPILOT_OAUTH_TOKEN="your_token_here"
export GOOGLE_API_KEY="your_google_api_key"
export ANTHROPIC_API_KEY="your_anthropic_key"
export OPENAI_API_KEY="your_openai_key"
```

## 🚀 Usage Examples

### Command Line Interface
```bash
# Start interactive chat mode with default settings
rippr

# Use a specific agent type and model
rippr --agent code --model-provider openai --model-name gpt-4o

# Run a single command without interactive mode
rippr --command "Create a function to parse JSON files"

# Analyze a specific codebase
rippr --path /path/to/your/project

# Start the API server
rippr --api-server
```

### API Server

Rippr now includes a comprehensive REST API that allows any frontend to connect to your agents:

```bash
# Start the API server
rippr-api

# Or with custom settings
rippr-api --host 0.0.0.0 --port 8000 --reload

# Access the interactive API documentation
open http://localhost:8000/docs
```

**Key API Features:**
- 🔧 **Agent Management**: Create and manage different agent types
- 💬 **Chat Interface**: Send messages with streaming support
- 🛠️ **Tool Execution**: Direct access to codebase tools
- 📁 **File Operations**: CRUD operations on files
- 🔍 **Search Operations**: Search across the codebase
- 🔌 **WebSocket Support**: Real-time streaming responses
- 🔐 **Authentication**: API key-based security
- ⚡ **Rate Limiting**: Configurable request limits

**Quick API Example:**
```bash
# Create an agent
curl -X POST "http://localhost:8000/api/v1/agents/" \
  -H "Authorization: Bearer rippr-dev-key-12345" \
  -H "Content-Type: application/json" \
  -d '{"name": "My Agent", "config": {"agent_type": "chat", "model_provider": "copilot"}}'

# Send a message
curl -X POST "http://localhost:8000/api/v1/chat/{agent_id}" \
  -H "Authorization: Bearer rippr-dev-key-12345" \
  -H "Content-Type: application/json" \
  -d '{"message": "Analyze this codebase"}'
```

See the [API Setup Guide](docs/API_SETUP_GUIDE.md) for complete documentation.

### Basic Code Agent
```python
from rippr import CodeAgent
from codegen import Codebase

# Initialize codebase
codebase = Codebase.from_repo("your-org/your-repo", language="python")

# Create agent with GitHub Copilot
agent = CodeAgent(
    codebase,
    model_provider="copilot",
    model_name="claude-3.7-sonnet",
    condensed_logging=True
)

# Run a task
result = agent.run("Update the README.md with current project status")
print(result)
```

### Multi-Model Fallback
```python
from rippr import create_codebase_agent
from codegen import Codebase

codebase = Codebase(repo_path="./", language="python")

# Try GitHub Copilot first, fallback to Google AI
try:
    agent = create_codebase_agent(
        codebase,
        model_provider="copilot",
        model_name="claude-3.7-sonnet"
    )
except Exception:
    agent = create_codebase_agent(
        codebase,
        model_provider="google",
        model_name="gemini-2.5-flash-preview-05-20"
    )
```

### GitHub Copilot Direct Usage
```python
from rippr import CopilotChat
from langchain_core.messages import HumanMessage

# Initialize Copilot client
copilot = CopilotChat(model_name="claude-3.7-sonnet")

# Simple chat
response = copilot.invoke([
    HumanMessage(content="Explain the benefits of type hints in Python")
])
print(response.content)
```