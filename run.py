#!/usr/bin/env python3
"""
Run script for Rippr project - can be executed from any directory

This is maintained for backward compatibility.
For new usage, please use the installed 'rippr' command.
"""

import os
import sys

print("\033[93mWarning: This script is deprecated. Please use 'rippr' command after installing the package.\033[0m")
print("If rippr is not installed, run: pip install -e .\n")

# Add the project root directory to the Python path
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

try:
    # Import the main CLI function and run it
    from rippr.cli import main
    main()
except ImportError:
    # Fallback to old method if package is not properly installed
    print("Using legacy mode (not recommended)...")
    from src.cli import main as cli_main
    cli_main()