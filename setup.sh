#!/bin/bash
set -e

# Define colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== Setting up Rippr development environment ===${NC}"

# Check if uv is installed
if ! command -v uv &> /dev/null; then
    echo -e "${RED}uv is not installed. Installing...${NC}"
    curl -fsSL https://install.uv.dev | sh
    echo -e "${GREEN}uv installed successfully!${NC}"
fi

# Create and activate virtual environment
echo -e "${BLUE}Creating virtual environment...${NC}"
uv venv .venv

# Determine the correct activation command based on OS
if [[ "$OSTYPE" == "darwin"* ]] || [[ "$OSTYPE" == "linux-gnu"* ]]; then
    ACTIVATE_CMD="source .venv/bin/activate"
elif [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "win32" ]]; then
    ACTIVATE_CMD=".venv\\Scripts\\activate"
else
    echo -e "${RED}Unsupported OS type: $OSTYPE${NC}"
    exit 1
fi

echo -e "${BLUE}Installing dependencies...${NC}"
eval "$ACTIVATE_CMD" && uv pip install -e ".[dev]"

echo -e "${GREEN}Setup complete! To activate the environment, run:${NC}"
echo -e "${BLUE}$ACTIVATE_CMD${NC}"