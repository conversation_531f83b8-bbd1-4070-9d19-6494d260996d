#!/usr/bin/env python3
"""
LSP Autonomous Agent Integration Demo

Demonstrates how autonomous agents can leverage the enhanced LSP server
with context intelligence for semantic code understanding.
"""

import sys
import os
import asyncio
import json
from typing import Dict, Any

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from codegen.sdk.core.codebase import Codebase
from lsp.enhanced_server import EnhancedCodegenLanguageServer, AgentLSPClient
from context_intelligence import ContextIntelligence<PERSON>ayer, EnhancedAgentTools


class AutonomousAgentDemo:
    """
    Demonstrates how an autonomous agent can use the enhanced LSP server
    to get semantic context for code understanding and manipulation.
    """
    
    def __init__(self):
        self.codebase = None
        self.lsp_server = None
        self.agent_client = None
        self.context_layer = None
        self.enhanced_tools = None
    
    async def initialize(self):
        """Initialize the demo environment"""
        print("🚀 LSP Autonomous Agent Integration Demo")
        print("=" * 60)
        
        # Initialize codebase
        try:
            print("📁 Loading codebase...")
            self.codebase = Codebase(repo_path=".")
            print(f"✅ Loaded codebase with {len(self.codebase.symbols)} symbols")
        except Exception as e:
            print(f"❌ Failed to load codebase: {e}")
            return False
        
        # Initialize enhanced LSP server
        print("🔧 Initializing enhanced LSP server...")
        self.lsp_server = EnhancedCodegenLanguageServer("demo-server", "v1.0")
        self.lsp_server.codebase = self.codebase
        self.lsp_server.initialize_context_intelligence(self.codebase)
        
        # Create agent client
        self.agent_client = AgentLSPClient(self.lsp_server)
        
        # Initialize direct context tools for comparison
        self.context_layer = ContextIntelligenceLayer(self.codebase)
        self.enhanced_tools = EnhancedAgentTools(self.codebase)
        
        print("✅ Enhanced LSP server initialized")
        return True
    
    async def demo_agent_workflows(self):
        """Demonstrate various agent workflows using LSP context intelligence"""
        
        print("\n" + "=" * 60)
        print("🤖 AUTONOMOUS AGENT WORKFLOWS")
        print("=" * 60)
        
        # Workflow 1: Bug Investigation
        await self.demo_bug_investigation()
        
        # Workflow 2: Feature Implementation
        await self.demo_feature_implementation()
        
        # Workflow 3: Code Refactoring
        await self.demo_code_refactoring()
        
        # Workflow 4: Code Discovery
        await self.demo_code_discovery()
    
    async def demo_bug_investigation(self):
        """Demo: Agent investigating a bug"""
        print("\n🐛 WORKFLOW 1: Bug Investigation")
        print("-" * 40)
        
        # Agent receives bug report
        bug_report = "User authentication is failing with 500 error"
        print(f"🎯 Agent Task: {bug_report}")
        
        # Step 1: Analyze the task using LSP
        print("\n📊 Step 1: Analyzing task context...")
        task_analysis = self.agent_client.analyze_task(bug_report, "fix_bug")
        
        print(f"   Intent: {task_analysis.get('task', {}).get('intent', 'unknown')}")
        print(f"   Domain: {task_analysis.get('task', {}).get('domain', 'unknown')}")
        print(f"   Confidence: {task_analysis.get('task', {}).get('confidence', 0):.2f}")
        
        # Step 2: Search for relevant code
        print("\n🔍 Step 2: Searching for authentication code...")
        search_results = self.agent_client.enhanced_search("authentication error 500")
        print("   Search Results:")
        print("   " + "\\n   ".join(search_results.split("\\n")[:5]))
        
        # Step 3: Analyze specific symbols
        if task_analysis.get('relevant_symbols'):
            symbol = task_analysis['relevant_symbols'][0]
            symbol_name = symbol.get('name', 'unknown')
            print(f"\n🔬 Step 3: Analyzing symbol '{symbol_name}'...")
            symbol_analysis = self.agent_client.get_symbol_context(symbol_name)
            print("   Symbol Analysis:")
            print("   " + "\\n   ".join(symbol_analysis.split("\\n")[:5]))
        
        print("\n✅ Bug investigation workflow complete!")
    
    async def demo_feature_implementation(self):
        """Demo: Agent implementing a new feature"""
        print("\n🆕 WORKFLOW 2: Feature Implementation")
        print("-" * 40)
        
        # Agent receives feature request
        feature_request = "Add rate limiting to API endpoints"
        print(f"🎯 Agent Task: {feature_request}")
        
        # Step 1: Analyze the task
        print("\n📊 Step 1: Analyzing feature requirements...")
        task_analysis = self.agent_client.analyze_task(feature_request, "add_feature")
        
        print(f"   Intent: {task_analysis.get('task', {}).get('intent', 'unknown')}")
        print(f"   Domain: {task_analysis.get('task', {}).get('domain', 'unknown')}")
        print(f"   Impact Scope: {task_analysis.get('impact_scope', 'unknown')}")
        
        # Step 2: Find integration points
        print("\n🔗 Step 2: Finding API integration points...")
        api_search = self.agent_client.enhanced_search("API endpoint route")
        print("   Integration Points:")
        print("   " + "\\n   ".join(api_search.split("\\n")[:4]))
        
        # Step 3: Check for existing patterns
        print("\n🔍 Step 3: Looking for existing rate limiting patterns...")
        pattern_search = self.agent_client.enhanced_search("rate limit throttle")
        print("   Existing Patterns:")
        print("   " + "\\n   ".join(pattern_search.split("\\n")[:4]))
        
        print("\n✅ Feature implementation analysis complete!")
    
    async def demo_code_refactoring(self):
        """Demo: Agent refactoring code"""
        print("\n🔄 WORKFLOW 3: Code Refactoring")
        print("-" * 40)
        
        # Agent receives refactoring task
        refactor_task = "Refactor database query methods for better performance"
        print(f"🎯 Agent Task: {refactor_task}")
        
        # Step 1: Analyze refactoring scope
        print("\n📊 Step 1: Analyzing refactoring scope...")
        task_analysis = self.agent_client.analyze_task(refactor_task, "refactor")
        
        print(f"   Intent: {task_analysis.get('task', {}).get('intent', 'unknown')}")
        print(f"   Domain: {task_analysis.get('task', {}).get('domain', 'unknown')}")
        print(f"   Impact Scope: {task_analysis.get('impact_scope', 'unknown')}")
        
        # Step 2: Find database-related code
        print("\n🗄️ Step 2: Finding database query methods...")
        db_search = self.agent_client.enhanced_search("database query method")
        print("   Database Code:")
        print("   " + "\\n   ".join(db_search.split("\\n")[:4]))
        
        # Step 3: Analyze dependencies and impact
        print("\n⚠️ Step 3: Analyzing refactoring impact...")
        if task_analysis.get('relevant_symbols'):
            symbol = task_analysis['relevant_symbols'][0]
            symbol_name = symbol.get('name', 'unknown')
            impact_analysis = self.agent_client.get_symbol_context(symbol_name)
            print("   Impact Analysis:")
            print("   " + "\\n   ".join(impact_analysis.split("\\n")[:4]))
        
        print("\n✅ Refactoring analysis complete!")
    
    async def demo_code_discovery(self):
        """Demo: Agent discovering and understanding code"""
        print("\n🔍 WORKFLOW 4: Code Discovery")
        print("-" * 40)
        
        # Agent needs to understand codebase
        discovery_task = "Where is user management functionality implemented?"
        print(f"🎯 Agent Task: {discovery_task}")
        
        # Step 1: Analyze the discovery query
        print("\n📊 Step 1: Analyzing discovery query...")
        task_analysis = self.agent_client.analyze_task(discovery_task, "find_code")
        
        print(f"   Intent: {task_analysis.get('task', {}).get('intent', 'unknown')}")
        print(f"   Domain: {task_analysis.get('task', {}).get('domain', 'unknown')}")
        print(f"   Entities: {task_analysis.get('task', {}).get('entities', [])}")
        
        # Step 2: Search for user management code
        print("\n👤 Step 2: Searching for user management code...")
        user_search = self.agent_client.enhanced_search("user management UserService")
        print("   User Management Code:")
        print("   " + "\\n   ".join(user_search.split("\\n")[:5]))
        
        # Step 3: Explore related files
        print("\n📁 Step 3: Exploring related files...")
        if task_analysis.get('related_files'):
            print("   Related Files:")
            for file_path in task_analysis.get('related_files', [])[:5]:
                print(f"   • {file_path}")
        
        print("\n✅ Code discovery complete!")
    
    async def demo_performance_comparison(self):
        """Compare performance with and without context intelligence"""
        print("\n" + "=" * 60)
        print("⚡ PERFORMANCE COMPARISON")
        print("=" * 60)
        
        import time
        
        test_query = "find authentication code"
        
        # Test without context intelligence (basic search)
        print("\n🔍 Basic Search (without context intelligence):")
        start_time = time.time()
        basic_result = f"Basic search results for: {test_query}"
        basic_time = (time.time() - start_time) * 1000
        print(f"   Time: {basic_time:.1f}ms")
        print(f"   Result: {basic_result}")
        
        # Test with context intelligence
        print("\n🧠 Enhanced Search (with context intelligence):")
        start_time = time.time()
        enhanced_result = self.agent_client.enhanced_search(test_query)
        enhanced_time = (time.time() - start_time) * 1000
        print(f"   Time: {enhanced_time:.1f}ms")
        print("   Result:")
        print("   " + "\\n   ".join(enhanced_result.split("\\n")[:3]))
        
        # Performance analysis
        print(f"\n📊 Performance Analysis:")
        print(f"   Enhanced search overhead: {enhanced_time - basic_time:.1f}ms")
        print(f"   Context intelligence provides:")
        print(f"   • Semantic understanding")
        print(f"   • Domain classification") 
        print(f"   • Impact analysis")
        print(f"   • Suggested actions")
    
    async def demo_lsp_commands(self):
        """Demonstrate LSP command interface for agents"""
        print("\n" + "=" * 60)
        print("📡 LSP COMMAND INTERFACE")
        print("=" * 60)
        
        # Simulate LSP command calls that agents would make
        print("\n🔧 Available LSP Commands for Agents:")
        commands = [
            "codegen.analyzeQuery",
            "codegen.enhancedSearch",
            "codegen.symbolAnalysis", 
            "codegen.getContext"
        ]
        
        for cmd in commands:
            print(f"   • {cmd}")
        
        # Demo command usage
        print("\n📞 Example Command Usage:")
        
        # Command 1: Analyze Query
        print("\\n1. codegen.analyzeQuery")
        if self.lsp_server.context_commands:
            result = self.lsp_server.context_commands.analyze_query(["fix authentication bug", "fix_bug"])
            print(f"   Result: {result.get('task', {}).get('intent', 'unknown')} in {result.get('task', {}).get('domain', 'unknown')} domain")
        
        # Command 2: Enhanced Search
        print("\\n2. codegen.enhancedSearch")
        if self.lsp_server.context_commands:
            result = self.lsp_server.context_commands.enhanced_search(["authentication", 5])
            print(f"   Result: {len(result)} characters of enhanced search results")
        
        print("\n✅ LSP command interface demo complete!")
    
    async def run_demo(self):
        """Run the complete demo"""
        if not await self.initialize():
            print("❌ Demo initialization failed")
            return
        
        try:
            # Run all demo workflows
            await self.demo_agent_workflows()
            await self.demo_performance_comparison()
            await self.demo_lsp_commands()
            
            print("\n" + "=" * 60)
            print("🎉 DEMO COMPLETE!")
            print("=" * 60)
            
            print("\\n🚀 Key Benefits Demonstrated:")
            print("   • Semantic context awareness for agents")
            print("   • Domain-specific code understanding")
            print("   • Impact analysis for safer operations")
            print("   • Enhanced search with context intelligence")
            print("   • LSP protocol integration for standard tooling")
            print("   • Zero agent training required")
            
            print("\\n🔮 Future Capabilities:")
            print("   • Real-time context updates")
            print("   • Multi-agent coordination")
            print("   • Cross-repository context")
            print("   • Machine learning integration")
            
        except Exception as e:
            print(f"❌ Demo error: {e}")
            import traceback
            traceback.print_exc()


async def main():
    """Main demo entry point"""
    demo = AutonomousAgentDemo()
    await demo.run_demo()


if __name__ == "__main__":
    asyncio.run(main())

