<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rippr API Demo</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .header {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .config-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #555;
        }
        input, select, textarea {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .chat-container {
            height: 400px;
            border: 1px solid #ddd;
            border-radius: 4px;
            overflow-y: auto;
            padding: 15px;
            background: #fafafa;
            margin-bottom: 15px;
        }
        .message {
            margin-bottom: 15px;
            padding: 10px;
            border-radius: 6px;
        }
        .user-message {
            background: #e3f2fd;
            margin-left: 20px;
        }
        .agent-message {
            background: #f1f8e9;
            margin-right: 20px;
        }
        .error-message {
            background: #ffebee;
            color: #c62828;
            margin-right: 20px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 15px;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
        }
        .tools-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .tool-result {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 Rippr API Demo</h1>
        <p>Interactive demo of the Rippr code agent API</p>
    </div>

    <!-- Configuration Section -->
    <div class="container">
        <h2>⚙️ Configuration</h2>
        <div class="config-section">
            <div>
                <div class="form-group">
                    <label for="apiUrl">API URL:</label>
                    <input type="text" id="apiUrl" value="http://localhost:8000">
                </div>
                <div class="form-group">
                    <label for="apiKey">API Key:</label>
                    <input type="text" id="apiKey" value="rippr-dev-key-12345">
                </div>
            </div>
            <div>
                <div class="form-group">
                    <label for="agentType">Agent Type:</label>
                    <select id="agentType">
                        <option value="chat">Chat Agent</option>
                        <option value="code">Code Agent</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="modelProvider">Model Provider:</label>
                    <select id="modelProvider">
                        <option value="copilot">GitHub Copilot</option>
                        <option value="openai">OpenAI</option>
                        <option value="anthropic">Anthropic</option>
                        <option value="vertex">Vertex AI</option>
                    </select>
                </div>
            </div>
        </div>
        <button onclick="createAgent()">Create Agent</button>
        <button onclick="listAgents()">List Agents</button>
        <button onclick="checkHealth()">Health Check</button>
        <div id="status"></div>
    </div>

    <!-- Chat Section -->
    <div class="container">
        <h2>💬 Chat Interface</h2>
        <div class="form-group">
            <label for="selectedAgent">Select Agent:</label>
            <select id="selectedAgent">
                <option value="">No agents available</option>
            </select>
        </div>
        <div class="chat-container" id="chatContainer">
            <div class="message agent-message">
                Welcome! Create an agent above and start chatting.
            </div>
        </div>
        <div class="form-group">
            <textarea id="messageInput" placeholder="Type your message here..." rows="3"></textarea>
        </div>
        <button onclick="sendMessage()">Send Message</button>
        <button onclick="clearChat()">Clear Chat</button>
    </div>

    <!-- Tools Section -->
    <div class="container">
        <h2>🔧 Tool Execution</h2>
        <div class="tools-section">
            <div>
                <h3>File Operations</h3>
                <div class="form-group">
                    <label for="filepath">File Path:</label>
                    <input type="text" id="filepath" value="src/main.py">
                </div>
                <button onclick="viewFile()">View File</button>
                <button onclick="listDirectory()">List Directory</button>
                <div id="fileResult" class="tool-result" style="display: none;"></div>
            </div>
            <div>
                <h3>Search Operations</h3>
                <div class="form-group">
                    <label for="searchQuery">Search Query:</label>
                    <input type="text" id="searchQuery" value="function">
                </div>
                <div class="form-group">
                    <label for="fileExtensions">File Extensions (comma-separated):</label>
                    <input type="text" id="fileExtensions" value=".py,.js,.ts">
                </div>
                <button onclick="searchCodebase()">Search</button>
                <button onclick="listTools()">List All Tools</button>
                <div id="searchResult" class="tool-result" style="display: none;"></div>
            </div>
        </div>
    </div>

    <script>
        let currentAgentId = null;
        let currentThreadId = null;

        // Utility functions
        function showStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.className = `status ${type}`;
            status.textContent = message;
            status.style.display = 'block';
        }

        function getHeaders() {
            return {
                'Authorization': `Bearer ${document.getElementById('apiKey').value}`,
                'Content-Type': 'application/json'
            };
        }

        function getApiUrl() {
            return document.getElementById('apiUrl').value;
        }

        // Agent management
        async function createAgent() {
            try {
                showStatus('Creating agent...', 'info');
                
                const response = await fetch(`${getApiUrl()}/api/v1/agents/`, {
                    method: 'POST',
                    headers: getHeaders(),
                    body: JSON.stringify({
                        name: `${document.getElementById('agentType').value} Agent`,
                        config: {
                            agent_type: document.getElementById('agentType').value,
                            model_provider: document.getElementById('modelProvider').value,
                            codebase_path: ".",
                            memory: true,
                            interactive: true
                        }
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${await response.text()}`);
                }

                const agent = await response.json();
                showStatus(`Agent created successfully: ${agent.id}`, 'success');
                
                // Add to dropdown
                const select = document.getElementById('selectedAgent');
                const option = document.createElement('option');
                option.value = agent.id;
                option.textContent = `${agent.name} (${agent.id.substring(0, 8)})`;
                select.appendChild(option);
                select.value = agent.id;
                currentAgentId = agent.id;
                
            } catch (error) {
                showStatus(`Error creating agent: ${error.message}`, 'error');
            }
        }

        async function listAgents() {
            try {
                const response = await fetch(`${getApiUrl()}/api/v1/agents/`, {
                    headers: getHeaders()
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${await response.text()}`);
                }

                const data = await response.json();
                showStatus(`Found ${data.agents.length} agents`, 'success');
                
                // Update dropdown
                const select = document.getElementById('selectedAgent');
                select.innerHTML = '<option value="">Select an agent</option>';
                
                data.agents.forEach(agent => {
                    const option = document.createElement('option');
                    option.value = agent.id;
                    option.textContent = `${agent.name} (${agent.id.substring(0, 8)})`;
                    select.appendChild(option);
                });
                
            } catch (error) {
                showStatus(`Error listing agents: ${error.message}`, 'error');
            }
        }

        async function checkHealth() {
            try {
                const response = await fetch(`${getApiUrl()}/health`);
                const health = await response.json();
                showStatus(`API is healthy - Version: ${health.version}, Uptime: ${Math.round(health.uptime)}s`, 'success');
            } catch (error) {
                showStatus(`Health check failed: ${error.message}`, 'error');
            }
        }

        // Chat functions
        function addMessage(content, isUser = false, isError = false) {
            const chatContainer = document.getElementById('chatContainer');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${isError ? 'error-message' : (isUser ? 'user-message' : 'agent-message')}`;
            messageDiv.textContent = content;
            chatContainer.appendChild(messageDiv);
            chatContainer.scrollTop = chatContainer.scrollHeight;
        }

        async function sendMessage() {
            const agentId = document.getElementById('selectedAgent').value;
            const message = document.getElementById('messageInput').value.trim();
            
            if (!agentId) {
                showStatus('Please select an agent first', 'error');
                return;
            }
            
            if (!message) {
                showStatus('Please enter a message', 'error');
                return;
            }

            try {
                addMessage(message, true);
                document.getElementById('messageInput').value = '';
                
                const response = await fetch(`${getApiUrl()}/api/v1/chat/${agentId}`, {
                    method: 'POST',
                    headers: getHeaders(),
                    body: JSON.stringify({
                        message: message,
                        thread_id: currentThreadId
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${await response.text()}`);
                }

                const chatResponse = await response.json();
                currentThreadId = chatResponse.thread_id;
                addMessage(chatResponse.response);
                
            } catch (error) {
                addMessage(`Error: ${error.message}`, false, true);
            }
        }

        function clearChat() {
            const chatContainer = document.getElementById('chatContainer');
            chatContainer.innerHTML = '<div class="message agent-message">Chat cleared. Start a new conversation!</div>';
            currentThreadId = null;
        }

        // Tool functions
        async function viewFile() {
            const filepath = document.getElementById('filepath').value;
            await executeTool('view_file', { filepath }, 'fileResult');
        }

        async function listDirectory() {
            await executeTool('list_directory', { dirpath: './' }, 'fileResult');
        }

        async function searchCodebase() {
            const query = document.getElementById('searchQuery').value;
            const extensions = document.getElementById('fileExtensions').value
                .split(',').map(ext => ext.trim()).filter(ext => ext);
            
            await executeTool('search', { 
                query, 
                file_extensions: extensions.length > 0 ? extensions : null 
            }, 'searchResult');
        }

        async function listTools() {
            try {
                const response = await fetch(`${getApiUrl()}/api/v1/tools/`, {
                    headers: getHeaders()
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${await response.text()}`);
                }

                const data = await response.json();
                const resultDiv = document.getElementById('searchResult');
                resultDiv.textContent = JSON.stringify(data, null, 2);
                resultDiv.style.display = 'block';
                
            } catch (error) {
                showStatus(`Error listing tools: ${error.message}`, 'error');
            }
        }

        async function executeTool(toolName, parameters, resultElementId) {
            try {
                const response = await fetch(`${getApiUrl()}/api/v1/tools/execute`, {
                    method: 'POST',
                    headers: getHeaders(),
                    body: JSON.stringify({
                        tool_name: toolName,
                        parameters: parameters
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${await response.text()}`);
                }

                const result = await response.json();
                const resultDiv = document.getElementById(resultElementId);
                resultDiv.textContent = JSON.stringify(result, null, 2);
                resultDiv.style.display = 'block';
                
            } catch (error) {
                showStatus(`Error executing ${toolName}: ${error.message}`, 'error');
            }
        }

        // Event listeners
        document.getElementById('selectedAgent').addEventListener('change', function() {
            currentAgentId = this.value;
            currentThreadId = null; // Reset thread when switching agents
        });

        document.getElementById('messageInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });

        // Initialize
        checkHealth();
    </script>
</body>
</html>
