#!/usr/bin/env python3
"""
Context Intelligence Demo

Demonstrates how the context intelligence layer enhances agent operations
with semantic understanding and automatic context discovery.
"""

import sys
import os

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from codegen.sdk.core.codebase import Codebase
from context_intelligence import ContextI<PERSON>lligence<PERSON>ayer, EnhancedAgentTools


def demo_context_intelligence():
    """Demonstrate context intelligence capabilities"""
    
    print("🚀 Context Intelligence Demo")
    print("=" * 50)
    
    # Initialize codebase (assumes we're in rippr directory)
    try:
        codebase = Codebase(repo_path=".")
        print(f"✅ Loaded codebase with {len(codebase.symbols)} symbols")
    except Exception as e:
        print(f"❌ Failed to load codebase: {e}")
        return
    
    # Initialize context intelligence
    context_layer = ContextIntelligenceLayer(codebase)
    enhanced_tools = EnhancedAgentTools(codebase)
    
    print("\n" + "=" * 50)
    print("🧠 CONTEXT ANALYSIS DEMO")
    print("=" * 50)
    
    # Demo queries that agents might make
    demo_queries = [
        "find authentication code",
        "fix user login bug", 
        "add rate limiting to API",
        "refactor database queries",
        "where is UserService used"
    ]
    
    for query in demo_queries:
        print(f"\n🔍 Agent Query: '{query}'")
        print("-" * 30)
        
        # Extract context
        context = context_layer.extract_context(query)
        
        print(f"Intent: {context.task.intent}")
        print(f"Domain: {context.task.domain}")
        print(f"Entities: {context.task.entities}")
        print(f"Confidence: {context.confidence:.2f}")
        print(f"Impact Scope: {context.impact_scope}")
        
        if context.relevant_symbols:
            print(f"Relevant Symbols ({len(context.relevant_symbols)}):")
            for symbol in context.relevant_symbols[:3]:
                print(f"  • {symbol.symbol_name} ({symbol.domain})")
        
        if context.suggested_actions:
            print("Suggested Actions:")
            for action in context.suggested_actions[:2]:
                print(f"  • {action}")
    
    print("\n" + "=" * 50)
    print("🔧 ENHANCED TOOLS DEMO")
    print("=" * 50)
    
    # Demo enhanced search
    print("\n🔍 Enhanced Search Demo:")
    print("-" * 25)
    
    search_result = enhanced_tools.enhanced_search("authentication")
    print(search_result[:500] + "..." if len(search_result) > 500 else search_result)
    
    # Demo symbol analysis
    print("\n🔍 Symbol Analysis Demo:")
    print("-" * 25)
    
    # Try to find a symbol to analyze
    if codebase.symbols:
        sample_symbol = codebase.symbols[0].name
        symbol_result = enhanced_tools.enhanced_find_symbol(sample_symbol)
        print(symbol_result[:500] + "..." if len(symbol_result) > 500 else symbol_result)
    
    print("\n" + "=" * 50)
    print("✅ Demo Complete!")
    print("=" * 50)
    
    print("\nKey Benefits Demonstrated:")
    print("• Automatic intent classification from natural language")
    print("• Domain-aware symbol discovery")
    print("• Impact analysis for safer operations")
    print("• Context-aware suggestions")
    print("• Enhanced search with semantic understanding")
    print("\nAgents get all this intelligence without any training! 🎯")


if __name__ == "__main__":
    demo_context_intelligence()

