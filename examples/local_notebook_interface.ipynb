{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 🚀 Rippr Local Notebook Interface\n", "\n", "This notebook provides an interactive interface for working with Rippr agents locally.\n", "Perfect for experimentation, prototyping, and iterative development."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Setup and imports\n", "import sys\n", "from pathlib import Path\n", "\n", "# Add project root to path\n", "project_root = Path.cwd().parent if Path.cwd().name == 'examples' else Path.cwd()\n", "sys.path.insert(0, str(project_root))\n", "\n", "from src.agents.chat_agent import ChatAgent\n", "from src.agents.code_agent import CodeAgent\n", "from codegen import Codebase\n", "from IPython.display import display, Markdown, HTML\n", "import ipywidgets as widgets\n", "from IPython.display import clear_output\n", "\n", "print(\"✅ Rippr imports successful!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Initialize codebase\n", "codebase_path = \".\"\n", "codebase = Codebase(codebase_path)\n", "print(f\"📁 Codebase loaded from: {codebase_path}\")\n", "print(f\"✅ Ready to create agents!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🤖 Create Agents\n", "\n", "Create different types of agents for various tasks:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create a chat agent\n", "chat_agent = ChatAgent(\n", "    codebase=codebase,\n", "    model_provider=\"copilot\",  # or \"openai\", \"anthropic\", \"vertex\"\n", "    model_name=\"gpt-4.1\",\n", "    memory=True,\n", "    interactive=True,\n", "    condensed_logging=True,\n", "    short_format=True\n", ")\n", "\n", "print(\"✅ Chat agent created!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create a code agent\n", "code_agent = CodeAgent(\n", "    codebase=codebase,\n", "    model_provider=\"vertex\",  # Good for code tasks\n", "    model_name=\"gemini-2.0-flash-001\",\n", "    memory=True,\n", "    condensed_logging=True,\n", "    short_format=True\n", ")\n", "\n", "print(\"✅ Code agent created!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 💬 Interactive Chat Interface\n", "\n", "Use the widget below for interactive chatting:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Interactive chat widget\n", "class NotebookChatInterface:\n", "    def __init__(self, agents):\n", "        self.agents = agents\n", "        self.current_agent = list(agents.values())[0] if agents else None\n", "        self.chat_history = []\n", "        \n", "        # Create widgets\n", "        self.agent_dropdown = widgets.Dropdown(\n", "            options=[(name, agent) for name, agent in agents.items()],\n", "            description='Agent:',\n", "            style={'description_width': 'initial'}\n", "        )\n", "        \n", "        self.message_input = widgets.Textarea(\n", "            placeholder='Type your message here...',\n", "            description='Message:',\n", "            layout=widgets.Layout(width='100%', height='80px'),\n", "            style={'description_width': 'initial'}\n", "        )\n", "        \n", "        self.send_button = widgets.Button(\n", "            description='Send',\n", "            button_style='primary',\n", "            icon='paper-plane'\n", "        )\n", "        \n", "        self.clear_button = widgets.Button(\n", "            description='Clear',\n", "            button_style='warning',\n", "            icon='trash'\n", "        )\n", "        \n", "        self.output = widgets.Output()\n", "        \n", "        # Set up event handlers\n", "        self.agent_dropdown.observe(self.on_agent_change, names='value')\n", "        self.send_button.on_click(self.send_message)\n", "        self.clear_button.on_click(self.clear_chat)\n", "        \n", "        # Layout\n", "        self.interface = widgets.VBox([\n", "            self.agent_dropdown,\n", "            self.message_input,\n", "            widgets.HBox([self.send_button, self.clear_button]),\n", "            self.output\n", "        ])\n", "        \n", "        # Initial message\n", "        with self.output:\n", "            display(HTML('<div style=\"padding: 10px; background: #e3f2fd; border-radius: 5px; margin: 5px 0;\"><strong>System:</strong> Welcome! Select an agent and start chatting.</div>'))\n", "    \n", "    def on_agent_change(self, change):\n", "        self.current_agent = change['new']\n", "        with self.output:\n", "            display(HTML(f'<div style=\"padding: 10px; background: #fff3e0; border-radius: 5px; margin: 5px 0;\"><strong>System:</strong> Switched to {type(self.current_agent).__name__}</div>'))\n", "    \n", "    def send_message(self, button):\n", "        message = self.message_input.value.strip()\n", "        if not message:\n", "            return\n", "        \n", "        # Clear input\n", "        self.message_input.value = ''\n", "        \n", "        # Display user message\n", "        with self.output:\n", "            display(HTML(f'<div style=\"padding: 10px; background: #e8f5e8; border-radius: 5px; margin: 5px 0; text-align: right;\"><strong>You:</strong> {message}</div>'))\n", "        \n", "        # Get agent response\n", "        try:\n", "            with self.output:\n", "                display(HTML('<div style=\"padding: 10px; background: #f5f5f5; border-radius: 5px; margin: 5px 0;\"><strong>Agent:</strong> <em>Thinking...</em></div>'))\n", "            \n", "            response = self.current_agent.run(message, stream_output=False)\n", "            \n", "            # Clear the \"thinking\" message and show response\n", "            with self.output:\n", "                # Remove the last \"thinking\" message\n", "                clear_output(wait=True)\n", "                \n", "                # Redisplay all messages including the new response\n", "                for msg in self.chat_history:\n", "                    display(HTML(msg))\n", "                \n", "                # Add new messages to history\n", "                user_msg = f'<div style=\"padding: 10px; background: #e8f5e8; border-radius: 5px; margin: 5px 0; text-align: right;\"><strong>You:</strong> {message}</div>'\n", "                agent_msg = f'<div style=\"padding: 10px; background: #f0f8ff; border-radius: 5px; margin: 5px 0;\"><strong>Agent:</strong> {response}</div>'\n", "                \n", "                self.chat_history.extend([user_msg, agent_msg])\n", "                \n", "                display(HTML(user_msg))\n", "                display(HTML(agent_msg))\n", "                \n", "        except Exception as e:\n", "            with self.output:\n", "                display(HTML(f'<div style=\"padding: 10px; background: #ffebee; border-radius: 5px; margin: 5px 0; color: #c62828;\"><strong>Error:</strong> {str(e)}</div>'))\n", "    \n", "    def clear_chat(self, button):\n", "        self.chat_history = []\n", "        with self.output:\n", "            clear_output()\n", "            display(HTML('<div style=\"padding: 10px; background: #e3f2fd; border-radius: 5px; margin: 5px 0;\"><strong>System:</strong> Chat cleared!</div>'))\n", "    \n", "    def display(self):\n", "        display(self.interface)\n", "\n", "# Create and display the chat interface\n", "agents = {\n", "    \"Chat Agent\": chat_agent,\n", "    \"Code Agent\": code_agent\n", "}\n", "\n", "chat_interface = NotebookChatInterface(agents)\n", "chat_interface.display()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🛠️ Direct Agent Usage\n", "\n", "You can also use agents directly in cells:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Direct chat agent usage\n", "response = chat_agent.run(\"What is the main purpose of this codebase?\")\n", "print(\"Agent Response:\")\n", "print(response)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Direct code agent usage\n", "response = code_agent.run(\"Show me the main entry points in this codebase\")\n", "print(\"Code Agent Response:\")\n", "print(response)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🔧 Tool Usage Examples\n", "\n", "Access individual tools directly:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import tools\n", "from src.tools import view_file, search, list_directory\n", "\n", "# View a file\n", "result = view_file(codebase, \"src/main.py\", start_line=1, end_line=20)\n", "print(\"File content:\")\n", "print(result.content)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Search the codebase\n", "search_result = search(codebase, \"def main\", file_extensions=[\".py\"])\n", "print(\"Search results:\")\n", "for file_result in search_result.files:\n", "    print(f\"\\n📄 {file_result.filepath}:\")\n", "    for match in file_result.matches[:3]:  # Show first 3 matches\n", "        print(f\"  Line {match.line_number}: {match.content.strip()}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# List directory contents\n", "dir_result = list_directory(codebase, \"./src\", depth=2)\n", "print(\"Directory structure:\")\n", "print(dir_result.content)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🎯 Quick Tasks\n", "\n", "Use this section for quick one-off tasks:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Quick task: Analyze project structure\n", "task = \"Analyze the overall structure of this project and tell me about the main components\"\n", "response = chat_agent.run(task)\n", "display(Markdown(f\"**Task:** {task}\\n\\n**Response:**\\n{response}\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Quick task: Code review\n", "task = \"Review the main CLI file and suggest any improvements\"\n", "response = code_agent.run(task)\n", "display(Markdown(f\"**Task:** {task}\\n\\n**Response:**\\n{response}\"))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📊 Agent Comparison\n", "\n", "Compare responses from different agents:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Compare agent responses\n", "question = \"What are the key features of this codebase?\"\n", "\n", "print(\"🤖 Chat Agent Response:\")\n", "chat_response = chat_agent.run(question)\n", "print(chat_response)\n", "\n", "print(\"\\n\" + \"=\"*60 + \"\\n\")\n", "\n", "print(\"🔧 Code Agent Response:\")\n", "code_response = code_agent.run(question)\n", "print(code_response)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.0"}}, "nbformat": 4, "nbformat_minor": 4}