#!/usr/bin/env python3
"""
Example Python client for the Rippr API.

This demonstrates how to interact with the Rippr API from Python.
"""

import requests
import json
import time
from typing import Dict, List, Optional


class RipprAPIClient:
    """Python client for the Rippr API."""
    
    def __init__(self, base_url: str = "http://localhost:8000", api_key: str = "rippr-dev-key-12345"):
        """Initialize the client.
        
        Args:
            base_url: Base URL of the Rippr API server
            api_key: API key for authentication
        """
        self.base_url = base_url.rstrip('/')
        self.headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
    
    def health_check(self) -> Dict:
        """Check if the API is healthy."""
        response = requests.get(f"{self.base_url}/health")
        response.raise_for_status()
        return response.json()
    
    def create_agent(self, name: str, agent_type: str = "chat", 
                    model_provider: str = "copilot", model_name: Optional[str] = None,
                    codebase_path: str = ".") -> Dict:
        """Create a new agent.
        
        Args:
            name: Human-readable name for the agent
            agent_type: Type of agent ("chat" or "code")
            model_provider: Model provider to use
            model_name: Specific model name (optional)
            codebase_path: Path to the codebase
            
        Returns:
            Agent information
        """
        config = {
            "agent_type": agent_type,
            "model_provider": model_provider,
            "codebase_path": codebase_path,
            "memory": True,
            "interactive": True,
            "condensed_logging": True,
            "short_format": True
        }
        
        if model_name:
            config["model_name"] = model_name
        
        data = {
            "name": name,
            "config": config
        }
        
        response = requests.post(
            f"{self.base_url}/api/v1/agents/",
            headers=self.headers,
            json=data
        )
        response.raise_for_status()
        return response.json()
    
    def list_agents(self) -> List[Dict]:
        """List all agents."""
        response = requests.get(
            f"{self.base_url}/api/v1/agents/",
            headers=self.headers
        )
        response.raise_for_status()
        return response.json()["agents"]
    
    def get_agent(self, agent_id: str) -> Dict:
        """Get information about a specific agent."""
        response = requests.get(
            f"{self.base_url}/api/v1/agents/{agent_id}",
            headers=self.headers
        )
        response.raise_for_status()
        return response.json()
    
    def send_message(self, agent_id: str, message: str, thread_id: Optional[str] = None) -> Dict:
        """Send a message to an agent.
        
        Args:
            agent_id: ID of the agent to send message to
            message: Message content
            thread_id: Optional thread ID for conversation continuity
            
        Returns:
            Chat response
        """
        data = {
            "message": message,
            "stream": False,
            "include_context": True
        }
        
        if thread_id:
            data["thread_id"] = thread_id
        
        response = requests.post(
            f"{self.base_url}/api/v1/chat/{agent_id}",
            headers=self.headers,
            json=data
        )
        response.raise_for_status()
        return response.json()
    
    def get_chat_history(self, agent_id: str, thread_id: str, limit: int = 50) -> Dict:
        """Get chat history for a thread."""
        params = {
            "thread_id": thread_id,
            "limit": limit
        }
        
        response = requests.get(
            f"{self.base_url}/api/v1/chat/{agent_id}/history",
            headers=self.headers,
            params=params
        )
        response.raise_for_status()
        return response.json()
    
    def execute_tool(self, tool_name: str, parameters: Dict, agent_id: Optional[str] = None) -> Dict:
        """Execute a tool.
        
        Args:
            tool_name: Name of the tool to execute
            parameters: Tool parameters
            agent_id: Optional agent ID for context
            
        Returns:
            Tool execution result
        """
        data = {
            "tool_name": tool_name,
            "parameters": parameters
        }
        
        if agent_id:
            data["agent_id"] = agent_id
        
        response = requests.post(
            f"{self.base_url}/api/v1/tools/execute",
            headers=self.headers,
            json=data
        )
        response.raise_for_status()
        return response.json()
    
    def list_tools(self, category: Optional[str] = None) -> Dict:
        """List available tools."""
        params = {}
        if category:
            params["category"] = category
        
        response = requests.get(
            f"{self.base_url}/api/v1/tools/",
            headers=self.headers,
            params=params
        )
        response.raise_for_status()
        return response.json()
    
    def view_file(self, filepath: str, start_line: Optional[int] = None, 
                  end_line: Optional[int] = None) -> Dict:
        """View a file."""
        params = {"filepath": filepath}
        if start_line:
            params["start_line"] = start_line
        if end_line:
            params["end_line"] = end_line
        
        response = requests.post(
            f"{self.base_url}/api/v1/tools/file/view",
            headers=self.headers,
            params=params
        )
        response.raise_for_status()
        return response.json()
    
    def search_codebase(self, query: str, file_extensions: Optional[List[str]] = None,
                       use_regex: bool = False, page: int = 1, page_size: int = 10) -> Dict:
        """Search the codebase."""
        data = {
            "query": query,
            "use_regex": use_regex,
            "page": page,
            "page_size": page_size
        }
        
        if file_extensions:
            data["file_extensions"] = file_extensions
        
        response = requests.post(
            f"{self.base_url}/api/v1/tools/search",
            headers=self.headers,
            json=data
        )
        response.raise_for_status()
        return response.json()


def main():
    """Example usage of the Rippr API client."""
    
    # Initialize client
    client = RipprAPIClient()
    
    print("🚀 Rippr API Client Example")
    print("=" * 40)
    
    try:
        # Health check
        print("1. Checking API health...")
        health = client.health_check()
        print(f"   ✅ API is healthy - Version: {health['version']}")
        
        # Create an agent
        print("\n2. Creating a chat agent...")
        agent = client.create_agent(
            name="Example Chat Agent",
            agent_type="chat",
            model_provider="copilot"
        )
        agent_id = agent["id"]
        print(f"   ✅ Agent created: {agent['name']} ({agent_id[:8]}...)")
        
        # List agents
        print("\n3. Listing all agents...")
        agents = client.list_agents()
        print(f"   ✅ Found {len(agents)} agents")
        for a in agents:
            print(f"      - {a['name']} ({a['id'][:8]}...) - {a['status']}")
        
        # Send a message
        print("\n4. Sending a message to the agent...")
        response = client.send_message(
            agent_id=agent_id,
            message="Hello! Can you tell me about this codebase?"
        )
        print(f"   ✅ Agent response: {response['response'][:100]}...")
        thread_id = response["thread_id"]
        
        # Send another message in the same thread
        print("\n5. Continuing the conversation...")
        response2 = client.send_message(
            agent_id=agent_id,
            message="What are the main files in this project?",
            thread_id=thread_id
        )
        print(f"   ✅ Agent response: {response2['response'][:100]}...")
        
        # List available tools
        print("\n6. Listing available tools...")
        tools = client.list_tools()
        print(f"   ✅ Found {len(tools['tools'])} tools in {len(tools['categories'])} categories")
        for category in tools['categories']:
            category_tools = [t for t in tools['tools'] if t['category'] == category]
            print(f"      - {category}: {len(category_tools)} tools")
        
        # View a file
        print("\n7. Viewing a file...")
        file_result = client.view_file("src/main.py", start_line=1, end_line=20)
        if file_result["success"]:
            print("   ✅ File viewed successfully")
            print(f"      Content preview: {str(file_result['result'])[:100]}...")
        else:
            print(f"   ❌ Failed to view file: {file_result.get('error', 'Unknown error')}")
        
        # Search the codebase
        print("\n8. Searching the codebase...")
        search_result = client.search_codebase(
            query="def main",
            file_extensions=[".py"],
            page_size=5
        )
        if search_result["success"]:
            print("   ✅ Search completed successfully")
            print(f"      Search results: {str(search_result['result'])[:100]}...")
        else:
            print(f"   ❌ Search failed: {search_result.get('error', 'Unknown error')}")
        
        # Get chat history
        print("\n9. Getting chat history...")
        history = client.get_chat_history(agent_id, thread_id)
        print(f"   ✅ Retrieved {len(history['messages'])} messages from thread")
        
        print("\n🎉 All examples completed successfully!")
        
    except requests.exceptions.RequestException as e:
        print(f"❌ API request failed: {e}")
        print("💡 Make sure the Rippr API server is running on http://localhost:8000")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")


if __name__ == "__main__":
    main()
