#!/usr/bin/env python3
"""
Comprehensive test script for Context Intelligence Layer

This script validates all components of the LSP Autonomous Agent Integration.
"""

import sys
import os
import traceback
from typing import Dict, Any

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_imports():
    """Test that all modules can be imported"""
    print("🧪 Testing imports...")
    
    try:
        from context_intelligence import (
            ContextIntelligenceLayer,
            TaskClassifier,
            SymbolAnalyzer,
            EnhancedAgentTools
        )
        print("✅ Context intelligence imports successful")
        return True
    except ImportError as e:
        print(f"❌ Context intelligence import failed: {e}")
        return False

def test_task_classifier():
    """Test the task classifier functionality"""
    print("\n🧪 Testing TaskClassifier...")
    
    try:
        from context_intelligence import TaskClassifier
        
        classifier = TaskClassifier()
        
        # Test intent classification
        test_cases = [
            ("fix authentication bug", "fix_bug"),
            ("add new user feature", "add_feature"),
            ("refactor user service", "refactor"),
            ("find user model", "find_code"),
            ("analyze login flow", "analyze"),
            ("test user registration", "test")
        ]
        
        for query, expected_intent in test_cases:
            intent = classifier.classify_intent(query)
            print(f"   Query: '{query}' -> Intent: {intent}")
            if intent != expected_intent:
                print(f"   ⚠️  Expected {expected_intent}, got {intent}")
        
        # Test domain classification
        domain_cases = [
            ("fix user login", "authentication"),
            ("update user profile", "user_management"),
            ("query database", "data_access"),
            ("create API endpoint", "api"),
            ("update UI component", "ui")
        ]
        
        for query, expected_domain in domain_cases:
            domain = classifier.classify_domain(query)
            print(f"   Query: '{query}' -> Domain: {domain}")
        
        # Test entity extraction
        entities = classifier.extract_entities("fix UserService authentication bug")
        print(f"   Entities from 'fix UserService authentication bug': {entities}")
        
        # Test complete analysis
        analysis = classifier.analyze_task("fix authentication bug in UserService")
        print(f"   Complete analysis: {analysis}")
        
        print("✅ TaskClassifier tests passed")
        return True
        
    except Exception as e:
        print(f"❌ TaskClassifier test failed: {e}")
        traceback.print_exc()
        return False

def test_with_codebase():
    """Test components that require a codebase"""
    print("\n🧪 Testing with codebase...")
    
    try:
        from codegen import Codebase
        from context_intelligence import ContextIntelligenceLayer, SymbolAnalyzer, EnhancedAgentTools
        
        # Initialize codebase
        print("   Loading codebase...")
        codebase = Codebase(".")
        print(f"   Loaded {len(codebase.symbols)} symbols")
        
        # Test SymbolAnalyzer
        print("   Testing SymbolAnalyzer...")
        symbol_analyzer = SymbolAnalyzer(codebase)
        
        # Find a symbol to analyze
        if codebase.symbols:
            test_symbol = codebase.symbols[0].name
            print(f"   Analyzing symbol: {test_symbol}")
            
            symbol_context = symbol_analyzer.analyze_symbol_context(test_symbol)
            if symbol_context:
                print(f"   Symbol context: {symbol_context.symbol_name} in {symbol_context.domain}")
            else:
                print("   No context found for symbol")
        
        # Test ContextIntelligenceLayer
        print("   Testing ContextIntelligenceLayer...")
        context_layer = ContextIntelligenceLayer(codebase)
        
        test_queries = [
            "fix authentication bug",
            "add new user feature", 
            "find UserService",
            "refactor database code"
        ]
        
        for query in test_queries:
            print(f"   Analyzing query: '{query}'")
            context = context_layer.extract_context(query)
            print(f"     Intent: {context.task.intent}, Domain: {context.task.domain}")
            print(f"     Confidence: {context.confidence:.2f}, Impact: {context.impact_scope}")
            print(f"     Relevant symbols: {len(context.relevant_symbols)}")
            print(f"     Suggested actions: {len(context.suggested_actions)}")
        
        # Test EnhancedAgentTools
        print("   Testing EnhancedAgentTools...")
        enhanced_tools = EnhancedAgentTools(codebase)
        
        # Test enhanced search
        search_result = enhanced_tools.enhanced_search("authentication")
        print(f"   Enhanced search result length: {len(search_result)} characters")
        
        # Test enhanced symbol finding
        if codebase.symbols:
            symbol_result = enhanced_tools.enhanced_find_symbol(codebase.symbols[0].name)
            print(f"   Enhanced symbol result length: {len(symbol_result)} characters")
        
        print("✅ Codebase-dependent tests passed")
        return True
        
    except Exception as e:
        print(f"❌ Codebase-dependent test failed: {e}")
        traceback.print_exc()
        return False

def test_lsp_integration():
    """Test LSP integration components"""
    print("\n🧪 Testing LSP integration...")
    
    try:
        from lsp.enhanced_server import create_enhanced_lsp_server, AgentLSPClient
        from lsp.context_provider import LSPContextProvider, LSPContextCommands
        
        # Test server creation
        print("   Creating enhanced LSP server...")
        server = create_enhanced_lsp_server()
        print(f"   Server created: {type(server).__name__}")
        
        # Test with codebase if available
        try:
            from codegen import Codebase
            codebase = Codebase(".")
            
            print("   Initializing context intelligence...")
            server.initialize_context_intelligence(codebase)
            
            if server.context_provider:
                print("   ✅ Context provider initialized")
            else:
                print("   ⚠️  Context provider not initialized")
            
            if server.context_commands:
                print("   ✅ Context commands initialized")
            else:
                print("   ⚠️  Context commands not initialized")
            
            # Test agent client
            print("   Testing AgentLSPClient...")
            agent_client = AgentLSPClient(server)
            
            # Test query analysis
            if server.context_provider:
                analysis = agent_client.analyze_task("fix authentication bug")
                print(f"   Agent task analysis: {type(analysis)}")
        
        except Exception as e:
            print(f"   ⚠️  Codebase-dependent LSP tests skipped: {e}")
        
        print("✅ LSP integration tests passed")
        return True
        
    except Exception as e:
        print(f"❌ LSP integration test failed: {e}")
        traceback.print_exc()
        return False

def test_performance():
    """Test performance characteristics"""
    print("\n🧪 Testing performance...")
    
    try:
        import time
        from context_intelligence import TaskClassifier
        
        classifier = TaskClassifier()
        
        # Test classification speed
        start_time = time.time()
        for i in range(100):
            classifier.analyze_task(f"fix bug number {i}")
        end_time = time.time()
        
        avg_time = (end_time - start_time) / 100 * 1000  # Convert to ms
        print(f"   Average task analysis time: {avg_time:.2f}ms")
        
        if avg_time < 100:  # Should be under 100ms as per spec
            print("   ✅ Performance target met")
        else:
            print("   ⚠️  Performance target not met")
        
        print("✅ Performance tests completed")
        return True
        
    except Exception as e:
        print(f"❌ Performance test failed: {e}")
        return False

def test_error_handling():
    """Test error handling and graceful degradation"""
    print("\n🧪 Testing error handling...")
    
    try:
        from context_intelligence import TaskClassifier, ContextIntelligenceLayer
        
        classifier = TaskClassifier()
        
        # Test with empty/invalid inputs
        test_cases = [
            "",
            None,
            "   ",
            "a" * 1000,  # Very long input
        ]
        
        for test_input in test_cases:
            try:
                if test_input is not None:
                    result = classifier.analyze_task(test_input)
                    print(f"   Input '{str(test_input)[:20]}...' -> {result.intent}")
                else:
                    print("   Skipping None input test")
            except Exception as e:
                print(f"   ⚠️  Error with input '{str(test_input)[:20]}...': {e}")
        
        print("✅ Error handling tests completed")
        return True
        
    except Exception as e:
        print(f"❌ Error handling test failed: {e}")
        return False

def run_integration_demo():
    """Run a complete integration demo"""
    print("\n🎯 Running integration demo...")
    
    try:
        from codegen import Codebase
        from context_intelligence import ContextIntelligenceLayer, EnhancedAgentTools
        from lsp.enhanced_server import create_enhanced_lsp_server
        
        # Load codebase
        print("   Loading codebase...")
        codebase = Codebase(".")
        
        # Initialize context intelligence
        print("   Initializing context intelligence...")
        context_layer = ContextIntelligenceLayer(codebase)
        enhanced_tools = EnhancedAgentTools(codebase)
        
        # Create LSP server
        print("   Creating enhanced LSP server...")
        server = create_enhanced_lsp_server()
        server.initialize_context_intelligence(codebase)
        
        # Demo scenario: Agent wants to fix authentication
        print("\n   🤖 Demo Scenario: Agent wants to fix authentication bug")
        
        # Step 1: Analyze the task
        context = context_layer.extract_context("fix authentication bug")
        print(f"   📊 Task Analysis:")
        print(f"      Intent: {context.task.intent}")
        print(f"      Domain: {context.task.domain}")
        print(f"      Confidence: {context.confidence:.2f}")
        print(f"      Entities: {context.task.entities}")
        
        # Step 2: Enhanced search
        search_results = enhanced_tools.enhanced_search("authentication")
        print(f"   🔍 Enhanced Search Results: {len(search_results)} characters")
        
        # Step 3: Symbol analysis
        if context.relevant_symbols:
            symbol_name = context.relevant_symbols[0].symbol_name
            symbol_analysis = enhanced_tools.enhanced_find_symbol(symbol_name)
            print(f"   🔬 Symbol Analysis for '{symbol_name}': {len(symbol_analysis)} characters")
        
        print("✅ Integration demo completed successfully")
        return True
        
    except Exception as e:
        print(f"❌ Integration demo failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    print("🚀 Context Intelligence Layer Validation")
    print("=" * 50)
    
    test_results = []
    
    # Run all tests
    tests = [
        ("Import Tests", test_imports),
        ("Task Classifier", test_task_classifier),
        ("Codebase Integration", test_with_codebase),
        ("LSP Integration", test_lsp_integration),
        ("Performance", test_performance),
        ("Error Handling", test_error_handling),
        ("Integration Demo", run_integration_demo)
    ]
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            test_results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            test_results.append((test_name, False))
    
    # Summary
    print("\n" + "="*50)
    print("📊 Test Summary")
    print("="*50)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Context Intelligence Layer is working correctly.")
        return 0
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
        return 1

if __name__ == "__main__":
    exit(main()) 