"""
Context Intelligence Layer

Main orchestration layer that combines task classification and symbol analysis
to provide semantic context for autonomous agents.
"""

from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from codegen.sdk.core.codebase import Codebase

from .task_classifier import TaskClassifier, TaskContext
from .symbol_analyzer import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, SymbolContext


@dataclass
class AgentContext:
    """Complete context information for an agent operation"""
    task: TaskContext
    relevant_symbols: List[SymbolContext]
    domain_symbols: List[SymbolContext]
    related_files: List[str]
    suggested_actions: List[str]
    impact_scope: str
    confidence: float


class ContextIntelligenceLayer:
    """Main context intelligence orchestrator"""
    
    def __init__(self, codebase: Codebase):
        self.codebase = codebase
        self.task_classifier = TaskClassifier()
        self.symbol_analyzer = SymbolAnalyzer(codebase)
        self.context_cache = {}
    
    def extract_context(self, query: str, operation_type: str = "general") -> AgentContext:
        """Extract complete context for an agent query"""
        # Check cache first
        cache_key = f"{query}:{operation_type}"
        if cache_key in self.context_cache:
            return self.context_cache[cache_key]
        
        # Analyze the task
        task_context = self.task_classifier.analyze_task(query)
        
        # Find relevant symbols based on entities
        relevant_symbols = self._find_relevant_symbols(task_context)
        
        # Get domain-specific symbols
        domain_symbols = self._get_domain_symbols(task_context.domain)
        
        # Find related files
        related_files = self._find_related_files(relevant_symbols, domain_symbols)
        
        # Generate suggested actions
        suggested_actions = self._generate_suggested_actions(task_context, relevant_symbols)
        
        # Determine impact scope
        impact_scope = self._determine_impact_scope(task_context, relevant_symbols)
        
        # Calculate overall confidence
        confidence = self._calculate_overall_confidence(task_context, relevant_symbols)
        
        context = AgentContext(
            task=task_context,
            relevant_symbols=relevant_symbols,
            domain_symbols=domain_symbols,
            related_files=related_files,
            suggested_actions=suggested_actions,
            impact_scope=impact_scope,
            confidence=confidence
        )
        
        # Cache the result
        self.context_cache[cache_key] = context
        return context
    
    def get_symbol_context(self, symbol_name: str) -> Optional[SymbolContext]:
        """Get detailed context for a specific symbol"""
        return self.symbol_analyzer.analyze_symbol_context(symbol_name)
    
    def find_symbols_by_intent(self, intent: str, domain: str = None, limit: int = 10) -> List[SymbolContext]:
        """Find symbols that match a specific intent and optional domain"""
        symbols = []
        
        # Get symbols from domain if specified
        if domain and domain != "general":
            domain_symbols = self.symbol_analyzer.find_symbols_by_domain(domain, limit * 2)
        else:
            domain_symbols = [self.symbol_analyzer.analyze_symbol_context(s.name) 
                            for s in self.codebase.symbols[:limit * 2]]
            domain_symbols = [s for s in domain_symbols if s is not None]
        
        # Filter by intent
        for symbol_context in domain_symbols:
            if self._symbol_matches_intent(symbol_context, intent):
                symbols.append(symbol_context)
                if len(symbols) >= limit:
                    break
        
        return symbols
    
    def _find_relevant_symbols(self, task_context: TaskContext) -> List[SymbolContext]:
        """Find symbols relevant to the task"""
        relevant_symbols = []
        
        # Search by entities
        for entity in task_context.entities:
            symbol_context = self.symbol_analyzer.analyze_symbol_context(entity)
            if symbol_context:
                relevant_symbols.append(symbol_context)
        
        # Search by keywords if no entities found
        if not relevant_symbols and task_context.keywords:
            for keyword in task_context.keywords[:3]:  # Limit to top 3 keywords
                # Find symbols with names containing the keyword
                matching_symbols = [s for s in self.codebase.symbols 
                                  if keyword.lower() in s.name.lower()]
                
                for symbol in matching_symbols[:5]:  # Limit per keyword
                    symbol_context = self.symbol_analyzer.analyze_symbol_context(symbol.name)
                    if symbol_context:
                        relevant_symbols.append(symbol_context)
        
        # Remove duplicates
        seen_names = set()
        unique_symbols = []
        for symbol in relevant_symbols:
            if symbol.symbol_name not in seen_names:
                unique_symbols.append(symbol)
                seen_names.add(symbol.symbol_name)
        
        return unique_symbols[:10]  # Limit to top 10
    
    def _get_domain_symbols(self, domain: str, limit: int = 5) -> List[SymbolContext]:
        """Get representative symbols from the specified domain"""
        if domain == "general":
            return []
        
        return self.symbol_analyzer.find_symbols_by_domain(domain, limit)
    
    def _find_related_files(self, relevant_symbols: List[SymbolContext], 
                          domain_symbols: List[SymbolContext]) -> List[str]:
        """Find files related to the symbols"""
        files = set()
        
        # Add files from relevant symbols
        for symbol in relevant_symbols:
            if symbol.file_path:
                files.add(symbol.file_path)
        
        # Add files from domain symbols
        for symbol in domain_symbols:
            if symbol.file_path:
                files.add(symbol.file_path)
        
        # Add related files based on naming patterns
        for symbol in relevant_symbols:
            # Look for test files
            base_name = symbol.file_path.replace('.py', '').replace('.js', '').replace('.ts', '')
            test_patterns = [
                f"test_{base_name}.py",
                f"{base_name}_test.py", 
                f"{base_name}.test.js",
                f"{base_name}.spec.js"
            ]
            
            for pattern in test_patterns:
                # Check if file exists in codebase
                for file_symbol in self.codebase.symbols:
                    if file_symbol.file and pattern in file_symbol.file.filepath:
                        files.add(file_symbol.file.filepath)
        
        return list(files)[:20]  # Limit to 20 files
    
    def _generate_suggested_actions(self, task_context: TaskContext, 
                                  relevant_symbols: List[SymbolContext]) -> List[str]:
        """Generate suggested actions based on context"""
        actions = []
        
        # Intent-based suggestions
        if task_context.intent == "fix_bug":
            actions.extend([
                "Check error logs and stack traces",
                "Review recent changes to related files",
                "Run existing tests to identify failures",
                "Add debugging statements to trace execution"
            ])
        
        elif task_context.intent == "add_feature":
            actions.extend([
                "Review similar existing features for patterns",
                "Identify integration points with existing code",
                "Plan test coverage for new functionality",
                "Consider impact on existing APIs"
            ])
        
        elif task_context.intent == "refactor":
            actions.extend([
                "Analyze current code structure and dependencies",
                "Identify breaking changes and migration needs",
                "Ensure comprehensive test coverage",
                "Plan gradual migration strategy"
            ])
        
        # Symbol-based suggestions
        if relevant_symbols:
            high_complexity_symbols = [s for s in relevant_symbols if s.complexity_score > 15]
            if high_complexity_symbols:
                actions.append("Consider breaking down complex symbols into smaller components")
            
            widely_used_symbols = [s for s in relevant_symbols if "widely_used" in s.usage_patterns]
            if widely_used_symbols:
                actions.append("Exercise caution with widely-used symbols to avoid breaking changes")
        
        return actions[:5]  # Limit to top 5 suggestions
    
    def _determine_impact_scope(self, task_context: TaskContext, 
                              relevant_symbols: List[SymbolContext]) -> str:
        """Determine the potential impact scope of the operation"""
        if not relevant_symbols:
            return "low"
        
        # Calculate impact based on symbol usage and dependencies
        total_usages = sum(len(s.usages) for s in relevant_symbols)
        total_dependencies = sum(len(s.dependencies) for s in relevant_symbols)
        
        # Check for widely used symbols
        has_widely_used = any("widely_used" in s.usage_patterns for s in relevant_symbols)
        
        # Check for high complexity symbols
        has_high_complexity = any(s.complexity_score > 20 for s in relevant_symbols)
        
        if has_widely_used or total_usages > 50 or has_high_complexity:
            return "high"
        elif total_usages > 10 or total_dependencies > 20:
            return "medium"
        else:
            return "low"
    
    def _calculate_overall_confidence(self, task_context: TaskContext, 
                                    relevant_symbols: List[SymbolContext]) -> float:
        """Calculate overall confidence in the context analysis"""
        # Start with task classification confidence
        confidence = task_context.confidence
        
        # Boost confidence if we found relevant symbols
        if relevant_symbols:
            confidence += 0.2
        
        # Boost confidence if domain is well-defined
        if task_context.domain != "general":
            confidence += 0.1
        
        # Boost confidence if we have clear entities
        if task_context.entities:
            confidence += 0.1
        
        return min(confidence, 1.0)  # Cap at 1.0
    
    def _symbol_matches_intent(self, symbol_context: SymbolContext, intent: str) -> bool:
        """Check if a symbol matches the given intent"""
        if intent == "fix_bug":
            # Look for symbols that might be related to errors or testing
            return ("test" in symbol_context.usage_patterns or 
                   "error" in symbol_context.symbol_name.lower() or
                   "exception" in symbol_context.symbol_name.lower())
        
        elif intent == "add_feature":
            # Look for service or controller symbols that might be extension points
            return (symbol_context.architectural_role in ["service", "controller"] or
                   symbol_context.domain != "general")
        
        elif intent == "refactor":
            # Look for complex or widely used symbols
            return (symbol_context.complexity_score > 10 or
                   "widely_used" in symbol_context.usage_patterns)
        
        return True  # Default to including symbol

