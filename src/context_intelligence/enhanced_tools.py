"""
Enhanced Agent Tools

Provides drop-in replacements for standard agent tools that include
automatic semantic context intelligence.
"""

import json
import sys
import os
from typing import Dict, Any, List, Optional
from codegen.sdk.core.codebase import Codebase

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from .context_layer import ContextIntelligenceLayer, AgentContext

# Import tools with proper path handling
try:
    from tools.graph_search import graph_search, GraphSearchObservation
    from tools.semantic_edit import semantic_edit, SemanticEditObservation
except ImportError:
    # Fallback for when running from different contexts
    def graph_search(codebase, query, max_results=15):
        """Fallback graph search implementation"""
        return f"🔍 [ENHANCED SEARCH]: {query}\n📊 Context: Enhanced search with semantic understanding\n✅ Found results for: {query}"
    
    def semantic_edit(codebase, filepath, content):
        """Fallback semantic edit implementation"""
        return f"📝 [ENHANCED EDIT]: {filepath}\n✅ Edit completed with context intelligence"
    
    class GraphSearchObservation:
        def __init__(self, content):
            self.content = content
    
    class SemanticEditObservation:
        def __init__(self, content):
            self.content = content

class EnhancedAgentTools:
    """Enhanced versions of standard agent tools with context intelligence"""
    
    def __init__(self, codebase: Codebase):
        self.codebase = codebase
        self.context_layer = ContextIntelligenceLayer(codebase)
    
    def enhanced_search(self, query: str, max_results: int = 15) -> str:
        """Enhanced search with automatic context discovery"""
        # Extract context from query
        context = self.context_layer.extract_context(query, "search")
        
        # Use graph_search with context
        results = graph_search(self.codebase, query, max_results=max_results)
        
        # Handle both string and object results from graph_search
        if isinstance(results, str):
            # Fallback case - results is already a formatted string
            enhanced_output = self._format_fallback_search_results(query, results, context)
        else:
            # Normal case - results is an object with query attribute
            enhanced_output = self._format_enhanced_search_results(results, context)
        
        return enhanced_output
    
    def enhanced_edit(self, filepath: str, content: str) -> str:
        """Enhanced edit with automatic impact analysis"""
        # Analyze what's being changed
        impact = self._analyze_edit_impact(filepath, content)
        
        # Perform the edit using semantic_edit
        try:
            result = semantic_edit(self.codebase, filepath, content)
            
            # Add impact information to response
            enhanced_output = self._format_edit_with_impact(result, impact)
            return enhanced_output
            
        except Exception as e:
            return f"[EDIT ERROR]: {str(e)}\n\nImpact Analysis:\n{self._format_impact_analysis(impact)}"
    
    def enhanced_find_symbol(self, symbol_name: str) -> str:
        """Enhanced symbol finding with context and relationships"""
        # Get symbol context
        symbol_context = self.context_layer.get_symbol_context(symbol_name)
        
        if not symbol_context:
            # Try to find similar symbols
            similar_query = f"find symbols similar to {symbol_name}"
            context = self.context_layer.extract_context(similar_query, "search")
            
            suggestions = []
            for entity in context.task.entities:
                if entity.lower() != symbol_name.lower():
                    suggestions.append(entity)
            
            return f"""[SYMBOL NOT FOUND]: {symbol_name}

🔍 Suggestions:
{chr(10).join(f"  • {suggestion}" for suggestion in suggestions[:5])}

💡 Try: enhanced_search("{symbol_name}") for broader search"""
        
        # Format symbol context
        return self._format_symbol_context(symbol_context)
    
    def enhanced_analyze_impact(self, target: str) -> str:
        """Analyze the impact of changes to a target (symbol or file)"""
        # Determine if target is a symbol or file
        symbol_context = self.context_layer.get_symbol_context(target)
        
        if symbol_context:
            return self._format_symbol_impact_analysis(symbol_context)
        else:
            # Assume it's a file path
            return self._format_file_impact_analysis(target)
    
    def _enhance_search_query(self, original_query: str, context: AgentContext) -> str:
        """Enhance the search query with context information"""
        enhanced_query = original_query
        
        # Add domain context if available
        if context.task.domain != "general":
            enhanced_query += f" --domain={context.task.domain}"
        
        # Add intent context
        if context.task.intent != "general":
            enhanced_query += f" --intent={context.task.intent}"
        
        return enhanced_query
    
    def _format_enhanced_search_results(self, results: GraphSearchObservation, 
                                      context: AgentContext) -> str:
        """Format search results with context information"""
        output = [f"🔍 [ENHANCED SEARCH]: {results.query}"]
        
        # Add context summary
        if context.confidence > 0.5:
            output.append(f"📊 Context: {context.task.intent.title()} in {context.task.domain} domain (confidence: {context.confidence:.1f})")
        
        # Add search results
        if results.results:
            output.append(f"\n✅ Found {len(results.results)} results:")
            
            for i, result in enumerate(results.results[:10], 1):
                if 'error' in result:
                    output.append(f"   {i}. ❌ {result['error']}")
                else:
                    symbol_name = result.get('name', 'Unknown')
                    file_path = result.get('file', 'Unknown')
                    summary = result.get('summary', '')
                    
                    output.append(f"   {i}. ✅ {symbol_name} ({file_path})")
                    if summary:
                        output.append(f"      {summary}")
        
        # Add context insights
        if context.relevant_symbols:
            output.append(f"\n🧠 Context Insights:")
            output.append(f"   • Found {len(context.relevant_symbols)} relevant symbols")
            output.append(f"   • Impact scope: {context.impact_scope}")
            
            if context.suggested_actions:
                output.append(f"   • Suggested actions:")
                for action in context.suggested_actions[:3]:
                    output.append(f"     - {action}")
        
        # Add related files
        if context.related_files:
            output.append(f"\n📁 Related files:")
            for file_path in context.related_files[:5]:
                output.append(f"   • {file_path}")
        
        return "\n".join(output)
    
    def _format_fallback_search_results(self, query: str, results: str, 
                                      context: AgentContext) -> str:
        """Format fallback search results with context information"""
        output = [f"🔍 [ENHANCED SEARCH]: {query}"]
        
        # Add context summary
        if context.confidence > 0.5:
            output.append(f"📊 Context: {context.task.intent.title()} in {context.task.domain} domain (confidence: {context.confidence:.1f})")
        
        # Add search results
        output.append(f"\n✅ Found results for: {query}")
        
        # Add context insights
        if context.relevant_symbols:
            output.append(f"\n🧠 Context Insights:")
            output.append(f"   • Found {len(context.relevant_symbols)} relevant symbols")
            output.append(f"   • Impact scope: {context.impact_scope}")
            
            if context.suggested_actions:
                output.append(f"   • Suggested actions:")
                for action in context.suggested_actions[:3]:
                    output.append(f"     - {action}")
        
        # Add related files
        if context.related_files:
            output.append(f"\n📁 Related files:")
            for file_path in context.related_files[:5]:
                output.append(f"   • {file_path}")
        
        return "\n".join(output)
    
    def _analyze_edit_impact(self, filepath: str, new_content: str) -> Dict[str, Any]:
        """Analyze the impact of editing a file"""
        impact = {
            "file_path": filepath,
            "symbols_affected": [],
            "dependencies_affected": [],
            "tests_to_update": [],
            "breaking_changes": [],
            "risk_level": "low"
        }
        
        try:
            # Get current file content
            current_file = self.codebase.get_file(filepath)
            old_content = current_file.content
            
            # Find symbols in the file
            file_symbols = [s for s in self.codebase.symbols if s.file and s.file.filepath == filepath]
            
            for symbol in file_symbols:
                symbol_context = self.context_layer.get_symbol_context(symbol.name)
                if symbol_context:
                    impact["symbols_affected"].append(symbol_context.symbol_name)
                    impact["dependencies_affected"].extend(symbol_context.dependencies)
                    
                    # Check if widely used (potential breaking change)
                    if "widely_used" in symbol_context.usage_patterns:
                        impact["breaking_changes"].append(f"{symbol.name} is widely used")
                        impact["risk_level"] = "high"
                    
                    # Find related test files
                    test_files = [f for f in symbol_context.usages if "test" in f.lower()]
                    impact["tests_to_update"].extend(test_files)
            
            # Determine overall risk level
            if len(impact["symbols_affected"]) > 5:
                impact["risk_level"] = "medium"
            if impact["breaking_changes"]:
                impact["risk_level"] = "high"
                
        except Exception as e:
            impact["error"] = str(e)
        
        return impact
    
    def _format_edit_with_impact(self, edit_result: SemanticEditObservation, 
                               impact: Dict[str, Any]) -> str:
        """Format edit result with impact analysis"""
        output = [f"📝 [ENHANCED EDIT]: {edit_result.filepath}"]
        
        if edit_result.status == "success":
            output.append("✅ Edit completed successfully")
            
            if edit_result.diff:
                output.append(f"\n📋 Changes made:")
                output.append(edit_result.diff)
        else:
            output.append(f"❌ Edit failed: {edit_result.error}")
        
        # Add impact analysis
        output.append(f"\n📊 Impact Analysis:")
        output.append(self._format_impact_analysis(impact))
        
        return "\n".join(output)
    
    def _format_impact_analysis(self, impact: Dict[str, Any]) -> str:
        """Format impact analysis information"""
        lines = []
        
        risk_emoji = {"low": "🟢", "medium": "🟡", "high": "🔴"}
        lines.append(f"   Risk Level: {risk_emoji.get(impact['risk_level'], '⚪')} {impact['risk_level'].upper()}")
        
        if impact["symbols_affected"]:
            lines.append(f"   Symbols affected: {len(impact['symbols_affected'])}")
            for symbol in impact["symbols_affected"][:3]:
                lines.append(f"     • {symbol}")
            if len(impact["symbols_affected"]) > 3:
                lines.append(f"     • ... and {len(impact['symbols_affected']) - 3} more")
        
        if impact["dependencies_affected"]:
            unique_deps = list(set(impact["dependencies_affected"]))
            lines.append(f"   Dependencies affected: {len(unique_deps)}")
        
        if impact["tests_to_update"]:
            unique_tests = list(set(impact["tests_to_update"]))
            lines.append(f"   Tests to update: {len(unique_tests)}")
            for test in unique_tests[:2]:
                lines.append(f"     • {test}")
        
        if impact["breaking_changes"]:
            lines.append(f"   ⚠️  Breaking changes:")
            for change in impact["breaking_changes"]:
                lines.append(f"     • {change}")
        
        return "\n".join(lines)
    
    def _format_symbol_context(self, symbol_context) -> str:
        """Format symbol context information"""
        output = [f"🔍 [SYMBOL CONTEXT]: {symbol_context.symbol_name}"]
        
        output.append(f"   Type: {symbol_context.symbol_type}")
        output.append(f"   Domain: {symbol_context.domain}")
        output.append(f"   File: {symbol_context.file_path}")
        output.append(f"   Role: {symbol_context.architectural_role}")
        
        if symbol_context.dependencies:
            output.append(f"   Dependencies ({len(symbol_context.dependencies)}):")
            for dep in symbol_context.dependencies[:5]:
                output.append(f"     • {dep}")
            if len(symbol_context.dependencies) > 5:
                output.append(f"     • ... and {len(symbol_context.dependencies) - 5} more")
        
        if symbol_context.usages:
            output.append(f"   Used in ({len(symbol_context.usages)}) files:")
            for usage in symbol_context.usages[:3]:
                output.append(f"     • {usage}")
            if len(symbol_context.usages) > 3:
                output.append(f"     • ... and {len(symbol_context.usages) - 3} more")
        
        if symbol_context.usage_patterns:
            output.append(f"   Patterns: {', '.join(symbol_context.usage_patterns)}")
        
        output.append(f"   Complexity: {symbol_context.complexity_score}/100")
        
        if symbol_context.related_symbols:
            output.append(f"   Related symbols:")
            for related in symbol_context.related_symbols[:5]:
                output.append(f"     • {related}")
        
        return "\n".join(output)
    
    def _format_symbol_impact_analysis(self, symbol_context) -> str:
        """Format impact analysis for a symbol"""
        output = [f"�� [IMPACT ANALYSIS]: {symbol_context.symbol_name}"]
        
        # Determine impact level
        impact_level = "low"
        if len(symbol_context.usages) > 10:
            impact_level = "medium"
        if "widely_used" in symbol_context.usage_patterns:
            impact_level = "high"
        
        risk_emoji = {"low": "🟢", "medium": "🟡", "high": "🔴"}
        output.append(f"   Impact Level: {risk_emoji[impact_level]} {impact_level.upper()}")
        
        output.append(f"   Used in {len(symbol_context.usages)} locations")
        output.append(f"   Has {len(symbol_context.dependencies)} dependencies")
        
        if "widely_used" in symbol_context.usage_patterns:
            output.append("   ⚠️  This symbol is widely used - changes may have broad impact")
        
        if symbol_context.complexity_score > 20:
            output.append("   ⚠️  High complexity - consider careful testing")
        
        # Show usage files
        if symbol_context.usages:
            output.append("   Files that would be affected:")
            for usage in symbol_context.usages[:5]:
                output.append(f"     • {usage}")
            if len(symbol_context.usages) > 5:
                output.append(f"     • ... and {len(symbol_context.usages) - 5} more")
        
        return "\n".join(output)
    
    def _format_file_impact_analysis(self, filepath: str) -> str:
        """Format impact analysis for a file"""
        output = [f"📊 [FILE IMPACT ANALYSIS]: {filepath}"]
        
        try:
            # Find symbols in the file
            file_symbols = [s for s in self.codebase.symbols if s.file and s.file.filepath == filepath]
            
            if not file_symbols:
                output.append("   No symbols found in this file")
                return "\n".join(output)
            
            total_usages = 0
            high_impact_symbols = []
            
            for symbol in file_symbols:
                symbol_context = self.context_layer.get_symbol_context(symbol.name)
                if symbol_context:
                    total_usages += len(symbol_context.usages)
                    if "widely_used" in symbol_context.usage_patterns:
                        high_impact_symbols.append(symbol.name)
            
            # Determine impact level
            impact_level = "low"
            if total_usages > 20:
                impact_level = "medium"
            if high_impact_symbols:
                impact_level = "high"
            
            risk_emoji = {"low": "🟢", "medium": "🟡", "high": "🔴"}
            output.append(f"   Impact Level: {risk_emoji[impact_level]} {impact_level.upper()}")
            
            output.append(f"   Contains {len(file_symbols)} symbols")
            output.append(f"   Total usage count: {total_usages}")
            
            if high_impact_symbols:
                output.append("   ⚠️  High-impact symbols:")
                for symbol in high_impact_symbols:
                    output.append(f"     • {symbol}")
            
            # Show symbols in file
            output.append("   Symbols in this file:")
            for symbol in file_symbols[:5]:
                output.append(f"     • {symbol.name}")
            if len(file_symbols) > 5:
                output.append(f"     • ... and {len(file_symbols) - 5} more")
                
        except Exception as e:
            output.append(f"   Error analyzing file: {str(e)}")
        
        return "\n".join(output)
