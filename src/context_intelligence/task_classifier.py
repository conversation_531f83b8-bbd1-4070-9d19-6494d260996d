"""
Task Classification for Agent Context Intelligence

Classifies agent queries and extracts relevant entities to determine
what context information should be provided.
"""

import re
from typing import List, Dict, Any
from dataclasses import dataclass


@dataclass
class TaskContext:
    """Context information extracted from an agent task"""
    intent: str
    entities: List[str]
    domain: str
    confidence: float
    keywords: List[str]


class TaskClassifier:
    """Classifies agent tasks and extracts semantic context"""
    
    # Intent classification patterns
    INTENT_PATTERNS = {
        "fix_bug": [
            "fix", "bug", "error", "issue", "broken", "failing", "crash",
            "exception", "problem", "debug", "resolve", "repair"
        ],
        "add_feature": [
            "add", "implement", "create", "new", "build", "develop",
            "feature", "functionality", "capability", "enhancement"
        ],
        "refactor": [
            "refactor", "clean", "improve", "optimize", "restructure",
            "reorganize", "simplify", "modernize", "update"
        ],
        "find_code": [
            "find", "where", "locate", "search", "show", "list",
            "get", "retrieve", "discover", "identify"
        ],
        "analyze": [
            "analyze", "understand", "explain", "how", "why", "what",
            "describe", "examine", "investigate", "study"
        ],
        "test": [
            "test", "testing", "spec", "verify", "validate", "check",
            "ensure", "confirm", "assert"
        ]
    }
    
    # Domain classification patterns
    DOMAIN_PATTERNS = {
        "authentication": [
            "auth", "login", "signin", "password", "credential", "verify",
            "authenticate", "authorization", "permission", "access"
        ],
        "user_management": [
            "user", "profile", "account", "member", "person", "identity",
            "registration", "signup", "onboarding"
        ],
        "data_access": [
            "database", "db", "model", "entity", "repository", "dao",
            "query", "sql", "orm", "persistence", "storage"
        ],
        "api": [
            "api", "endpoint", "route", "controller", "handler", "service",
            "rest", "graphql", "request", "response"
        ],
        "ui": [
            "ui", "interface", "component", "view", "template", "form",
            "button", "page", "screen", "frontend"
        ],
        "business_logic": [
            "business", "logic", "rule", "process", "workflow", "operation",
            "calculation", "algorithm", "validation"
        ]
    }
    
    # Code entity patterns (potential symbol names)
    CODE_ENTITY_PATTERNS = [
        r'\b[A-Z][a-zA-Z0-9]*\b',  # PascalCase (classes)
        r'\b[a-z][a-zA-Z0-9]*\b',  # camelCase (functions/variables)
        r'\b[a-z_][a-z0-9_]*\b',   # snake_case (functions/variables)
        r'\b[A-Z_][A-Z0-9_]*\b',   # CONSTANT_CASE
    ]
    
    def classify_intent(self, query: str) -> str:
        """Classify the intent of an agent query"""
        query_lower = query.lower()
        intent_scores = {}
        
        for intent, patterns in self.INTENT_PATTERNS.items():
            score = sum(1 for pattern in patterns if pattern in query_lower)
            if score > 0:
                intent_scores[intent] = score
        
        if not intent_scores:
            return "general"
        
        # Return intent with highest score
        return max(intent_scores.items(), key=lambda x: x[1])[0]
    
    def classify_domain(self, query: str) -> str:
        """Classify the domain/area of the query"""
        query_lower = query.lower()
        domain_scores = {}
        
        for domain, patterns in self.DOMAIN_PATTERNS.items():
            score = sum(1 for pattern in patterns if pattern in query_lower)
            if score > 0:
                domain_scores[domain] = score
        
        if not domain_scores:
            return "general"
        
        # Return domain with highest score
        return max(domain_scores.items(), key=lambda x: x[1])[0]
    
    def extract_entities(self, query: str) -> List[str]:
        """Extract potential code entities from query"""
        entities = []
        
        # Extract using regex patterns
        for pattern in self.CODE_ENTITY_PATTERNS:
            matches = re.findall(pattern, query)
            entities.extend(matches)
        
        # Filter out common words and duplicates
        common_words = {
            "the", "and", "or", "but", "in", "on", "at", "to", "for",
            "of", "with", "by", "from", "up", "about", "into", "through",
            "during", "before", "after", "above", "below", "between",
            "is", "are", "was", "were", "be", "been", "being", "have",
            "has", "had", "do", "does", "did", "will", "would", "could",
            "should", "may", "might", "can", "must", "shall", "this",
            "that", "these", "those", "a", "an", "all", "any", "some",
            "each", "every", "no", "not", "only", "own", "same", "so",
            "than", "too", "very", "just", "now", "here", "there", "when",
            "where", "why", "how", "what", "which", "who", "whom", "whose",
            "if", "then", "else", "while", "until", "since", "because",
            "although", "though", "unless", "whether", "both", "either",
            "neither", "nor", "but", "yet", "so", "for", "and", "or"
        }
        
        # Filter and deduplicate
        filtered_entities = []
        seen = set()
        
        for entity in entities:
            entity_lower = entity.lower()
            if (entity_lower not in common_words and 
                len(entity) > 2 and 
                entity_lower not in seen):
                filtered_entities.append(entity)
                seen.add(entity_lower)
        
        return filtered_entities[:10]  # Limit to top 10 entities
    
    def extract_keywords(self, query: str) -> List[str]:
        """Extract important keywords from query"""
        # Remove common words and extract meaningful terms
        words = re.findall(r'\b\w+\b', query.lower())
        
        # Common stop words to filter out
        stop_words = {
            "the", "and", "or", "but", "in", "on", "at", "to", "for",
            "of", "with", "by", "from", "is", "are", "was", "were",
            "be", "been", "being", "have", "has", "had", "do", "does",
            "did", "will", "would", "could", "should", "can", "this",
            "that", "these", "those", "a", "an", "all", "any", "some"
        }
        
        keywords = [word for word in words if word not in stop_words and len(word) > 2]
        return list(dict.fromkeys(keywords))  # Remove duplicates while preserving order
    
    def analyze_task(self, query: str) -> TaskContext:
        """Perform complete task analysis"""
        intent = self.classify_intent(query)
        domain = self.classify_domain(query)
        entities = self.extract_entities(query)
        keywords = self.extract_keywords(query)
        
        # Calculate confidence based on pattern matches
        confidence = self._calculate_confidence(query, intent, domain)
        
        return TaskContext(
            intent=intent,
            entities=entities,
            domain=domain,
            confidence=confidence,
            keywords=keywords
        )
    
    def _calculate_confidence(self, query: str, intent: str, domain: str) -> float:
        """Calculate confidence score for classification"""
        query_lower = query.lower()
        
        # Count pattern matches for intent
        intent_matches = 0
        if intent in self.INTENT_PATTERNS:
            intent_matches = sum(1 for pattern in self.INTENT_PATTERNS[intent] 
                               if pattern in query_lower)
        
        # Count pattern matches for domain
        domain_matches = 0
        if domain in self.DOMAIN_PATTERNS:
            domain_matches = sum(1 for pattern in self.DOMAIN_PATTERNS[domain]
                               if pattern in query_lower)
        
        # Calculate confidence (0.0 to 1.0)
        total_words = len(query_lower.split())
        total_matches = intent_matches + domain_matches
        
        if total_words == 0:
            return 0.0
        
        confidence = min(total_matches / total_words, 1.0)
        return round(confidence, 2)

