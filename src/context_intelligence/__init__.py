"""
Context Intelligence Layer for Autonomous Agent Enhancement

This module provides semantic context awareness for autonomous coding agents
without requiring any agent training or modifications.
"""

from .context_layer import ContextIntelligenceLayer
from .task_classifier import TaskClassifier
from .symbol_analyzer import SymbolAnalyzer
from .enhanced_tools import EnhancedAgentTools

__all__ = [
    "ContextIntelligenceLayer",
    "TaskClassifier", 
    "SymbolAnalyzer",
    "EnhancedAgentTools"
]

