"""
Symbol Analysis for Context Intelligence

Analyzes code symbols and their relationships to provide semantic context
without requiring embeddings or AI inference.
"""

from typing import List, Dict, Any, Optional, Set
from dataclasses import dataclass
from codegen.sdk.core.codebase import Codebase
from codegen.sdk.core.symbol import Symbol


@dataclass
class SymbolContext:
    """Context information about a symbol"""
    symbol_name: str
    symbol_type: str
    domain: str
    file_path: str
    dependencies: List[str]
    usages: List[str]
    related_symbols: List[str]
    architectural_role: str
    usage_patterns: List[str]
    complexity_score: int


class SymbolAnalyzer:
    """Analyzes symbols and their relationships for context intelligence"""
    
    # Domain classification patterns
    DOMAIN_INDICATORS = {
        "authentication": {
            "name_patterns": ["auth", "login", "signin", "verify", "credential", "password"],
            "dependency_patterns": ["hash", "bcrypt", "jwt", "token", "session", "cookie"],
            "file_patterns": ["auth", "login", "security", "credential"]
        },
        "authorization": {
            "name_patterns": ["permission", "role", "access", "authorize", "grant", "deny"],
            "dependency_patterns": ["rbac", "acl", "policy", "guard", "middleware"],
            "file_patterns": ["permission", "role", "access", "policy"]
        },
        "user_management": {
            "name_patterns": ["user", "profile", "account", "member", "person"],
            "dependency_patterns": ["registration", "signup", "onboard", "profile"],
            "file_patterns": ["user", "profile", "account", "member"]
        },
        "data_access": {
            "name_patterns": ["repository", "dao", "model", "entity", "query"],
            "dependency_patterns": ["database", "sql", "orm", "connection", "transaction"],
            "file_patterns": ["model", "repository", "dao", "entity", "db"]
        },
        "api": {
            "name_patterns": ["controller", "handler", "endpoint", "route", "api"],
            "dependency_patterns": ["request", "response", "http", "rest", "graphql"],
            "file_patterns": ["controller", "handler", "route", "api", "endpoint"]
        },
        "business_logic": {
            "name_patterns": ["service", "manager", "processor", "calculator", "validator"],
            "dependency_patterns": ["business", "logic", "rule", "process", "workflow"],
            "file_patterns": ["service", "business", "logic", "process"]
        },
        "ui": {
            "name_patterns": ["component", "view", "template", "form", "widget"],
            "dependency_patterns": ["react", "vue", "angular", "html", "css"],
            "file_patterns": ["component", "view", "template", "ui", "frontend"]
        },
        "testing": {
            "name_patterns": ["test", "spec", "mock", "stub", "fixture"],
            "dependency_patterns": ["jest", "mocha", "pytest", "junit", "assert"],
            "file_patterns": ["test", "spec", "__test__", "tests"]
        }
    }
    
    # Architectural role patterns
    ARCHITECTURAL_ROLES = {
        "entry_point": ["main", "app", "server", "index", "start"],
        "controller": ["controller", "handler", "endpoint", "route"],
        "service": ["service", "manager", "processor", "engine"],
        "repository": ["repository", "dao", "store", "persistence"],
        "model": ["model", "entity", "dto", "schema"],
        "utility": ["util", "helper", "tool", "common"],
        "configuration": ["config", "setting", "constant", "env"],
        "middleware": ["middleware", "interceptor", "filter", "guard"]
    }
    
    def __init__(self, codebase: Codebase):
        self.codebase = codebase
        self.symbol_index = self._build_symbol_index()
        self.domain_cache = {}
        self.relationship_cache = {}
    
    def _build_symbol_index(self) -> Dict[str, Symbol]:
        """Build fast lookup index for symbols"""
        index = {}
        for symbol in self.codebase.symbols:
            index[symbol.name] = symbol
            # Also index by lowercase for case-insensitive lookup
            index[symbol.name.lower()] = symbol
        return index
    
    def analyze_symbol_context(self, symbol_name: str) -> Optional[SymbolContext]:
        """Analyze complete context for a symbol"""
        symbol = self.symbol_index.get(symbol_name) or self.symbol_index.get(symbol_name.lower())
        
        if not symbol:
            return None
        
        # Get or calculate domain
        domain = self._get_symbol_domain(symbol)
        
        # Analyze relationships
        dependencies = [dep.name for dep in symbol.dependencies]
        
        # Fix usage access - usage has usage_symbol attribute
        usages = []
        for usage in symbol.usages:
            if hasattr(usage, 'usage_symbol') and hasattr(usage.usage_symbol, 'file'):
                usages.append(usage.usage_symbol.file.filepath)
            elif hasattr(usage, 'usage_symbol') and hasattr(usage.usage_symbol, 'filepath'):
                usages.append(usage.usage_symbol.filepath)
        
        related_symbols = self._find_related_symbols(symbol)
        
        # Determine architectural role
        architectural_role = self._determine_architectural_role(symbol)
        
        # Analyze usage patterns
        usage_patterns = self._analyze_usage_patterns(symbol)
        
        # Calculate complexity score
        complexity_score = self._calculate_complexity_score(symbol)
        
        return SymbolContext(
            symbol_name=symbol.name,
            symbol_type=self._determine_symbol_type(symbol),
            domain=domain,
            file_path=symbol.file.filepath if symbol.file else "",
            dependencies=dependencies,
            usages=usages,
            related_symbols=related_symbols,
            architectural_role=architectural_role,
            usage_patterns=usage_patterns,
            complexity_score=complexity_score
        )
    
    def find_symbols_by_domain(self, domain: str, limit: int = 20) -> List[SymbolContext]:
        """Find all symbols belonging to a specific domain"""
        domain_symbols = []
        
        for symbol in self.codebase.symbols:
            symbol_domain = self._get_symbol_domain(symbol)
            if symbol_domain == domain:
                context = self.analyze_symbol_context(symbol.name)
                if context:
                    domain_symbols.append(context)
                
                if len(domain_symbols) >= limit:
                    break
        
        return domain_symbols
    
    def find_related_symbols(self, symbol_name: str, max_depth: int = 2) -> List[str]:
        """Find symbols related to the given symbol through dependencies and usages"""
        if symbol_name in self.relationship_cache:
            return self.relationship_cache[symbol_name]
        
        symbol = self.symbol_index.get(symbol_name) or self.symbol_index.get(symbol_name.lower())
        if not symbol:
            return []
        
        related = set()
        visited = set()
        
        def traverse(current_symbol, depth):
            if depth > max_depth or current_symbol.name in visited:
                return
            
            visited.add(current_symbol.name)
            
            # Add dependencies
            for dep in current_symbol.dependencies:
                related.add(dep.name)
                if depth < max_depth:
                    traverse(dep, depth + 1)
            
            # Add symbols that use this one
            for usage in current_symbol.usages:
                if hasattr(usage, 'symbol') and usage.symbol:
                    related.add(usage.symbol.name)
                    if depth < max_depth:
                        traverse(usage.symbol, depth + 1)
        
        traverse(symbol, 0)
        related.discard(symbol.name)  # Remove self
        
        result = list(related)[:20]  # Limit results
        self.relationship_cache[symbol_name] = result
        return result
    
    def _get_symbol_domain(self, symbol: Symbol) -> str:
        """Determine the domain/category of a symbol"""
        if symbol.name in self.domain_cache:
            return self.domain_cache[symbol.name]
        
        name_lower = symbol.name.lower()
        file_path = symbol.file.filepath.lower() if symbol.file else ""
        dependencies = [dep.name.lower() for dep in symbol.dependencies]
        
        domain_scores = {}
        
        for domain, indicators in self.DOMAIN_INDICATORS.items():
            score = 0
            
            # Check name patterns
            for pattern in indicators["name_patterns"]:
                if pattern in name_lower:
                    score += 3
            
            # Check dependency patterns
            for pattern in indicators["dependency_patterns"]:
                if any(pattern in dep for dep in dependencies):
                    score += 2
            
            # Check file path patterns
            for pattern in indicators["file_patterns"]:
                if pattern in file_path:
                    score += 1
            
            if score > 0:
                domain_scores[domain] = score
        
        # Return domain with highest score, or "general" if no matches
        if domain_scores:
            domain = max(domain_scores.items(), key=lambda x: x[1])[0]
        else:
            domain = "general"
        
        self.domain_cache[symbol.name] = domain
        return domain
    
    def _determine_architectural_role(self, symbol: Symbol) -> str:
        """Determine the architectural role of a symbol"""
        name_lower = symbol.name.lower()
        file_path = symbol.file.filepath.lower() if symbol.file else ""
        
        for role, patterns in self.ARCHITECTURAL_ROLES.items():
            for pattern in patterns:
                if pattern in name_lower or pattern in file_path:
                    return role
        
        return "general"
    
    def _analyze_usage_patterns(self, symbol) -> List[str]:
        """Analyze how a symbol is used across the codebase"""
        patterns = []
        
        # Fix usage access pattern
        usage_files = []
        for usage in symbol.usages:
            if hasattr(usage, 'usage_symbol') and hasattr(usage.usage_symbol, 'file'):
                usage_files.append(usage.usage_symbol.file.filepath)
            elif hasattr(usage, 'usage_symbol') and hasattr(usage.usage_symbol, 'filepath'):
                usage_files.append(usage.usage_symbol.filepath)
        
        # Analyze usage distribution
        if len(usage_files) > 10:
            patterns.append("widely_used")
        elif len(usage_files) > 3:
            patterns.append("moderately_used")
        else:
            patterns.append("limited_use")
        
        # Analyze file type distribution
        file_types = set()
        for filepath in usage_files:
            if 'test' in filepath.lower():
                file_types.add('test')
            elif 'controller' in filepath.lower():
                file_types.add('controller')
            elif 'service' in filepath.lower():
                file_types.add('service')
            elif 'model' in filepath.lower():
                file_types.add('model')
        
        if file_types:
            patterns.extend([f"used_in_{ft}" for ft in file_types])
        
        return patterns
    
    def _calculate_complexity_score(self, symbol: Symbol) -> int:
        """Calculate a complexity score for the symbol"""
        score = 0
        
        # Base complexity from dependencies
        score += len(symbol.dependencies)
        
        # Complexity from usage count
        score += min(len(symbol.usages), 10)  # Cap at 10
        
        # Complexity from source code length (if available)
        if symbol.source:
            lines = len(symbol.source.split('\n'))
            score += min(lines // 10, 20)  # Cap at 20
        
        return score
    
    def _determine_symbol_type(self, symbol: Symbol) -> str:
        """Determine the type of symbol (function, class, variable, etc.)"""
        if not symbol.source:
            return "unknown"
        
        source_lower = symbol.source.lower().strip()
        
        if source_lower.startswith('class '):
            return "class"
        elif source_lower.startswith('def ') or source_lower.startswith('function '):
            return "function"
        elif source_lower.startswith('interface '):
            return "interface"
        elif source_lower.startswith('enum '):
            return "enum"
        elif '=' in source_lower and 'def ' not in source_lower and 'class ' not in source_lower:
            return "variable"
        else:
            return "unknown"
    
    def _find_related_symbols(self, symbol: Symbol) -> List[str]:
        """Find symbols related to the given symbol"""
        related = set()
        
        # Add direct dependencies
        for dep in symbol.dependencies:
            related.add(dep.name)
        
        # Add symbols in the same file
        if symbol.file:
            for file_symbol in symbol.file.symbols:
                if file_symbol.name != symbol.name:
                    related.add(file_symbol.name)
        
        # Add symbols with similar names (same domain)
        symbol_domain = self._get_symbol_domain(symbol)
        for other_symbol in self.codebase.symbols:
            if (other_symbol.name != symbol.name and 
                self._get_symbol_domain(other_symbol) == symbol_domain):
                related.add(other_symbol.name)
                if len(related) >= 10:  # Limit to prevent explosion
                    break
        
        return list(related)[:10]  # Return top 10
