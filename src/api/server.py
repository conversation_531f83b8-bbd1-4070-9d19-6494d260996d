"""
FastAPI server for the Rippr API.
"""

import os
import uvicorn
from fastapi import FastAPI
from fastapi.middleware.gzip import GZipMiddleware
from contextlib import asynccontextmanager

from .middleware.auth import AuthMiddleware
from .middleware.cors import setup_cors
from .middleware.rate_limit import setup_rate_limiting
from .routes import agents_router, chat_router, tools_router, health_router
from .websocket import chat_ws_router


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    # Startup
    print("🚀 Starting Rippr API server...")
    yield
    # Shutdown
    print("🛑 Shutting down Rippr API server...")


def create_app() -> FastAPI:
    """Create and configure the FastAPI application."""

    app = FastAPI(
        title="Rippr API",
        description="""
        REST API for the Rippr code agent framework.

        This API allows any frontend to interact with Rippr agents through HTTP endpoints.
        Features include:
        - Agent management (create, configure, manage agents)
        - Chat interface (send messages, get responses, streaming support)
        - Tool execution (direct access to codebase tools)
        - File operations (CRUD operations on files)
        - Search operations (search across the codebase)

        ## Authentication

        All endpoints (except /health and docs) require API key authentication.
        Include your API key in the Authorization header:

        ```
        Authorization: Bearer your-api-key-here
        ```

        ## Rate Limiting

        API requests are rate limited to prevent abuse. Default limit is 60 requests per minute.

        ## Streaming

        Chat endpoints support streaming responses via Server-Sent Events (SSE).
        Use the `/chat/{agent_id}/stream` endpoint for real-time responses.
        """,
        version="1.0.0",
        lifespan=lifespan,
        docs_url="/docs",
        redoc_url="/redoc"
    )

    # Setup middleware
    setup_cors(app)
    setup_rate_limiting(app)

    # Add gzip compression
    app.add_middleware(GZipMiddleware, minimum_size=1000)

    # Add authentication middleware (but allow it to be disabled for development)
    if os.getenv("RIPPR_DISABLE_AUTH", "false").lower() != "true":
        app.add_middleware(AuthMiddleware)

    # Include routers
    app.include_router(health_router)
    app.include_router(agents_router, prefix="/api/v1")
    app.include_router(chat_router, prefix="/api/v1")
    app.include_router(tools_router, prefix="/api/v1")
    app.include_router(chat_ws_router, prefix="/api/v1")

    return app


def start_server(
    host: str = "0.0.0.0",
    port: int = 8000,
    reload: bool = False,
    workers: int = 1
):
    """Start the API server."""

    # Get configuration from environment
    host = os.getenv("RIPPR_API_HOST", host)
    port = int(os.getenv("RIPPR_API_PORT", port))
    reload = os.getenv("RIPPR_API_RELOAD", str(reload)).lower() == "true"
    workers = int(os.getenv("RIPPR_API_WORKERS", workers))

    print(f"🌐 Starting server on {host}:{port}")
    print(f"📚 API documentation available at http://{host}:{port}/docs")

    # Configure uvicorn
    config = uvicorn.Config(
        "src.api.server:create_app",
        factory=True,
        host=host,
        port=port,
        reload=reload,
        workers=workers if not reload else 1,  # Workers > 1 not compatible with reload
        log_level="info",
        access_log=True
    )

    server = uvicorn.Server(config)
    server.run()


if __name__ == "__main__":
    start_server(reload=True)
