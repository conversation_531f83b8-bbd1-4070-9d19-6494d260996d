"""
Tool execution endpoints.
"""

from typing import List
from fastapi import APIRouter, HTTPException, Depends, Query
from ..models.tools import (
    ToolRequest, ToolResponse, ToolListResponse, 
    FileOperationRequest, SearchRequest, ToolCategory
)
from ..models.common import ErrorResponse
from ..middleware.auth import api_key_auth
from ..services.tool_service import ToolService

router = APIRouter(prefix="/tools", tags=["tools"])

# Global tool service instance
tool_service = ToolService()


@router.get("/", response_model=ToolListResponse)
async def list_tools(
    category: ToolCategory = None,
    api_key: str = Depends(api_key_auth)
):
    """
    Get a list of all available tools.
    
    - **category**: Optional filter by tool category
    """
    try:
        tools = await tool_service.list_tools(category)
        return tools
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/execute", response_model=ToolResponse)
async def execute_tool(
    request: ToolRequest,
    api_key: str = Depends(api_key_auth)
):
    """
    Execute a tool with the provided parameters.
    
    - **tool_name**: Name of the tool to execute
    - **parameters**: Tool-specific parameters
    - **agent_id**: Optional agent ID to use for execution context
    """
    try:
        result = await tool_service.execute_tool(request)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/file/view", response_model=ToolResponse)
async def view_file(
    filepath: str,
    start_line: int = None,
    end_line: int = None,
    line_numbers: bool = True,
    api_key: str = Depends(api_key_auth)
):
    """
    View the contents of a file.
    
    - **filepath**: Path to the file relative to workspace root
    - **start_line**: Starting line number (1-indexed, inclusive)
    - **end_line**: Ending line number (1-indexed, inclusive)
    - **line_numbers**: Whether to include line numbers
    """
    try:
        request = ToolRequest(
            tool_name="view_file",
            parameters={
                "filepath": filepath,
                "start_line": start_line,
                "end_line": end_line,
                "line_numbers": line_numbers
            }
        )
        result = await tool_service.execute_tool(request)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/file/edit", response_model=ToolResponse)
async def edit_file(
    filepath: str,
    content: str,
    api_key: str = Depends(api_key_auth)
):
    """
    Edit the contents of a file.
    
    - **filepath**: Path to the file relative to workspace root
    - **content**: New content for the file
    """
    try:
        request = ToolRequest(
            tool_name="edit_file",
            parameters={
                "filepath": filepath,
                "content": content
            }
        )
        result = await tool_service.execute_tool(request)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/file/create", response_model=ToolResponse)
async def create_file(
    filepath: str,
    content: str = "",
    api_key: str = Depends(api_key_auth)
):
    """
    Create a new file.
    
    - **filepath**: Path for the new file relative to workspace root
    - **content**: Initial content for the file
    """
    try:
        request = ToolRequest(
            tool_name="create_file",
            parameters={
                "filepath": filepath,
                "content": content
            }
        )
        result = await tool_service.execute_tool(request)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/file/{filepath:path}", response_model=ToolResponse)
async def delete_file(
    filepath: str,
    api_key: str = Depends(api_key_auth)
):
    """
    Delete a file.
    
    - **filepath**: Path to the file to delete
    """
    try:
        request = ToolRequest(
            tool_name="delete_file",
            parameters={"filepath": filepath}
        )
        result = await tool_service.execute_tool(request)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/directory", response_model=ToolResponse)
async def list_directory(
    dirpath: str = "./",
    depth: int = 1,
    api_key: str = Depends(api_key_auth)
):
    """
    List contents of a directory.
    
    - **dirpath**: Path to the directory
    - **depth**: Depth of directory traversal
    """
    try:
        request = ToolRequest(
            tool_name="list_directory",
            parameters={
                "dirpath": dirpath,
                "depth": depth
            }
        )
        result = await tool_service.execute_tool(request)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/search", response_model=ToolResponse)
async def search_codebase(
    query: str,
    file_extensions: List[str] = None,
    use_regex: bool = False,
    page: int = 1,
    page_size: int = 10,
    api_key: str = Depends(api_key_auth)
):
    """
    Search the codebase for text or patterns.
    
    - **query**: Search query or regex pattern
    - **file_extensions**: List of file extensions to search (e.g., [".py", ".js"])
    - **use_regex**: Whether to use regex for search
    - **page**: Page number for results (1-based)
    - **page_size**: Number of results per page
    """
    try:
        request = ToolRequest(
            tool_name="search",
            parameters={
                "query": query,
                "file_extensions": file_extensions,
                "use_regex": use_regex,
                "page": page,
                "files_per_page": page_size
            }
        )
        result = await tool_service.execute_tool(request)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
