"""
Health check endpoints.
"""

import time
from datetime import datetime
from fastapi import APIRouter
from ..models.common import HealthResponse

router = APIRouter(tags=["health"])

# Track server start time
_start_time = time.time()


@router.get("/health", response_model=HealthResponse)
async def health_check():
    """
    Health check endpoint to verify API is running.
    
    Returns basic service information including status, version, and uptime.
    """
    current_time = time.time()
    uptime = current_time - _start_time
    
    return HealthResponse(
        status="healthy",
        version="1.0.0",
        timestamp=datetime.utcnow().isoformat(),
        uptime=uptime
    )
