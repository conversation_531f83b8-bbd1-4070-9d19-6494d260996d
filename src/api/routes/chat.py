"""
Chat endpoints for interacting with agents.
"""

import uuid
from datetime import datetime
from typing import Async<PERSON>enerator
from fastapi import <PERSON><PERSON>out<PERSON>, HTTPException, Depends
from fastapi.responses import StreamingResponse
from ..models.chat import (
    ChatRequest, ChatResponse, ChatHistoryRequest, 
    ChatHistoryResponse, StreamingChatChunk
)
from ..models.common import ErrorResponse
from ..middleware.auth import api_key_auth
from ..services.agent_manager import Agent<PERSON>anager
from ..services.chat_service import ChatService

router = APIRouter(prefix="/chat", tags=["chat"])

# Global service instances
agent_manager = AgentManager()
chat_service = ChatService(agent_manager)


@router.post("/{agent_id}", response_model=ChatResponse)
async def send_message(
    agent_id: str,
    request: ChatRequest,
    api_key: str = Depends(api_key_auth)
):
    """
    Send a message to an agent and get a response.
    
    - **agent_id**: ID of the agent to chat with
    - **message**: The message to send
    - **thread_id**: Optional thread ID for conversation continuity
    - **stream**: Whether to enable streaming (for non-streaming endpoint)
    - **include_context**: Whether to include conversation context
    """
    try:
        # Verify agent exists
        agent_info = await agent_manager.get_agent(agent_id)
        if not agent_info:
            raise HTTPException(status_code=404, detail="Agent not found")
        
        # Send message and get response
        response = await chat_service.send_message(agent_id, request)
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{agent_id}/stream")
async def send_message_stream(
    agent_id: str,
    request: ChatRequest,
    api_key: str = Depends(api_key_auth)
):
    """
    Send a message to an agent and get a streaming response.
    
    - **agent_id**: ID of the agent to chat with
    - **message**: The message to send
    - **thread_id**: Optional thread ID for conversation continuity
    - **include_context**: Whether to include conversation context
    
    Returns a streaming response with Server-Sent Events (SSE).
    """
    try:
        # Verify agent exists
        agent_info = await agent_manager.get_agent(agent_id)
        if not agent_info:
            raise HTTPException(status_code=404, detail="Agent not found")
        
        # Create streaming response
        async def generate_stream():
            async for chunk in chat_service.send_message_stream(agent_id, request):
                yield f"data: {chunk.model_dump_json()}\n\n"
            yield "data: [DONE]\n\n"
        
        return StreamingResponse(
            generate_stream(),
            media_type="text/plain",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Content-Type": "text/event-stream",
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{agent_id}/history", response_model=ChatHistoryResponse)
async def get_chat_history(
    agent_id: str,
    thread_id: str,
    limit: int = 50,
    before_message_id: str = None,
    api_key: str = Depends(api_key_auth)
):
    """
    Get chat history for a specific thread.
    
    - **agent_id**: ID of the agent
    - **thread_id**: Thread ID to get history for
    - **limit**: Maximum number of messages to return (1-200)
    - **before_message_id**: Get messages before this message ID
    """
    try:
        # Verify agent exists
        agent_info = await agent_manager.get_agent(agent_id)
        if not agent_info:
            raise HTTPException(status_code=404, detail="Agent not found")
        
        # Validate limit
        if limit < 1 or limit > 200:
            raise HTTPException(status_code=400, detail="Limit must be between 1 and 200")
        
        # Get chat history
        history_request = ChatHistoryRequest(
            thread_id=thread_id,
            limit=limit,
            before_message_id=before_message_id
        )
        
        history = await chat_service.get_chat_history(agent_id, history_request)
        return history
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/{agent_id}/history/{thread_id}")
async def clear_chat_history(
    agent_id: str,
    thread_id: str,
    api_key: str = Depends(api_key_auth)
):
    """
    Clear chat history for a specific thread.
    
    - **agent_id**: ID of the agent
    - **thread_id**: Thread ID to clear history for
    """
    try:
        # Verify agent exists
        agent_info = await agent_manager.get_agent(agent_id)
        if not agent_info:
            raise HTTPException(status_code=404, detail="Agent not found")
        
        # Clear history
        success = await chat_service.clear_chat_history(agent_id, thread_id)
        if not success:
            raise HTTPException(status_code=404, detail="Thread not found")
        
        return {"success": True, "message": f"Chat history cleared for thread {thread_id}"}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
