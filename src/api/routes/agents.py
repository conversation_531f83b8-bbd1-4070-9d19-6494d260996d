"""
Agent management endpoints.
"""

import uuid
from datetime import datetime
from typing import Dict, List
from fastapi import APIRouter, HTTPException, Depends
from ..models.agent import (
    AgentCreateRequest, AgentResponse, AgentListResponse, 
    AgentUpdateRequest, AgentStatus, AgentType
)
from ..models.common import ErrorResponse, SuccessResponse
from ..middleware.auth import api_key_auth
from ..services.agent_manager import AgentManager

router = APIRouter(prefix="/agents", tags=["agents"])

# Global agent manager instance
agent_manager = AgentManager()


@router.post("/", response_model=AgentResponse)
async def create_agent(
    request: AgentCreateRequest,
    api_key: str = Depends(api_key_auth)
):
    """
    Create a new agent with the specified configuration.
    
    - **name**: Human-readable name for the agent
    - **config**: Agent configuration including type, model, and settings
    """
    try:
        agent_id = await agent_manager.create_agent(request.name, request.config)
        agent_info = await agent_manager.get_agent(agent_id)
        return agent_info
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/", response_model=AgentListResponse)
async def list_agents(api_key: str = Depends(api_key_auth)):
    """
    Get a list of all created agents.
    
    Returns information about all agents including their status and configuration.
    """
    try:
        agents = await agent_manager.list_agents()
        return AgentListResponse(agents=agents)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{agent_id}", response_model=AgentResponse)
async def get_agent(
    agent_id: str,
    api_key: str = Depends(api_key_auth)
):
    """
    Get information about a specific agent.
    
    - **agent_id**: Unique identifier of the agent
    """
    try:
        agent_info = await agent_manager.get_agent(agent_id)
        if not agent_info:
            raise HTTPException(status_code=404, detail="Agent not found")
        return agent_info
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.patch("/{agent_id}", response_model=AgentResponse)
async def update_agent(
    agent_id: str,
    request: AgentUpdateRequest,
    api_key: str = Depends(api_key_auth)
):
    """
    Update an existing agent.
    
    - **agent_id**: Unique identifier of the agent
    - **name**: New name for the agent (optional)
    - **status**: New status for the agent (optional)
    """
    try:
        updated_agent = await agent_manager.update_agent(agent_id, request)
        if not updated_agent:
            raise HTTPException(status_code=404, detail="Agent not found")
        return updated_agent
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/{agent_id}", response_model=SuccessResponse)
async def delete_agent(
    agent_id: str,
    api_key: str = Depends(api_key_auth)
):
    """
    Delete an agent.
    
    - **agent_id**: Unique identifier of the agent to delete
    """
    try:
        success = await agent_manager.delete_agent(agent_id)
        if not success:
            raise HTTPException(status_code=404, detail="Agent not found")
        return SuccessResponse(message=f"Agent {agent_id} deleted successfully")
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
