"""
Chat-related API models.
"""

from typing import Any, Dict, List, Optional
from pydantic import BaseModel, Field
from enum import Enum


class MessageRole(str, Enum):
    """Message roles in a conversation."""
    USER = "user"
    ASSISTANT = "assistant"
    SYSTEM = "system"


class MessageType(str, Enum):
    """Types of messages."""
    TEXT = "text"
    TOOL_CALL = "tool_call"
    TOOL_RESULT = "tool_result"
    ERROR = "error"


class ChatMessage(BaseModel):
    """A single chat message."""
    
    role: MessageRole = Field(..., description="Role of the message sender")
    content: str = Field(..., description="Message content")
    message_type: MessageType = Field(MessageType.TEXT, description="Type of message")
    timestamp: str = Field(..., description="Message timestamp")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional message metadata")


class ChatRequest(BaseModel):
    """Request to send a chat message."""
    
    message: str = Field(..., description="User message to send")
    thread_id: Optional[str] = Field(None, description="Thread ID for conversation continuity")
    stream: bool = Field(False, description="Enable streaming response")
    include_context: bool = Field(True, description="Include conversation context")


class ChatResponse(BaseModel):
    """Response from a chat message."""
    
    response: str = Field(..., description="Agent response")
    thread_id: str = Field(..., description="Thread ID for this conversation")
    message_id: str = Field(..., description="Unique message identifier")
    timestamp: str = Field(..., description="Response timestamp")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional response metadata")


class ChatHistoryRequest(BaseModel):
    """Request to get chat history."""
    
    thread_id: str = Field(..., description="Thread ID to get history for")
    limit: Optional[int] = Field(50, ge=1, le=200, description="Maximum number of messages to return")
    before_message_id: Optional[str] = Field(None, description="Get messages before this message ID")


class ChatHistoryResponse(BaseModel):
    """Response containing chat history."""
    
    messages: List[ChatMessage] = Field(..., description="List of chat messages")
    thread_id: str = Field(..., description="Thread ID")
    has_more: bool = Field(..., description="Whether there are more messages available")


class StreamingChatChunk(BaseModel):
    """A chunk of streaming chat response."""
    
    chunk: str = Field(..., description="Partial response content")
    is_final: bool = Field(False, description="Whether this is the final chunk")
    message_id: Optional[str] = Field(None, description="Message ID (only in final chunk)")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional chunk metadata")
