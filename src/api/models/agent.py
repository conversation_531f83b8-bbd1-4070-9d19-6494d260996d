"""
Agent-related API models.
"""

from typing import Any, Dict, List, Optional
from pydantic import BaseModel, Field
from enum import Enum


class AgentType(str, Enum):
    """Available agent types."""
    CHAT = "chat"
    CODE = "code"


class ModelProvider(str, Enum):
    """Available model providers."""
    COPILOT = "copilot"
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    VERTEX = "vertex"
    GOOGLE = "google"
    XAI = "xai"


class AgentStatus(str, Enum):
    """Agent status values."""
    ACTIVE = "active"
    INACTIVE = "inactive"
    ERROR = "error"


class AgentConfig(BaseModel):
    """Configuration for creating an agent."""
    
    agent_type: AgentType = Field(..., description="Type of agent to create")
    model_provider: ModelProvider = Field(ModelProvider.COPILOT, description="Model provider to use")
    model_name: Optional[str] = Field(None, description="Specific model name (uses provider default if not specified)")
    codebase_path: str = Field(".", description="Path to the codebase")
    memory: bool = Field(True, description="Enable conversation memory")
    interactive: bool = Field(True, description="Enable interactive mode")
    condensed_logging: bool = Field(True, description="Enable condensed logging")
    short_format: bool = Field(True, description="Use short format responses")
    additional_config: Optional[Dict[str, Any]] = Field(None, description="Additional configuration options")


class AgentCreateRequest(BaseModel):
    """Request to create a new agent."""
    
    name: str = Field(..., description="Human-readable name for the agent")
    config: AgentConfig = Field(..., description="Agent configuration")


class AgentResponse(BaseModel):
    """Response containing agent information."""
    
    id: str = Field(..., description="Unique agent identifier")
    name: str = Field(..., description="Human-readable agent name")
    agent_type: AgentType = Field(..., description="Type of agent")
    model_provider: ModelProvider = Field(..., description="Model provider")
    model_name: str = Field(..., description="Model name")
    status: AgentStatus = Field(..., description="Current agent status")
    codebase_path: str = Field(..., description="Path to the codebase")
    created_at: str = Field(..., description="Creation timestamp")
    last_used: Optional[str] = Field(None, description="Last used timestamp")


class AgentListResponse(BaseModel):
    """Response containing list of agents."""
    
    agents: List[AgentResponse] = Field(..., description="List of agents")


class AgentUpdateRequest(BaseModel):
    """Request to update an agent."""
    
    name: Optional[str] = Field(None, description="New name for the agent")
    status: Optional[AgentStatus] = Field(None, description="New status for the agent")
