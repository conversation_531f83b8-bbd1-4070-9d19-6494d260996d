"""
Tool-related API models.
"""

from typing import Any, Dict, List, Optional
from pydantic import BaseModel, Field
from enum import Enum


class ToolCategory(str, Enum):
    """Categories of available tools."""
    FILE_OPERATIONS = "file_operations"
    SEARCH = "search"
    EDIT = "edit"
    GIT = "git"
    ANALYSIS = "analysis"
    SYSTEM = "system"


class ToolParameter(BaseModel):
    """Definition of a tool parameter."""
    
    name: str = Field(..., description="Parameter name")
    type: str = Field(..., description="Parameter type")
    description: str = Field(..., description="Parameter description")
    required: bool = Field(..., description="Whether parameter is required")
    default: Optional[Any] = Field(None, description="Default value if any")


class ToolDefinition(BaseModel):
    """Definition of an available tool."""
    
    name: str = Field(..., description="Tool name")
    description: str = Field(..., description="Tool description")
    category: ToolCategory = Field(..., description="Tool category")
    parameters: List[ToolParameter] = Field(..., description="Tool parameters")


class ToolRequest(BaseModel):
    """Request to execute a tool."""
    
    tool_name: str = Field(..., description="Name of the tool to execute")
    parameters: Dict[str, Any] = Field(..., description="Tool parameters")
    agent_id: Optional[str] = Field(None, description="Agent ID to use for execution")


class ToolResponse(BaseModel):
    """Response from tool execution."""
    
    success: bool = Field(..., description="Whether tool execution was successful")
    result: Any = Field(..., description="Tool execution result")
    error: Optional[str] = Field(None, description="Error message if execution failed")
    execution_time: float = Field(..., description="Execution time in seconds")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional execution metadata")


class ToolListResponse(BaseModel):
    """Response containing list of available tools."""
    
    tools: List[ToolDefinition] = Field(..., description="List of available tools")
    categories: List[ToolCategory] = Field(..., description="Available tool categories")


class FileOperationRequest(BaseModel):
    """Request for file operations."""
    
    filepath: str = Field(..., description="Path to the file")
    operation: str = Field(..., description="Operation to perform")
    content: Optional[str] = Field(None, description="File content for write operations")
    start_line: Optional[int] = Field(None, description="Start line for partial operations")
    end_line: Optional[int] = Field(None, description="End line for partial operations")


class SearchRequest(BaseModel):
    """Request for search operations."""
    
    query: str = Field(..., description="Search query")
    file_extensions: Optional[List[str]] = Field(None, description="File extensions to search")
    use_regex: bool = Field(False, description="Use regex for search")
    page: int = Field(1, ge=1, description="Page number for results")
    page_size: int = Field(10, ge=1, le=50, description="Number of results per page")
