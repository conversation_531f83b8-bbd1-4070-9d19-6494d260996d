"""
Rate limiting middleware.
"""

import os
import time
from collections import defaultdict, deque
from typing import Dict, Deque
from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Request
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import Response


class RateLimitMiddleware(BaseHTTPMiddleware):
    """Simple in-memory rate limiting middleware."""
    
    def __init__(self, app, requests_per_minute: int = 60):
        super().__init__(app)
        self.requests_per_minute = requests_per_minute
        self.requests: Dict[str, Deque[float]] = defaultdict(deque)
    
    def _get_client_id(self, request: Request) -> str:
        """Get client identifier for rate limiting."""
        # Use API key if available, otherwise fall back to IP
        if hasattr(request.state, 'api_key'):
            return f"api_key:{request.state.api_key}"
        
        # Get client IP
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return f"ip:{forwarded_for.split(',')[0].strip()}"
        
        return f"ip:{request.client.host}"
    
    def _is_rate_limited(self, client_id: str) -> bool:
        """Check if client is rate limited."""
        now = time.time()
        minute_ago = now - 60
        
        # Clean old requests
        client_requests = self.requests[client_id]
        while client_requests and client_requests[0] < minute_ago:
            client_requests.popleft()
        
        # Check if rate limit exceeded
        if len(client_requests) >= self.requests_per_minute:
            return True
        
        # Add current request
        client_requests.append(now)
        return False
    
    async def dispatch(self, request: Request, call_next):
        """Process the request and check rate limits."""
        # Skip rate limiting for health check
        if request.url.path == "/health":
            return await call_next(request)
        
        client_id = self._get_client_id(request)
        
        if self._is_rate_limited(client_id):
            return Response(
                content='{"error": "Rate limit exceeded. Please try again later."}',
                status_code=429,
                media_type="application/json",
                headers={"Retry-After": "60"}
            )
        
        return await call_next(request)


def setup_rate_limiting(app: FastAPI) -> None:
    """Setup rate limiting middleware."""
    requests_per_minute = int(os.getenv("RIPPR_RATE_LIMIT", "60"))
    
    app.add_middleware(RateLimitMiddleware, requests_per_minute=requests_per_minute)
