"""
Authentication middleware for the API.
"""

import os
from typing import Optional
from fastapi import HTT<PERSON><PERSON>x<PERSON>, Security, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.requests import Request
from starlette.responses import Response


class AuthMiddleware(BaseHTTPMiddleware):
    """Middleware for API key authentication."""
    
    def __init__(self, app, api_keys: Optional[set] = None):
        super().__init__(app)
        self.api_keys = api_keys or self._load_api_keys()
        
    def _load_api_keys(self) -> set:
        """Load API keys from environment variables."""
        api_keys_str = os.getenv("RIPPR_API_KEYS", "")
        if not api_keys_str:
            # Generate a default API key for development
            default_key = os.getenv("RIPPR_DEFAULT_API_KEY", "rippr-dev-key-12345")
            return {default_key}
        
        return set(key.strip() for key in api_keys_str.split(",") if key.strip())
    
    async def dispatch(self, request: Request, call_next):
        """Process the request and validate API key."""
        # Skip auth for health check and docs endpoints
        if request.url.path in ["/health", "/docs", "/redoc", "/openapi.json"]:
            return await call_next(request)
        
        # Check for API key in header
        auth_header = request.headers.get("Authorization")
        if not auth_header:
            return Response(
                content='{"error": "Missing Authorization header"}',
                status_code=401,
                media_type="application/json"
            )
        
        # Validate API key format
        if not auth_header.startswith("Bearer "):
            return Response(
                content='{"error": "Invalid Authorization header format. Use: Bearer <api_key>"}',
                status_code=401,
                media_type="application/json"
            )
        
        api_key = auth_header[7:]  # Remove "Bearer " prefix
        
        # Validate API key
        if api_key not in self.api_keys:
            return Response(
                content='{"error": "Invalid API key"}',
                status_code=401,
                media_type="application/json"
            )
        
        # Add API key to request state for potential use in endpoints
        request.state.api_key = api_key
        
        return await call_next(request)


# Security scheme for FastAPI docs
security = HTTPBearer()


async def api_key_auth(credentials: HTTPAuthorizationCredentials = Security(security)) -> str:
    """
    Dependency for API key authentication in individual endpoints.
    Use this when you need more granular control over authentication.
    """
    api_keys_str = os.getenv("RIPPR_API_KEYS", "")
    if not api_keys_str:
        default_key = os.getenv("RIPPR_DEFAULT_API_KEY", "rippr-dev-key-12345")
        valid_keys = {default_key}
    else:
        valid_keys = set(key.strip() for key in api_keys_str.split(",") if key.strip())
    
    if credentials.credentials not in valid_keys:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid API key",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    return credentials.credentials
