"""
Tool execution service.
"""

import time
from typing import Dict, List, Optional, Any
from codegen import Codebase
from ..models.tools import (
    ToolRequest, ToolResponse, ToolListResponse, 
    ToolDefinition, ToolParameter, ToolCategory
)
from src.tools import (
    view_file, edit_file, create_file, delete_file, 
    list_directory, search, reveal_symbol
)


class ToolService:
    """Service for executing tools and managing tool definitions."""
    
    def __init__(self):
        self.codebase_cache: Dict[str, Codebase] = {}
        self.tool_definitions = self._initialize_tool_definitions()
    
    async def list_tools(self, category: Optional[ToolCategory] = None) -> ToolListResponse:
        """Get list of available tools, optionally filtered by category."""
        tools = self.tool_definitions
        
        if category:
            tools = [tool for tool in tools if tool.category == category]
        
        categories = list(set(tool.category for tool in self.tool_definitions))
        
        return ToolListResponse(tools=tools, categories=categories)
    
    async def execute_tool(self, request: ToolRequest) -> ToolResponse:
        """Execute a tool with the provided parameters."""
        start_time = time.time()
        
        try:
            # Get codebase (default to current directory if not specified)
            codebase_path = request.parameters.get("codebase_path", ".")
            codebase = self._get_codebase(codebase_path)
            
            # Execute the tool based on its name
            result = await self._execute_tool_by_name(request.tool_name, request.parameters, codebase)
            
            execution_time = time.time() - start_time
            
            return ToolResponse(
                success=True,
                result=result,
                execution_time=execution_time
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            
            return ToolResponse(
                success=False,
                result=None,
                error=str(e),
                execution_time=execution_time
            )
    
    async def _execute_tool_by_name(self, tool_name: str, parameters: Dict[str, Any], codebase: Codebase) -> Any:
        """Execute a specific tool by name."""
        
        if tool_name == "view_file":
            result = view_file(
                codebase=codebase,
                filepath=parameters["filepath"],
                line_numbers=parameters.get("line_numbers", True),
                start_line=parameters.get("start_line"),
                end_line=parameters.get("end_line"),
                max_lines=parameters.get("max_lines", 500)
            )
            return self._format_tool_result(result)
            
        elif tool_name == "edit_file":
            result = edit_file(
                codebase=codebase,
                filepath=parameters["filepath"],
                new_content=parameters["content"]
            )
            return self._format_tool_result(result)
            
        elif tool_name == "create_file":
            result = create_file(
                codebase=codebase,
                filepath=parameters["filepath"],
                content=parameters.get("content", "")
            )
            return self._format_tool_result(result)
            
        elif tool_name == "delete_file":
            result = delete_file(
                codebase=codebase,
                filepath=parameters["filepath"]
            )
            return self._format_tool_result(result)
            
        elif tool_name == "list_directory":
            result = list_directory(
                codebase=codebase,
                dirpath=parameters.get("dirpath", "./"),
                depth=parameters.get("depth", 1)
            )
            return self._format_tool_result(result)
            
        elif tool_name == "search":
            result = search(
                codebase=codebase,
                query=parameters["query"],
                file_extensions=parameters.get("file_extensions"),
                page=parameters.get("page", 1),
                files_per_page=parameters.get("files_per_page", 10),
                use_regex=parameters.get("use_regex", False)
            )
            return self._format_tool_result(result)
            
        elif tool_name == "reveal_symbol":
            result = reveal_symbol(
                codebase=codebase,
                symbol_name=parameters["symbol_name"]
            )
            return self._format_tool_result(result)
            
        else:
            raise ValueError(f"Unknown tool: {tool_name}")
    
    def _get_codebase(self, path: str) -> Codebase:
        """Get or create a codebase instance."""
        if path not in self.codebase_cache:
            self.codebase_cache[path] = Codebase(path)
        return self.codebase_cache[path]
    
    def _format_tool_result(self, result) -> Dict[str, Any]:
        """Format tool result for API response."""
        if hasattr(result, 'model_dump'):
            return result.model_dump()
        elif hasattr(result, '__dict__'):
            return result.__dict__
        else:
            return {"result": str(result)}
    
    def _initialize_tool_definitions(self) -> List[ToolDefinition]:
        """Initialize the list of available tool definitions."""
        return [
            ToolDefinition(
                name="view_file",
                description="View the contents of a file with optional line range",
                category=ToolCategory.FILE_OPERATIONS,
                parameters=[
                    ToolParameter(name="filepath", type="string", description="Path to the file", required=True),
                    ToolParameter(name="start_line", type="integer", description="Starting line number", required=False),
                    ToolParameter(name="end_line", type="integer", description="Ending line number", required=False),
                    ToolParameter(name="line_numbers", type="boolean", description="Include line numbers", required=False, default=True),
                    ToolParameter(name="max_lines", type="integer", description="Maximum lines to display", required=False, default=500)
                ]
            ),
            ToolDefinition(
                name="edit_file",
                description="Edit the contents of a file",
                category=ToolCategory.EDIT,
                parameters=[
                    ToolParameter(name="filepath", type="string", description="Path to the file", required=True),
                    ToolParameter(name="content", type="string", description="New file content", required=True)
                ]
            ),
            ToolDefinition(
                name="create_file",
                description="Create a new file with optional content",
                category=ToolCategory.FILE_OPERATIONS,
                parameters=[
                    ToolParameter(name="filepath", type="string", description="Path for the new file", required=True),
                    ToolParameter(name="content", type="string", description="Initial file content", required=False, default="")
                ]
            ),
            ToolDefinition(
                name="delete_file",
                description="Delete a file",
                category=ToolCategory.FILE_OPERATIONS,
                parameters=[
                    ToolParameter(name="filepath", type="string", description="Path to the file to delete", required=True)
                ]
            ),
            ToolDefinition(
                name="list_directory",
                description="List contents of a directory",
                category=ToolCategory.FILE_OPERATIONS,
                parameters=[
                    ToolParameter(name="dirpath", type="string", description="Path to the directory", required=False, default="./"),
                    ToolParameter(name="depth", type="integer", description="Directory traversal depth", required=False, default=1)
                ]
            ),
            ToolDefinition(
                name="search",
                description="Search the codebase for text or patterns",
                category=ToolCategory.SEARCH,
                parameters=[
                    ToolParameter(name="query", type="string", description="Search query", required=True),
                    ToolParameter(name="file_extensions", type="array", description="File extensions to search", required=False),
                    ToolParameter(name="use_regex", type="boolean", description="Use regex for search", required=False, default=False),
                    ToolParameter(name="page", type="integer", description="Page number", required=False, default=1),
                    ToolParameter(name="files_per_page", type="integer", description="Results per page", required=False, default=10)
                ]
            ),
            ToolDefinition(
                name="reveal_symbol",
                description="Find and reveal information about a symbol in the codebase",
                category=ToolCategory.ANALYSIS,
                parameters=[
                    ToolParameter(name="symbol_name", type="string", description="Name of the symbol to reveal", required=True)
                ]
            )
        ]
