"""
Agent management service.
"""

import uuid
from datetime import datetime
from typing import Dict, List, Optional
from codegen import Codebase
from ..models.agent import (
    AgentConfig, AgentResponse, AgentUpdateRequest, 
    AgentStatus, AgentType, ModelProvider
)
from src.agents.chat_agent import ChatAgent
from src.agents.code_agent import CodeAgent


class AgentManager:
    """Manages agent instances and their lifecycle."""
    
    def __init__(self):
        self.agents: Dict[str, dict] = {}
        self.agent_instances: Dict[str, object] = {}
    
    async def create_agent(self, name: str, config: AgentConfig) -> str:
        """Create a new agent with the given configuration."""
        agent_id = str(uuid.uuid4())
        
        try:
            # Initialize codebase
            codebase = Codebase(config.codebase_path)
            
            # Create agent instance based on type
            if config.agent_type == AgentType.CHAT:
                agent_instance = ChatAgent(
                    codebase=codebase,
                    model_provider=config.model_provider.value,
                    model_name=config.model_name,
                    memory=config.memory,
                    interactive=config.interactive,
                    condensed_logging=config.condensed_logging,
                    short_format=config.short_format,
                    **(config.additional_config or {})
                )
            elif config.agent_type == AgentType.CODE:
                agent_instance = CodeAgent(
                    codebase=codebase,
                    model_provider=config.model_provider.value,
                    model_name=config.model_name,
                    memory=config.memory,
                    condensed_logging=config.condensed_logging,
                    short_format=config.short_format,
                    **(config.additional_config or {})
                )
            else:
                raise ValueError(f"Unknown agent type: {config.agent_type}")
            
            # Store agent information
            agent_info = {
                "id": agent_id,
                "name": name,
                "agent_type": config.agent_type,
                "model_provider": config.model_provider,
                "model_name": config.model_name or self._get_default_model(config.model_provider),
                "status": AgentStatus.ACTIVE,
                "codebase_path": config.codebase_path,
                "created_at": datetime.utcnow().isoformat(),
                "last_used": None,
                "config": config
            }
            
            self.agents[agent_id] = agent_info
            self.agent_instances[agent_id] = agent_instance
            
            return agent_id
            
        except Exception as e:
            raise Exception(f"Failed to create agent: {str(e)}")
    
    async def get_agent(self, agent_id: str) -> Optional[AgentResponse]:
        """Get agent information by ID."""
        if agent_id not in self.agents:
            return None
        
        agent_info = self.agents[agent_id]
        return AgentResponse(**agent_info)
    
    async def list_agents(self) -> List[AgentResponse]:
        """Get list of all agents."""
        return [AgentResponse(**agent_info) for agent_info in self.agents.values()]
    
    async def update_agent(self, agent_id: str, update_request: AgentUpdateRequest) -> Optional[AgentResponse]:
        """Update an existing agent."""
        if agent_id not in self.agents:
            return None
        
        agent_info = self.agents[agent_id]
        
        # Update fields if provided
        if update_request.name is not None:
            agent_info["name"] = update_request.name
        
        if update_request.status is not None:
            agent_info["status"] = update_request.status
            
            # If setting to inactive, we might want to cleanup resources
            if update_request.status == AgentStatus.INACTIVE:
                # Could add cleanup logic here
                pass
        
        return AgentResponse(**agent_info)
    
    async def delete_agent(self, agent_id: str) -> bool:
        """Delete an agent."""
        if agent_id not in self.agents:
            return False
        
        # Cleanup agent instance
        if agent_id in self.agent_instances:
            del self.agent_instances[agent_id]
        
        # Remove agent info
        del self.agents[agent_id]
        
        return True
    
    def get_agent_instance(self, agent_id: str) -> Optional[object]:
        """Get the actual agent instance for execution."""
        return self.agent_instances.get(agent_id)
    
    async def update_last_used(self, agent_id: str):
        """Update the last used timestamp for an agent."""
        if agent_id in self.agents:
            self.agents[agent_id]["last_used"] = datetime.utcnow().isoformat()
    
    def _get_default_model(self, provider: ModelProvider) -> str:
        """Get default model name for a provider."""
        defaults = {
            ModelProvider.COPILOT: "gpt-4.1",
            ModelProvider.OPENAI: "gpt-4",
            ModelProvider.ANTHROPIC: "claude-3-sonnet-20240229",
            ModelProvider.VERTEX: "gemini-2.0-flash-001",
            ModelProvider.GOOGLE: "gemini-pro",
            ModelProvider.XAI: "grok-beta"
        }
        return defaults.get(provider, "gpt-4")
