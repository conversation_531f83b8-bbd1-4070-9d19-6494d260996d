"""
Chat service for handling conversations with agents.
"""

import uuid
import async<PERSON>
from datetime import datetime
from typing import Async<PERSON>enerator, Dict, List, Optional
from ..models.chat import (
    ChatRequest, ChatResponse, ChatHistoryRequest, 
    ChatHistoryResponse, ChatMessage, MessageRole, 
    MessageType, StreamingChatChunk
)
from .agent_manager import AgentManager


class ChatService:
    """Service for handling chat interactions with agents."""
    
    def __init__(self, agent_manager: AgentManager):
        self.agent_manager = agent_manager
        # In-memory storage for chat history (in production, use a database)
        self.chat_history: Dict[str, List[ChatMessage]] = {}
    
    async def send_message(self, agent_id: str, request: ChatRequest) -> ChatResponse:
        """Send a message to an agent and get a response."""
        # Get agent instance
        agent = self.agent_manager.get_agent_instance(agent_id)
        if not agent:
            raise Exception("Agent not found or not available")
        
        # Generate thread ID if not provided
        thread_id = request.thread_id or str(uuid.uuid4())
        message_id = str(uuid.uuid4())
        
        # Store user message in history
        user_message = ChatMessage(
            role=MessageRole.USER,
            content=request.message,
            message_type=MessageType.TEXT,
            timestamp=datetime.utcnow().isoformat()
        )
        self._add_message_to_history(thread_id, user_message)
        
        try:
            # Send message to agent
            if hasattr(agent, 'run'):
                # For both ChatAgent and CodeAgent
                response_content = agent.run(request.message, thread_id=thread_id)
            else:
                raise Exception("Agent does not support message execution")
            
            # Store assistant response in history
            assistant_message = ChatMessage(
                role=MessageRole.ASSISTANT,
                content=response_content,
                message_type=MessageType.TEXT,
                timestamp=datetime.utcnow().isoformat()
            )
            self._add_message_to_history(thread_id, assistant_message)
            
            # Update agent last used timestamp
            await self.agent_manager.update_last_used(agent_id)
            
            return ChatResponse(
                response=response_content,
                thread_id=thread_id,
                message_id=message_id,
                timestamp=datetime.utcnow().isoformat()
            )
            
        except Exception as e:
            # Store error message in history
            error_message = ChatMessage(
                role=MessageRole.ASSISTANT,
                content=f"Error: {str(e)}",
                message_type=MessageType.ERROR,
                timestamp=datetime.utcnow().isoformat()
            )
            self._add_message_to_history(thread_id, error_message)
            raise Exception(f"Agent execution failed: {str(e)}")
    
    async def send_message_stream(self, agent_id: str, request: ChatRequest) -> AsyncGenerator[StreamingChatChunk, None]:
        """Send a message to an agent and get a streaming response."""
        # Get agent instance
        agent = self.agent_manager.get_agent_instance(agent_id)
        if not agent:
            raise Exception("Agent not found or not available")
        
        # Generate thread ID if not provided
        thread_id = request.thread_id or str(uuid.uuid4())
        message_id = str(uuid.uuid4())
        
        # Store user message in history
        user_message = ChatMessage(
            role=MessageRole.USER,
            content=request.message,
            message_type=MessageType.TEXT,
            timestamp=datetime.utcnow().isoformat()
        )
        self._add_message_to_history(thread_id, user_message)
        
        try:
            # For streaming, we'll simulate streaming by running the agent
            # and yielding chunks. In a real implementation, you'd want
            # the agent to support native streaming.
            
            # Run agent in a separate task to avoid blocking
            response_content = ""
            
            if hasattr(agent, 'run'):
                # Execute agent
                full_response = agent.run(request.message, thread_id=thread_id, stream_output=False)
                
                # Simulate streaming by breaking response into chunks
                words = full_response.split()
                current_chunk = ""
                
                for i, word in enumerate(words):
                    current_chunk += word + " "
                    
                    # Yield chunk every few words or at the end
                    if len(current_chunk.split()) >= 5 or i == len(words) - 1:
                        is_final = i == len(words) - 1
                        
                        yield StreamingChatChunk(
                            chunk=current_chunk.strip(),
                            is_final=is_final,
                            message_id=message_id if is_final else None
                        )
                        
                        response_content += current_chunk
                        current_chunk = ""
                        
                        # Small delay to simulate streaming
                        await asyncio.sleep(0.1)
                
                # Store assistant response in history
                assistant_message = ChatMessage(
                    role=MessageRole.ASSISTANT,
                    content=full_response,
                    message_type=MessageType.TEXT,
                    timestamp=datetime.utcnow().isoformat()
                )
                self._add_message_to_history(thread_id, assistant_message)
                
                # Update agent last used timestamp
                await self.agent_manager.update_last_used(agent_id)
                
            else:
                raise Exception("Agent does not support message execution")
                
        except Exception as e:
            # Store error message in history
            error_message = ChatMessage(
                role=MessageRole.ASSISTANT,
                content=f"Error: {str(e)}",
                message_type=MessageType.ERROR,
                timestamp=datetime.utcnow().isoformat()
            )
            self._add_message_to_history(thread_id, error_message)
            
            # Yield error chunk
            yield StreamingChatChunk(
                chunk=f"Error: {str(e)}",
                is_final=True,
                message_id=message_id
            )
    
    async def get_chat_history(self, agent_id: str, request: ChatHistoryRequest) -> ChatHistoryResponse:
        """Get chat history for a thread."""
        thread_messages = self.chat_history.get(request.thread_id, [])
        
        # Apply pagination
        if request.before_message_id:
            # Find the index of the before_message_id and slice from there
            # For simplicity, we'll just return all messages for now
            pass
        
        # Limit messages
        limited_messages = thread_messages[-request.limit:] if request.limit else thread_messages
        
        has_more = len(thread_messages) > len(limited_messages)
        
        return ChatHistoryResponse(
            messages=limited_messages,
            thread_id=request.thread_id,
            has_more=has_more
        )
    
    async def clear_chat_history(self, agent_id: str, thread_id: str) -> bool:
        """Clear chat history for a thread."""
        if thread_id in self.chat_history:
            del self.chat_history[thread_id]
            return True
        return False
    
    def _add_message_to_history(self, thread_id: str, message: ChatMessage):
        """Add a message to the chat history."""
        if thread_id not in self.chat_history:
            self.chat_history[thread_id] = []
        
        self.chat_history[thread_id].append(message)
