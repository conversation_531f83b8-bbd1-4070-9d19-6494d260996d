"""
Configuration management for the API.
"""

import os
from typing import List, Optional
from pydantic import BaseSettings, Field


class APIConfig(BaseSettings):
    """API configuration settings."""
    
    # Server settings
    host: str = Field(default="0.0.0.0", env="RIPPR_API_HOST")
    port: int = Field(default=8000, env="RIPPR_API_PORT")
    reload: bool = Field(default=False, env="RIPPR_API_RELOAD")
    workers: int = Field(default=1, env="RIPPR_API_WORKERS")
    
    # Authentication settings
    api_keys: Optional[str] = Field(default=None, env="RIPPR_API_KEYS")
    default_api_key: str = Field(default="rippr-dev-key-12345", env="RIPPR_DEFAULT_API_KEY")
    disable_auth: bool = Field(default=False, env="RIPPR_DISABLE_AUTH")
    
    # CORS settings
    cors_origins: str = Field(default="*", env="RIPPR_CORS_ORIGINS")
    
    # Rate limiting
    rate_limit: int = Field(default=60, env="RIPPR_RATE_LIMIT")
    
    # Logging
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
    
    def get_api_keys(self) -> set:
        """Get the set of valid API keys."""
        if self.api_keys:
            return set(key.strip() for key in self.api_keys.split(",") if key.strip())
        return {self.default_api_key}
    
    def get_cors_origins(self) -> List[str]:
        """Get the list of allowed CORS origins."""
        if self.cors_origins == "*":
            return ["*"]
        return [origin.strip() for origin in self.cors_origins.split(",") if origin.strip()]


# Global configuration instance
config = APIConfig()
