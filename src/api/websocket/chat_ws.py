"""
WebSocket endpoints for real-time chat.
"""

import json
import uuid
from typing import Dict
from fastapi import <PERSON>Rout<PERSON>, WebSocket, WebSocketDisconnect, Query
from ..services.agent_manager import AgentManager
from ..services.chat_service import ChatService
from ..models.chat import Chat<PERSON>e<PERSON>, StreamingChatChunk

router = APIRouter()

# Global service instances
agent_manager = AgentManager()
chat_service = ChatService(agent_manager)

# Active WebSocket connections
active_connections: Dict[str, WebSocket] = {}


class ConnectionManager:
    """Manages WebSocket connections."""
    
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
    
    async def connect(self, websocket: WebSocket, connection_id: str):
        """Accept a WebSocket connection."""
        await websocket.accept()
        self.active_connections[connection_id] = websocket
    
    def disconnect(self, connection_id: str):
        """Remove a WebSocket connection."""
        if connection_id in self.active_connections:
            del self.active_connections[connection_id]
    
    async def send_message(self, connection_id: str, message: dict):
        """Send a message to a specific connection."""
        if connection_id in self.active_connections:
            websocket = self.active_connections[connection_id]
            await websocket.send_text(json.dumps(message))
    
    async def send_chunk(self, connection_id: str, chunk: StreamingChatChunk):
        """Send a streaming chunk to a connection."""
        await self.send_message(connection_id, {
            "type": "chunk",
            "data": chunk.model_dump()
        })
    
    async def send_error(self, connection_id: str, error: str):
        """Send an error message to a connection."""
        await self.send_message(connection_id, {
            "type": "error",
            "error": error
        })


manager = ConnectionManager()


@router.websocket("/ws/chat/{agent_id}")
async def websocket_chat(
    websocket: WebSocket,
    agent_id: str,
    api_key: str = Query(..., description="API key for authentication")
):
    """
    WebSocket endpoint for real-time chat with an agent.
    
    Send messages as JSON:
    {
        "message": "Your message here",
        "thread_id": "optional-thread-id"
    }
    
    Receive streaming responses as JSON chunks:
    {
        "type": "chunk",
        "data": {
            "chunk": "partial response",
            "is_final": false,
            "message_id": "msg-id"
        }
    }
    """
    connection_id = str(uuid.uuid4())
    
    try:
        # Validate API key (simple validation for WebSocket)
        # In production, you'd want more robust auth
        import os
        api_keys_str = os.getenv("RIPPR_API_KEYS", "")
        if not api_keys_str:
            default_key = os.getenv("RIPPR_DEFAULT_API_KEY", "rippr-dev-key-12345")
            valid_keys = {default_key}
        else:
            valid_keys = set(key.strip() for key in api_keys_str.split(",") if key.strip())
        
        if api_key not in valid_keys:
            await websocket.close(code=1008, reason="Invalid API key")
            return
        
        # Verify agent exists
        agent_info = await agent_manager.get_agent(agent_id)
        if not agent_info:
            await websocket.close(code=1008, reason="Agent not found")
            return
        
        # Accept connection
        await manager.connect(websocket, connection_id)
        
        # Send connection confirmation
        await manager.send_message(connection_id, {
            "type": "connected",
            "agent_id": agent_id,
            "agent_name": agent_info.name
        })
        
        while True:
            # Wait for message from client
            data = await websocket.receive_text()
            
            try:
                message_data = json.loads(data)
                
                # Validate message format
                if "message" not in message_data:
                    await manager.send_error(connection_id, "Missing 'message' field")
                    continue
                
                # Create chat request
                chat_request = ChatRequest(
                    message=message_data["message"],
                    thread_id=message_data.get("thread_id"),
                    stream=True,
                    include_context=message_data.get("include_context", True)
                )
                
                # Send acknowledgment
                await manager.send_message(connection_id, {
                    "type": "message_received",
                    "thread_id": chat_request.thread_id
                })
                
                # Stream response
                async for chunk in chat_service.send_message_stream(agent_id, chat_request):
                    await manager.send_chunk(connection_id, chunk)
                
                # Send completion signal
                await manager.send_message(connection_id, {
                    "type": "message_complete"
                })
                
            except json.JSONDecodeError:
                await manager.send_error(connection_id, "Invalid JSON format")
            except Exception as e:
                await manager.send_error(connection_id, f"Error processing message: {str(e)}")
    
    except WebSocketDisconnect:
        manager.disconnect(connection_id)
    except Exception as e:
        await manager.send_error(connection_id, f"Connection error: {str(e)}")
        manager.disconnect(connection_id)
