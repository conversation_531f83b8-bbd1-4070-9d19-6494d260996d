#!/usr/bin/env python3
"""
CLI for starting the Rippr API server.
"""

import argparse
import os
import sys
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent.parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

try:
    from dotenv import load_dotenv
    load_dotenv()
    load_dotenv(".env.local")
except ImportError:
    pass

from src.api.server import start_server


def main():
    """Main CLI entry point for the API server."""
    parser = argparse.ArgumentParser(
        description="Rippr API Server - REST API for the Rippr code agent framework",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Environment Variables:
  RIPPR_API_HOST              Server host (default: 0.0.0.0)
  RIPPR_API_PORT              Server port (default: 8000)
  RIPPR_API_RELOAD            Enable auto-reload for development (default: false)
  RIPPR_API_WORKERS           Number of worker processes (default: 1)
  RIPPR_API_KEYS              Comma-separated list of valid API keys
  RIPPR_DEFAULT_API_KEY       Default API key for development (default: rippr-dev-key-12345)
  RIPPR_DISABLE_AUTH          Disable authentication for development (default: false)
  RIPPR_CORS_ORIGINS          Allowed CORS origins (default: *)
  RIPPR_RATE_LIMIT            Requests per minute rate limit (default: 60)

Examples:
  rippr-api                                    # Start server with defaults
  rippr-api --host 127.0.0.1 --port 3000      # Custom host and port
  rippr-api --reload --workers 1              # Development mode with auto-reload
  rippr-api --disable-auth                     # Disable authentication for testing
        """
    )
    
    parser.add_argument(
        "--host",
        default=os.getenv("RIPPR_API_HOST", "0.0.0.0"),
        help="Host to bind the server to (default: 0.0.0.0)"
    )
    
    parser.add_argument(
        "--port",
        type=int,
        default=int(os.getenv("RIPPR_API_PORT", "8000")),
        help="Port to bind the server to (default: 8000)"
    )
    
    parser.add_argument(
        "--reload",
        action="store_true",
        default=os.getenv("RIPPR_API_RELOAD", "false").lower() == "true",
        help="Enable auto-reload for development"
    )
    
    parser.add_argument(
        "--workers",
        type=int,
        default=int(os.getenv("RIPPR_API_WORKERS", "1")),
        help="Number of worker processes (default: 1, ignored if --reload is used)"
    )
    
    parser.add_argument(
        "--disable-auth",
        action="store_true",
        help="Disable authentication (sets RIPPR_DISABLE_AUTH=true)"
    )
    
    parser.add_argument(
        "--api-key",
        help="Set a custom API key (sets RIPPR_DEFAULT_API_KEY)"
    )
    
    parser.add_argument(
        "--cors-origins",
        help="Comma-separated list of allowed CORS origins (sets RIPPR_CORS_ORIGINS)"
    )
    
    parser.add_argument(
        "--rate-limit",
        type=int,
        help="Requests per minute rate limit (sets RIPPR_RATE_LIMIT)"
    )
    
    args = parser.parse_args()
    
    # Set environment variables based on CLI args
    if args.disable_auth:
        os.environ["RIPPR_DISABLE_AUTH"] = "true"
        print("🔓 Authentication disabled for this session")
    
    if args.api_key:
        os.environ["RIPPR_DEFAULT_API_KEY"] = args.api_key
        print(f"🔑 Using custom API key: {args.api_key}")
    
    if args.cors_origins:
        os.environ["RIPPR_CORS_ORIGINS"] = args.cors_origins
        print(f"🌐 CORS origins set to: {args.cors_origins}")
    
    if args.rate_limit:
        os.environ["RIPPR_RATE_LIMIT"] = str(args.rate_limit)
        print(f"⏱️  Rate limit set to: {args.rate_limit} requests/minute")
    
    # Print configuration
    print("🚀 Starting Rippr API Server")
    print("=" * 50)
    print(f"Host: {args.host}")
    print(f"Port: {args.port}")
    print(f"Reload: {args.reload}")
    print(f"Workers: {args.workers if not args.reload else 1}")
    print(f"Auth: {'Disabled' if args.disable_auth else 'Enabled'}")
    
    if not args.disable_auth:
        api_key = os.getenv("RIPPR_DEFAULT_API_KEY", "rippr-dev-key-12345")
        print(f"API Key: {api_key}")
    
    print("=" * 50)
    print(f"📚 API Documentation: http://{args.host}:{args.port}/docs")
    print(f"🔄 Alternative Docs: http://{args.host}:{args.port}/redoc")
    print(f"❤️  Health Check: http://{args.host}:{args.port}/health")
    print("=" * 50)
    
    # Start the server
    try:
        start_server(
            host=args.host,
            port=args.port,
            reload=args.reload,
            workers=args.workers
        )
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
    except Exception as e:
        print(f"❌ Failed to start server: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
