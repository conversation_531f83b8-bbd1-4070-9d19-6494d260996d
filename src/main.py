#!/usr/bin/env python3
"""
Main entry point for the Rippr package when installed.
This module creates a console script entry point that can be run from anywhere.
"""

import os
import sys
from pathlib import Path

def ensure_project_in_path():
    """Ensure the project root is in sys.path."""
    # Try to find the project root by looking for specific markers
    current_dir = Path.cwd()
    
    # If we're in the project directory or a subdirectory
    script_dir = Path(__file__).parent.parent.resolve()
    
    # Add the script's parent directory (project root) to sys.path
    if script_dir not in [Path(p) for p in sys.path]:
        sys.path.insert(0, str(script_dir))
    
    return script_dir

def main():
    """Main entry point that works regardless of current directory."""
    # Ensure we can import from the project
    project_root = ensure_project_in_path()
    
    # Import the CLI main function and call it
    from src.cli import main as cli_main
    cli_main()

if __name__ == "__main__":
    main()