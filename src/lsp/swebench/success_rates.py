LITE_SUCCESS_RATES = {
    "pallets__flask-5063": 0.0,
    "sphinx-doc__sphinx-8282": 0.0,
    "django__django-14667": 0.0,
    "sphinx-doc__sphinx-8474": 0.0,
    "sympy__sympy-11400": 0.0,
    "sympy__sympy-11870": 0.0,
    "sympy__sympy-11897": 0.0,
    "sympy__sympy-12171": 0.0,
    "sympy__sympy-12236": 0.0,
    "sympy__sympy-13146": 0.0,
    "sympy__sympy-13773": 0.0,
    "sympy__sympy-13895": 0.0,
    "django__django-13220": 0.0,
    "sympy__sympy-13915": 0.0,
    "sympy__sympy-14024": 0.0,
    "sympy__sympy-14308": 0.0,
    "django__django-14730": 0.0,
    "sphinx-doc__sphinx-7738": 0.0,
    "sphinx-doc__sphinx-7686": 0.0,
    "django__django-14997": 0.0,
    "matplotlib__matplotlib-25079": 0.0,
    "pydata__xarray-4493": 0.0,
    "matplotlib__matplotlib-22835": 0.0,
    "matplotlib__matplotlib-18869": 0.0,
    "pylint-dev__pylint-7228": 0.0,
    "pytest-dev__pytest-5103": 0.0,
    "pytest-dev__pytest-5221": 0.0,
    "sympy__sympy-14317": 0.0,
    "django__django-16820": 0.0,
    "django__django-16229": 0.0,
    "pytest-dev__pytest-9359": 0.0,
    "scikit-learn__scikit-learn-10508": 0.0,
    "scikit-learn__scikit-learn-10949": 0.0,
    "scikit-learn__scikit-learn-11040": 0.0,
    "django__django-15695": 0.0,
    "scikit-learn__scikit-learn-25638": 0.0,
    "django__django-16816": 0.0,
    "sympy__sympy-15308": 0.0,
    "matplotlib__matplotlib-25433": 0.0,
    "sympy__sympy-18087": 0.0,
    "astropy__astropy-7746": 0.0,
    "django__django-11630": 0.0,
    "sympy__sympy-18199": 0.0,
    "sympy__sympy-23191": 0.0,
    "sympy__sympy-17630": 0.0,
    "sympy__sympy-19254": 0.0,
    "sympy__sympy-21627": 0.0,
    "sympy__sympy-16281": 0.0,
    "sympy__sympy-16106": 0.0,
    "sympy__sympy-24102": 0.0,
    "django__django-11905": 0.0,
    "sympy__sympy-21171": 0.0,
    "sympy__sympy-20639": 0.0,
    "django__django-12589": 0.0,
    "sympy__sympy-20322": 0.0,
    "django__django-11564": 0.0,
    "django__django-11019": 0.0,
    "django__django-16910": 0.02,
    "django__django-15252": 0.02,
    "pytest-dev__pytest-5413": 0.02,
    "django__django-11742": 0.02,
    "sphinx-doc__sphinx-8273": 0.02,
    "pytest-dev__pytest-8906": 0.02,
    "django__django-15996": 0.02,
    "sympy__sympy-19007": 0.02,
    "django__django-11910": 0.02,
    "matplotlib__matplotlib-22711": 0.02,
    "django__django-13768": 0.02,
    "astropy__astropy-14182": 0.02,
    "mwaskom__seaborn-3407": 0.02,
    "pallets__flask-4045": 0.02,
    "django__django-12908": 0.02,
    "pallets__flask-4992": 0.02,
    "pydata__xarray-3364": 0.02,
    "sympy__sympy-16503": 0.02,
    "django__django-15738": 0.02,
    "pydata__xarray-4248": 0.02,
    "django__django-13265": 0.02,
    "sympy__sympy-13177": 0.02,
    "django__django-13448": 0.02,
    "django__django-12113": 0.02,
    "sympy__sympy-13043": 0.02,
    "sympy__sympy-12454": 0.02,
    "sympy__sympy-13437": 0.02,
    "django__django-16408": 0.03,
    "pytest-dev__pytest-6116": 0.03,
    "pytest-dev__pytest-8365": 0.03,
    "psf__requests-2148": 0.03,
    "sympy__sympy-21612": 0.03,
    "astropy__astropy-14365": 0.03,
    "matplotlib__matplotlib-23299": 0.03,
    "django__django-11283": 0.03,
    "django__django-14155": 0.03,
    "sphinx-doc__sphinx-8506": 0.03,
    "django__django-11797": 0.03,
    "sympy__sympy-18698": 0.03,
    "django__django-15320": 0.03,
    "sphinx-doc__sphinx-10451": 0.03,
    "django__django-15388": 0.03,
    "sympy__sympy-20049": 0.03,
    "django__django-15781": 0.05,
    "django__django-13321": 0.05,
    "sympy__sympy-18835": 0.05,
    "django__django-14534": 0.05,
    "matplotlib__matplotlib-24265": 0.05,
    "django__django-15202": 0.05,
    "django__django-12856": 0.05,
    "matplotlib__matplotlib-23476": 0.05,
    "django__django-15061": 0.05,
    "sphinx-doc__sphinx-11445": 0.06,
    "django__django-12470": 0.06,
    "django__django-16400": 0.06,
    "sympy__sympy-15346": 0.06,
    "pytest-dev__pytest-5495": 0.06,
    "sphinx-doc__sphinx-8801": 0.08,
    "matplotlib__matplotlib-23563": 0.08,
    "sympy__sympy-21379": 0.08,
    "django__django-15819": 0.08,
    "mwaskom__seaborn-2848": 0.08,
    "scikit-learn__scikit-learn-25500": 0.08,
    "sympy__sympy-12419": 0.08,
    "django__django-12308": 0.09,
    "sympy__sympy-14396": 0.09,
    "sympy__sympy-15345": 0.09,
    "sympy__sympy-19487": 0.09,
    "pytest-dev__pytest-7168": 0.09,
    "scikit-learn__scikit-learn-25747": 0.09,
    "matplotlib__matplotlib-25498": 0.11,
    "sympy__sympy-22840": 0.11,
    "sphinx-doc__sphinx-8627": 0.11,
    "pydata__xarray-4094": 0.11,
    "pytest-dev__pytest-7220": 0.11,
    "django__django-12747": 0.11,
    "sympy__sympy-13031": 0.12,
    "django__django-13660": 0.12,
    "scikit-learn__scikit-learn-14983": 0.12,
    "sphinx-doc__sphinx-8435": 0.14,
    "sympy__sympy-20590": 0.14,
    "scikit-learn__scikit-learn-14087": 0.14,
    "sympy__sympy-24909": 0.14,
    "django__django-15400": 0.14,
    "matplotlib__matplotlib-25311": 0.14,
    "pylint-dev__pylint-6506": 0.15,
    "django__django-12125": 0.15,
    "matplotlib__matplotlib-24334": 0.15,
    "scikit-learn__scikit-learn-13497": 0.17,
    "sympy__sympy-16792": 0.17,
    "django__django-14580": 0.17,
    "pylint-dev__pylint-7080": 0.18,
    "matplotlib__matplotlib-25332": 0.18,
    "sympy__sympy-22005": 0.18,
    "sympy__sympy-20442": 0.2,
    "django__django-13551": 0.2,
    "sympy__sympy-14817": 0.2,
    "matplotlib__matplotlib-23987": 0.2,
    "django__django-13033": 0.21,
    "sphinx-doc__sphinx-7975": 0.21,
    "django__django-13925": 0.23,
    "sphinx-doc__sphinx-10325": 0.23,
    "sympy__sympy-16988": 0.23,
    "pytest-dev__pytest-7490": 0.24,
    "django__django-15213": 0.24,
    "django__django-12284": 0.24,
    "pytest-dev__pytest-11148": 0.24,
    "django__django-11964": 0.24,
    "pylint-dev__pylint-7114": 0.26,
    "django__django-11422": 0.26,
    "django__django-14017": 0.27,
    "django__django-15902": 0.27,
    "django__django-10924": 0.27,
    "django__django-13158": 0.29,
    "django__django-11620": 0.29,
    "sympy__sympy-13971": 0.29,
    "django__django-15498": 0.3,
    "django__django-12184": 0.3,
    "django__django-13964": 0.3,
    "psf__requests-1963": 0.3,
    "matplotlib__matplotlib-25442": 0.3,
    "django__django-13757": 0.32,
    "scikit-learn__scikit-learn-15512": 0.32,
    "sympy__sympy-21614": 0.33,
    "sympy__sympy-15609": 0.33,
    "matplotlib__matplotlib-23562": 0.33,
    "django__django-13315": 0.33,
    "django__django-11848": 0.35,
    "django__django-17087": 0.35,
    "matplotlib__matplotlib-26011": 0.36,
    "sympy__sympy-21055": 0.36,
    "sympy__sympy-17022": 0.36,
    "pylint-dev__pylint-7993": 0.36,
    "astropy__astropy-6938": 0.38,
    "sympy__sympy-15678": 0.38,
    "django__django-17051": 0.38,
    "scikit-learn__scikit-learn-14092": 0.38,
    "pylint-dev__pylint-5859": 0.39,
    "django__django-14411": 0.39,
    "django__django-11001": 0.41,
    "astropy__astropy-12907": 0.41,
    "sympy__sympy-18057": 0.42,
    "sympy__sympy-23262": 0.44,
    "sympy__sympy-18189": 0.44,
    "sympy__sympy-17139": 0.45,
    "django__django-15790": 0.45,
    "django__django-14999": 0.45,
    "sympy__sympy-18532": 0.47,
    "scikit-learn__scikit-learn-11281": 0.47,
    "django__django-12915": 0.47,
    "sympy__sympy-12481": 0.47,
    "sympy__sympy-24066": 0.48,
    "django__django-11815": 0.48,
    "django__django-13028": 0.48,
    "sympy__sympy-17655": 0.48,
    "django__django-12708": 0.48,
    "matplotlib__matplotlib-24970": 0.5,
    "mwaskom__seaborn-3190": 0.52,
    "scikit-learn__scikit-learn-13142": 0.52,
    "matplotlib__matplotlib-26020": 0.53,
    "scikit-learn__scikit-learn-15535": 0.53,
    "sympy__sympy-13471": 0.53,
    "sympy__sympy-15011": 0.53,
    "psf__requests-3362": 0.55,
    "matplotlib__matplotlib-24149": 0.55,
    "matplotlib__matplotlib-23314": 0.55,
    "django__django-14608": 0.56,
    "scikit-learn__scikit-learn-13241": 0.56,
    "scikit-learn__scikit-learn-25570": 0.56,
    "sympy__sympy-18621": 0.56,
    "scikit-learn__scikit-learn-13584": 0.56,
    "django__django-13401": 0.58,
    "pytest-dev__pytest-5692": 0.58,
    "django__django-14787": 0.58,
    "django__django-15814": 0.58,
    "sphinx-doc__sphinx-8721": 0.58,
    "django__django-14016": 0.58,
    "django__django-11999": 0.59,
    "django__django-12497": 0.59,
    "psf__requests-2674": 0.59,
    "matplotlib__matplotlib-23913": 0.59,
    "pytest-dev__pytest-7432": 0.59,
    "django__django-11049": 0.59,
    "sympy__sympy-22714": 0.62,
    "scikit-learn__scikit-learn-12471": 0.62,
    "psf__requests-863": 0.62,
    "django__django-14672": 0.62,
    "sympy__sympy-20154": 0.62,
    "django__django-13590": 0.64,
    "django__django-12700": 0.64,
    "sphinx-doc__sphinx-8595": 0.64,
    "django__django-15789": 0.65,
    "django__django-12453": 0.68,
    "django__django-13447": 0.68,
    "psf__requests-2317": 0.7,
    "django__django-11583": 0.7,
    "django__django-16046": 0.7,
    "django__django-14238": 0.71,
    "django__django-15851": 0.71,
    "django__django-13710": 0.73,
    "sympy__sympy-21847": 0.73,
    "sympy__sympy-23117": 0.73,
    "django__django-12983": 0.73,
    "scikit-learn__scikit-learn-13779": 0.74,
    "sympy__sympy-13647": 0.74,
    "django__django-16041": 0.74,
    "scikit-learn__scikit-learn-10297": 0.74,
    "django__django-15347": 0.74,
    "scikit-learn__scikit-learn-13496": 0.74,
    "sympy__sympy-20212": 0.76,
    "scikit-learn__scikit-learn-13439": 0.76,
    "django__django-13933": 0.76,
    "django__django-12286": 0.76,
    "django__django-13230": 0.77,
    "astropy__astropy-14995": 0.77,
    "django__django-11179": 0.77,
    "sphinx-doc__sphinx-8713": 0.77,
    "sympy__sympy-24213": 0.77,
    "matplotlib__matplotlib-23964": 0.79,
    "scikit-learn__scikit-learn-14894": 0.79,
    "django__django-10914": 0.8,
    "pydata__xarray-5131": 0.8,
    "django__django-11039": 0.82,
    "pytest-dev__pytest-7373": 0.82,
    "django__django-14915": 0.82,
    "django__django-16595": 0.83,
    "pytest-dev__pytest-11143": 0.85,
    "sympy__sympy-14774": 0.85,
    "pytest-dev__pytest-5227": 0.85,
    "django__django-16873": 0.85,
    "django__django-16139": 0.85,
    "mwaskom__seaborn-3010": 0.86,
    "django__django-14382": 0.86,
    "django__django-14752": 0.86,
    "sympy__sympy-13480": 0.86,
    "django__django-16379": 0.86,
    "sympy__sympy-24152": 0.88,
    "django__django-14855": 0.88,
    "django__django-11133": 0.88,
    "django__django-11099": 0.91,
    "django__django-13658": 0.91,
    "django__django-16255": 0.91,
    "django__django-16527": 0.91,
}
