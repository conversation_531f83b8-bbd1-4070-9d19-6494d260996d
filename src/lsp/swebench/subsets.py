from src.swebench.enums import SWEBenchLiteSubset

SMALL_LITE_SUBSET = [
    "mwaskom__seaborn-2848",
    "sphinx-doc__sphinx-8627",
    "sphinx-doc__sphinx-7975",
    "django__django-17087",
    "sympy__sympy-17655",
    "matplotlib__matplotlib-26020",
    "sympy__sympy-20154",
    "scikit-learn__scikit-learn-13439",
    "pytest-dev__pytest-7373",
    "django__django-16527",
]

MEDIUM_LITE_SUBSET = [
    "sympy__sympy-15346",
    "sympy__sympy-16281",
    "sympy__sympy-22840",
    "pytest-dev__pytest-7220",
    "django__django-12284",
    "pytest-dev__pytest-7490",
    "matplotlib__matplotlib-25442",
    "django__django-13757",
    "django__django-15790",
    "sympy__sympy-18532",
    "sympy__sympy-13471",
    "scikit-learn__scikit-learn-15535",
    "django__django-13447",
    "django__django-15789",
    "scikit-learn__scikit-learn-14894",
    "django__django-14238",
    "django__django-10914",
    "pytest-dev__pytest-11143",
    "django__django-16255",
    "django__django-13658",
]

LARGE_LITE_SUBSET = [
    "pytest-dev__pytest-5495",
    "django__django-11797",
    "django__django-14730",
    "scikit-learn__scikit-learn-25500",
    "sphinx-doc__sphinx-8506",
    "django__django-16408",
    "django__django-16910",
    "sympy__sympy-12236",
    "matplotlib__matplotlib-24265",
    "django__django-15320",
    "matplotlib__matplotlib-25311",
    "django__django-12125",
    "django__django-12747",
    "matplotlib__matplotlib-24334",
    "scikit-learn__scikit-learn-14983",
    "scikit-learn__scikit-learn-13497",
    "django__django-14580",
    "pylint-dev__pylint-6506",
    "matplotlib__matplotlib-23987",
    "scikit-learn__scikit-learn-13497",
    "django__django-14017",
    "django__django-15213",
    "django__django-12284",
    "pylint-dev__pylint-7114",
    "django__django-11422",
    "django__django-11620",
    "django__django-12284",
    "sympy__sympy-13971",
    "django__django-12284",
    "sphinx-doc__sphinx-7975",
    "scikit-learn__scikit-learn-15512",
    "scikit-learn__scikit-learn-15512",
    "pylint-dev__pylint-7993",
    "django__django-12184",
    "django__django-13315",
    "sympy__sympy-15609",
    "pylint-dev__pylint-7993",
    "sympy__sympy-17022",
    "pylint-dev__pylint-7993",
    "sympy__sympy-15678",
    "sympy__sympy-18057",
    "sympy__sympy-17655",
    "sympy__sympy-17655",
    "django__django-13028",
    "sympy__sympy-17139",
    "django__django-14999",
    "django__django-15790",
    "scikit-learn__scikit-learn-11281",
    "astropy__astropy-12907",
    "django__django-11815",
    "sympy__sympy-18621",
    "django__django-11999",
    "sphinx-doc__sphinx-8721",
    "matplotlib__matplotlib-23314",
    "sphinx-doc__sphinx-8721",
    "sympy__sympy-18621",
    "django__django-12497",
    "scikit-learn__scikit-learn-13584",
    "matplotlib__matplotlib-24970",
    "scikit-learn__scikit-learn-13584",
    "django__django-12453",
    "sympy__sympy-20154",
    "django__django-13447",
    "sphinx-doc__sphinx-8595",
    "sympy__sympy-20154",
    "sympy__sympy-20154",
    "django__django-12700",
    "psf__requests-2317",
    "django__django-16046",
    "sympy__sympy-20154",
    "sympy__sympy-20212",
    "django__django-13710",
    "sympy__sympy-13647",
    "django__django-15851",
    "scikit-learn__scikit-learn-14894",
    "sympy__sympy-24213",
    "scikit-learn__scikit-learn-13779",
    "django__django-13710",
    "django__django-13933",
    "sympy__sympy-20212",
    "django__django-14855",
    "django__django-11039",
    "django__django-16379",
    "pydata__xarray-5131",
    "pytest-dev__pytest-7373",
    "django__django-16139",
    "django__django-14382",
    "pytest-dev__pytest-5227",
    "django__django-16595",
    "django__django-16379",
    "django__django-16527",
    "django__django-13658",
    "django__django-16255",
    "django__django-16527",
    "django__django-13658",
    "django__django-13658",
    "django__django-13658",
    "django__django-11099",
    "django__django-16527",
    "django__django-11099",
]

LITE_SUBSETS = {
    SWEBenchLiteSubset.LITE_SMALL: SMALL_LITE_SUBSET,
    SWEBenchLiteSubset.LITE_MEDIUM: MEDIUM_LITE_SUBSET,
    SWEBenchLiteSubset.LITE_LARGE: LARGE_LITE_SUBSET,
}
