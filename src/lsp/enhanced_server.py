"""
Enhanced LSP Server with Context Intelligence

Extends the base CodegenLanguageServer with semantic context awareness
for autonomous agents.
"""

from typing import Any, List, Optional
from lsprotocol import types
from lsprotocol.types import Position, Range

from .server import CodegenLanguageServer
from .context_provider import <PERSON><PERSON><PERSON><PERSON>xt<PERSON><PERSON>ider, LSPContextCommands
from codegen.sdk.core.codebase import Codebase
from codegen.shared.logging.get_logger import get_logger

logger = get_logger(__name__)


class EnhancedCodegenLanguageServer(CodegenLanguageServer):
    """
    Enhanced LSP server with context intelligence for autonomous agents.
    
    This server provides all standard LSP functionality plus semantic
    context awareness through the Context Intelligence Layer.
    """
    
    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)
        self.context_provider: Optional[LSPContextProvider] = None
        self.context_commands: Optional[LSPContextCommands] = None
        
        logger.info("Enhanced Codegen Language Server initialized")
    
    def initialize_context_intelligence(self, codebase: Codebase) -> None:
        """Initialize the context intelligence layer"""
        try:
            self.context_provider = LSPContextProvider(codebase)
            self.context_commands = LSPContextCommands(self.context_provider)
            
            # Register context intelligence commands
            self._register_context_commands()
            
            logger.info("Context intelligence layer initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize context intelligence: {e}")
            # Continue without context intelligence
            self.context_provider = None
            self.context_commands = None
    
    def _register_context_commands(self) -> None:
        """Register LSP commands for context intelligence"""
        if not self.context_commands:
            return
        
        # Register commands that agents can call
        @self.command("codegen.analyzeQuery")
        def analyze_query(arguments: List[Any]) -> dict:
            """Analyze an agent query for semantic context"""
            return self.context_commands.analyze_query(arguments)
        
        @self.command("codegen.enhancedSearch")
        def enhanced_search(arguments: List[Any]) -> str:
            """Perform enhanced search with semantic context"""
            return self.context_commands.enhanced_search(arguments)
        
        @self.command("codegen.symbolAnalysis")
        def symbol_analysis(arguments: List[Any]) -> str:
            """Get detailed symbol analysis with context"""
            return self.context_commands.symbol_analysis(arguments)
        
        @self.command("codegen.getContext")
        def get_context(arguments: List[Any]) -> dict:
            """Get context for a specific position"""
            return self.context_commands.get_context(arguments)
        
        logger.info("Context intelligence commands registered")
    
    def enhanced_hover(self, params: types.HoverParams) -> Optional[types.Hover]:
        """Provide enhanced hover with semantic context"""
        if not self.context_provider:
            return None
        
        try:
            return self.context_provider.get_enhanced_hover(
                params.text_document.uri,
                params.position
            )
        except Exception as e:
            logger.error(f"Error in enhanced hover: {e}")
            return None
    
    def enhanced_completion(self, params: types.CompletionParams) -> List[types.CompletionItem]:
        """Provide enhanced completions with semantic context"""
        if not self.context_provider:
            return []
        
        try:
            return self.context_provider.get_context_completion(
                params.text_document.uri,
                params.position,
                params.context
            )
        except Exception as e:
            logger.error(f"Error in enhanced completion: {e}")
            return []
    
    def get_context_for_agent(self, document_uri: str, position: Position) -> dict:
        """Get semantic context for autonomous agents"""
        if not self.context_provider:
            return {"error": "Context intelligence not available"}
        
        return self.context_provider.get_context_for_position(document_uri, position)
    
    def analyze_agent_query(self, query: str, operation_type: str = "general") -> dict:
        """Analyze an agent query and provide semantic context"""
        if not self.context_provider:
            return {"error": "Context intelligence not available"}
        
        return self.context_provider.analyze_agent_query(query, operation_type)


def create_enhanced_lsp_server() -> EnhancedCodegenLanguageServer:
    """Factory function to create an enhanced LSP server"""
    server = EnhancedCodegenLanguageServer("codegen-enhanced", "v1.0")
    
    # Set up standard LSP handlers with context enhancement
    @server.feature(types.TEXT_DOCUMENT_HOVER)
    def hover(params: types.HoverParams) -> Optional[types.Hover]:
        """Enhanced hover with semantic context"""
        enhanced_hover = server.enhanced_hover(params)
        if enhanced_hover:
            return enhanced_hover
        
        # Fallback to standard hover if context intelligence fails
        return None
    
    @server.feature(types.TEXT_DOCUMENT_COMPLETION)
    def completion(params: types.CompletionParams) -> List[types.CompletionItem]:
        """Enhanced completion with semantic context"""
        enhanced_completions = server.enhanced_completion(params)
        if enhanced_completions:
            return enhanced_completions
        
        # Fallback to standard completions
        return []
    
    @server.feature(types.INITIALIZE)
    def initialize(params: types.InitializeParams) -> types.InitializeResult:
        """Initialize the enhanced LSP server"""
        logger.info("Initializing enhanced LSP server")
        
        # Initialize context intelligence if codebase is available
        if hasattr(server, 'codebase') and server.codebase:
            server.initialize_context_intelligence(server.codebase)
        
        return types.InitializeResult(
            capabilities=types.ServerCapabilities(
                text_document_sync=types.TextDocumentSyncKind.Full,
                hover_provider=True,
                completion_provider=types.CompletionOptions(
                    trigger_characters=["."],
                    resolve_provider=True
                ),
                execute_command_provider=types.ExecuteCommandOptions(
                    commands=[
                        "codegen.analyzeQuery",
                        "codegen.enhancedSearch", 
                        "codegen.symbolAnalysis",
                        "codegen.getContext"
                    ]
                )
            ),
            server_info=types.ServerInfo(
                name="Codegen Enhanced LSP Server",
                version="1.0.0"
            )
        )
    
    @server.feature(types.INITIALIZED)
    def initialized(params: types.InitializedParams) -> None:
        """Server initialized callback"""
        logger.info("Enhanced LSP server initialized and ready")
    
    return server


# Example usage for agents
class AgentLSPClient:
    """
    Example client interface for autonomous agents to use the enhanced LSP server.
    
    This shows how agents can leverage the context intelligence through LSP.
    """
    
    def __init__(self, server: EnhancedCodegenLanguageServer):
        self.server = server
    
    def analyze_task(self, query: str, operation_type: str = "general") -> dict:
        """Analyze a task and get semantic context"""
        return self.server.analyze_agent_query(query, operation_type)
    
    def enhanced_search(self, query: str, max_results: int = 15) -> str:
        """Perform enhanced search with context"""
        if self.server.context_provider:
            return self.server.context_provider.get_enhanced_search_results(query, max_results)
        return f"Basic search for: {query}"
    
    def get_symbol_context(self, symbol_name: str) -> str:
        """Get detailed context for a symbol"""
        if self.server.context_provider:
            return self.server.context_provider.get_symbol_analysis(symbol_name)
        return f"Basic symbol info for: {symbol_name}"
    
    def get_position_context(self, file_uri: str, line: int, character: int) -> dict:
        """Get context for a specific position in a file"""
        position = Position(line=line, character=character)
        return self.server.get_context_for_agent(file_uri, position)


if __name__ == "__main__":
    # Example of how to start the enhanced LSP server
    import asyncio
    from pygls.server import aio_readline
    
    async def main():
        server = create_enhanced_lsp_server()
        
        # In a real implementation, you would set up the codebase here
        # server.codebase = Codebase(repo_path=".")
        # server.initialize_context_intelligence(server.codebase)
        
        await server.start_io()
    
    asyncio.run(main())

