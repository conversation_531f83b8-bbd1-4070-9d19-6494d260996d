"""
LSP Context Provider

Integrates the Context Intelligence Layer with the LSP server to provide
semantic context awareness for autonomous agents through LSP protocol.
"""

from typing import Any, Dict, List, Optional, Union
from lsprotocol import types
from lsprotocol.types import Position, Range, TextDocumentIdentifier

from codegen.sdk.core.codebase import Codebase
from codegen.shared.logging.get_logger import get_logger

# Import context intelligence components
import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from context_intelligence import (
    ContextIntelligenceLayer,
    EnhancedAgentTools,
    TaskClassifier,
    SymbolAnalyzer
)

logger = get_logger(__name__)


class LSPContextProvider:
    """
    Provides semantic context through LSP protocol for autonomous agents.
    
    This class bridges the Context Intelligence Layer with LSP capabilities,
    allowing agents to get enhanced context through standard LSP requests.
    """
    
    def __init__(self, codebase: Codebase):
        self.codebase = codebase
        self.context_layer = ContextIntelligenceLayer(codebase)
        self.enhanced_tools = EnhancedAgentTools(codebase)
        self.task_classifier = TaskClassifier()
        self.symbol_analyzer = SymbolAnalyzer(codebase)
        
        logger.info("LSP Context Provider initialized with semantic intelligence")
    
    def get_context_for_position(self, document_uri: str, position: Position) -> Dict[str, Any]:
        """
        Get semantic context for a specific position in a document.
        
        This provides context-aware information about the code at the cursor position,
        including related symbols, domain classification, and suggested actions.
        """
        try:
            # Get file from codebase
            file_path = self._uri_to_path(document_uri)
            file = self.codebase.get_file(file_path)
            
            if not file:
                return {"error": f"File not found: {file_path}"}
            
            # Find symbol at position
            symbol = self._find_symbol_at_position(file, position)
            
            if symbol:
                # Get detailed symbol context
                symbol_context = self.symbol_analyzer.analyze_symbol_context(symbol.name)
                
                return {
                    "type": "symbol_context",
                    "symbol": {
                        "name": symbol.name,
                        "type": symbol.symbol_type,
                        "domain": symbol_context.get("domain", "general"),
                        "file_path": file_path,
                        "line": position.line,
                        "character": position.character
                    },
                    "context": symbol_context,
                    "suggestions": self._get_context_suggestions(symbol_context)
                }
            else:
                # Provide general file context
                return {
                    "type": "file_context",
                    "file": {
                        "path": file_path,
                        "line": position.line,
                        "character": position.character
                    },
                    "context": self._get_file_context(file),
                    "suggestions": ["Explore symbols in this file", "Search for related code"]
                }
                
        except Exception as e:
            logger.error(f"Error getting context for position: {e}")
            return {"error": str(e)}
    
    def get_enhanced_hover(self, document_uri: str, position: Position) -> Optional[types.Hover]:
        """
        Provide enhanced hover information with semantic context.
        
        This replaces standard LSP hover with context-aware information
        that helps agents understand the semantic meaning of code elements.
        """
        try:
            context = self.get_context_for_position(document_uri, position)
            
            if "error" in context:
                return None
            
            if context["type"] == "symbol_context":
                symbol_info = context["symbol"]
                symbol_context = context["context"]
                
                # Create rich hover content
                hover_content = [
                    f"**{symbol_info['name']}** ({symbol_info['type']})",
                    f"Domain: {symbol_info['domain']}",
                    ""
                ]
                
                # Add dependencies
                if symbol_context.get("dependencies"):
                    hover_content.append("**Dependencies:**")
                    for dep in symbol_context["dependencies"][:5]:
                        hover_content.append(f"• {dep}")
                    hover_content.append("")
                
                # Add usage information
                if symbol_context.get("usages"):
                    usage_count = len(symbol_context["usages"])
                    hover_content.append(f"**Used in {usage_count} files**")
                    hover_content.append("")
                
                # Add architectural context
                if symbol_context.get("architectural_role"):
                    hover_content.append(f"**Role:** {symbol_context['architectural_role']}")
                    hover_content.append("")
                
                # Add suggestions
                if context.get("suggestions"):
                    hover_content.append("**Suggestions:**")
                    for suggestion in context["suggestions"][:3]:
                        hover_content.append(f"• {suggestion}")
                
                return types.Hover(
                    contents=types.MarkupContent(
                        kind=types.MarkupKind.Markdown,
                        value="\\n".join(hover_content)
                    )
                )
            
            return None
            
        except Exception as e:
            logger.error(f"Error creating enhanced hover: {e}")
            return None
    
    def get_context_completion(self, document_uri: str, position: Position, 
                             context: types.CompletionContext) -> List[types.CompletionItem]:
        """
        Provide context-aware completions for autonomous agents.
        
        This enhances standard completions with semantic understanding
        of what the agent is trying to accomplish.
        """
        try:
            file_path = self._uri_to_path(document_uri)
            file = self.codebase.get_file(file_path)
            
            if not file:
                return []
            
            # Get current context
            file_context = self._get_file_context(file)
            
            # Generate context-aware completions
            completions = []
            
            # Add domain-specific completions
            domain = file_context.get("domain", "general")
            if domain == "authentication":
                completions.extend([
                    types.CompletionItem(
                        label="authenticate_user",
                        kind=types.CompletionItemKind.Function,
                        detail="Authentication function",
                        documentation="Authenticate user with credentials"
                    ),
                    types.CompletionItem(
                        label="verify_token",
                        kind=types.CompletionItemKind.Function,
                        detail="Token verification",
                        documentation="Verify authentication token"
                    )
                ])
            elif domain == "api":
                completions.extend([
                    types.CompletionItem(
                        label="rate_limit",
                        kind=types.CompletionItemKind.Function,
                        detail="Rate limiting",
                        documentation="Apply rate limiting to endpoint"
                    ),
                    types.CompletionItem(
                        label="validate_request",
                        kind=types.CompletionItemKind.Function,
                        detail="Request validation",
                        documentation="Validate incoming request"
                    )
                ])
            
            # Add related symbols as completions
            related_symbols = file_context.get("related_symbols", [])
            for symbol_name in related_symbols[:10]:
                completions.append(
                    types.CompletionItem(
                        label=symbol_name,
                        kind=types.CompletionItemKind.Reference,
                        detail="Related symbol",
                        documentation=f"Symbol related to current context"
                    )
                )
            
            return completions
            
        except Exception as e:
            logger.error(f"Error getting context completions: {e}")
            return []
    
    def analyze_agent_query(self, query: str, operation_type: str = "general") -> Dict[str, Any]:
        """
        Analyze an agent query and provide semantic context.
        
        This is the main entry point for agents to get enhanced context
        for their operations.
        """
        try:
            # Extract context using the intelligence layer
            context = self.context_layer.extract_context(query, operation_type)
            
            # Format for LSP consumption
            return {
                "query": query,
                "operation_type": operation_type,
                "task": {
                    "intent": context.task.intent,
                    "domain": context.task.domain,
                    "entities": context.task.entities,
                    "confidence": context.confidence
                },
                "relevant_symbols": [
                    {
                        "name": symbol.symbol_name,
                        "domain": symbol.domain,
                        "file_path": getattr(symbol, "file_path", "unknown"),
                        "complexity": getattr(symbol, "complexity", 0)
                    }
                    for symbol in context.relevant_symbols[:10]
                ],
                "impact_scope": context.impact_scope,
                "suggested_actions": context.suggested_actions,
                "related_files": context.related_files[:10]
            }
            
        except Exception as e:
            logger.error(f"Error analyzing agent query: {e}")
            return {"error": str(e)}
    
    def get_enhanced_search_results(self, query: str, max_results: int = 15) -> str:
        """
        Get enhanced search results with semantic context.
        
        This provides the enhanced search functionality through LSP.
        """
        try:
            return self.enhanced_tools.enhanced_search(query, max_results)
        except Exception as e:
            logger.error(f"Error getting enhanced search results: {e}")
            return f"Error: {str(e)}"
    
    def get_symbol_analysis(self, symbol_name: str) -> str:
        """
        Get detailed symbol analysis with context.
        
        This provides enhanced symbol information through LSP.
        """
        try:
            return self.enhanced_tools.enhanced_find_symbol(symbol_name)
        except Exception as e:
            logger.error(f"Error getting symbol analysis: {e}")
            return f"Error: {str(e)}"
    
    def _uri_to_path(self, uri: str) -> str:
        """Convert LSP URI to file path"""
        if uri.startswith("file://"):
            return uri[7:]  # Remove file:// prefix
        return uri
    
    def _find_symbol_at_position(self, file, position: Position):
        """Find symbol at the given position in the file"""
        try:
            # Get symbols in the file
            symbols = [s for s in self.codebase.symbols if s.file == file]
            
            # Find symbol that contains the position
            for symbol in symbols:
                if hasattr(symbol, 'range') and symbol.range:
                    if (symbol.range.start.line <= position.line <= symbol.range.end.line):
                        return symbol
                elif hasattr(symbol, 'line_range') and symbol.line_range:
                    if (symbol.line_range[0] <= position.line <= symbol.line_range[1]):
                        return symbol
            
            return None
            
        except Exception as e:
            logger.error(f"Error finding symbol at position: {e}")
            return None
    
    def _get_file_context(self, file) -> Dict[str, Any]:
        """Get general context for a file"""
        try:
            # Get symbols in the file
            file_symbols = [s for s in self.codebase.symbols if s.file == file]
            
            # Classify file domain based on symbols
            domains = []
            for symbol in file_symbols:
                domain = self.symbol_analyzer._get_symbol_domain(symbol)
                if domain != "general":
                    domains.append(domain)
            
            # Get most common domain
            if domains:
                domain = max(set(domains), key=domains.count)
            else:
                domain = "general"
            
            # Get related symbols
            related_symbols = []
            for symbol in file_symbols[:5]:
                related = self.symbol_analyzer._find_related_symbols(symbol)
                related_symbols.extend(related[:3])
            
            return {
                "domain": domain,
                "symbol_count": len(file_symbols),
                "related_symbols": list(set(related_symbols))[:10],
                "file_path": file.filepath
            }
            
        except Exception as e:
            logger.error(f"Error getting file context: {e}")
            return {"domain": "general", "symbol_count": 0, "related_symbols": []}
    
    def _get_context_suggestions(self, symbol_context: Dict[str, Any]) -> List[str]:
        """Generate context-aware suggestions"""
        suggestions = []
        
        # Domain-specific suggestions
        domain = symbol_context.get("domain", "general")
        if domain == "authentication":
            suggestions.extend([
                "Check security implications",
                "Verify error handling",
                "Review access controls"
            ])
        elif domain == "api":
            suggestions.extend([
                "Consider rate limiting",
                "Validate input parameters",
                "Check response format"
            ])
        elif domain == "data_access":
            suggestions.extend([
                "Review query performance",
                "Check data validation",
                "Consider caching"
            ])
        
        # Usage-based suggestions
        usage_count = len(symbol_context.get("usages", []))
        if usage_count > 10:
            suggestions.append("High usage - test changes carefully")
        elif usage_count == 0:
            suggestions.append("Unused symbol - consider removal")
        
        # Complexity-based suggestions
        complexity = symbol_context.get("complexity", 0)
        if complexity > 50:
            suggestions.append("High complexity - consider refactoring")
        
        return suggestions[:5]


class LSPContextCommands:
    """
    LSP commands for context intelligence operations.
    
    These commands can be called by agents through LSP execute command requests.
    """
    
    def __init__(self, context_provider: LSPContextProvider):
        self.context_provider = context_provider
    
    def analyze_query(self, arguments: List[Any]) -> Dict[str, Any]:
        """Command: codegen.analyzeQuery"""
        if not arguments or len(arguments) < 1:
            return {"error": "Query argument required"}
        
        query = arguments[0]
        operation_type = arguments[1] if len(arguments) > 1 else "general"
        
        return self.context_provider.analyze_agent_query(query, operation_type)
    
    def enhanced_search(self, arguments: List[Any]) -> str:
        """Command: codegen.enhancedSearch"""
        if not arguments or len(arguments) < 1:
            return "Error: Query argument required"
        
        query = arguments[0]
        max_results = arguments[1] if len(arguments) > 1 else 15
        
        return self.context_provider.get_enhanced_search_results(query, max_results)
    
    def symbol_analysis(self, arguments: List[Any]) -> str:
        """Command: codegen.symbolAnalysis"""
        if not arguments or len(arguments) < 1:
            return "Error: Symbol name argument required"
        
        symbol_name = arguments[0]
        return self.context_provider.get_symbol_analysis(symbol_name)
    
    def get_context(self, arguments: List[Any]) -> Dict[str, Any]:
        """Command: codegen.getContext"""
        if not arguments or len(arguments) < 3:
            return {"error": "Document URI, line, and character arguments required"}
        
        document_uri = arguments[0]
        line = int(arguments[1])
        character = int(arguments[2])
        
        position = Position(line=line, character=character)
        return self.context_provider.get_context_for_position(document_uri, position)

