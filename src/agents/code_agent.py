import os
from typing import TYPE_CHECKING, Optional
from uuid import uuid4

from langchain.tools import BaseTool
from langchain_core.messages import AIMessage, HumanMessage
from langchain_core.runnables.config import RunnableConfig
from langgraph.graph.graph import CompiledGraph
from langsmith import Client

from src.agents.loggers import ExternalLogger
from src.agents.tracer import MessageStreamTracer
from src.langchain.agent import create_codebase_agent
from src.langchain.condensed_logger import run_agent_with_condensed_logging, create_condensed_stream
from src.langchain.rate_limiter import RateLimitHandler
from src.langchain.utils.get_langsmith_url import (
    find_and_print_langsmith_run_url,
)

if TYPE_CHECKING:
    from codegen import Codebase

from src.agents.utils import AgentConfig


class CodeAgent:
    """Agent for interacting with a codebase."""

    codebase: "Codebase"
    agent: CompiledGraph
    langsmith_client: Client
    project_name: str
    thread_id: str | None = None
    run_id: str | None = None
    instance_id: str | None = None
    difficulty: int | None = None
    logger: Optional[ExternalLogger] = None

    def __init__(
        self,
        codebase: "Codebase",
        model_provider: str = "vertex",
        model_name: str = "gemini-2.0-flash-001",
        memory: bool = True,
        debug: bool = False,
        condensed_logging: bool = False,
        short_format: bool = False,
        tools: Optional[list[BaseTool]] = None,
        tags: Optional[list[str]] = [],
        metadata: Optional[dict] = {},
        agent_config: Optional[AgentConfig] = None,
        thread_id: Optional[str] = None,
        logger: Optional[ExternalLogger] = None,
        **kwargs,
    ):
        """Initialize a CodeAgent.

        Args:
            codebase: The codebase to operate on
            model_provider: The model provider to use ("anthropic" or "openai" or "google")
            model_name: Name of the model to use
            memory: Whether to let LLM keep track of the conversation history
            debug: Whether to enable debug mode with verbose logging (default: False)
            condensed_logging: Whether to use condensed logging instead of verbose output (default: True)
            short_format: Whether to use short format for tool calls (default: True). When True, shows "tool call --> view_file x.py". When False, shows detailed format with timestamps.
            tools: Additional tools to use
            tags: Tags to add to the agent trace. Must be of the same type.
            metadata: Metadata to use for the agent. Must be a dictionary.
            **kwargs: Additional LLM configuration options. Supported options:
                - temperature: Temperature parameter (0-1)
                - top_p: Top-p sampling parameter (0-1)
                - top_k: Top-k sampling parameter (>= 1)
                - max_tokens: Maximum number of tokens to generate
        """
        self.codebase = codebase
        self.model_provider = model_provider
        self.model_name = model_name
        self.memory = memory
        self.debug = debug
        self.condensed_logging = condensed_logging
        self.short_format = short_format
        self.tools = tools
        self.agent_config = agent_config
        self.kwargs = kwargs
        self.rate_limiter = RateLimitHandler()
        
        self.agent = create_codebase_agent(
            self.codebase,
            model_provider=model_provider,
            model_name=model_name,
            memory=memory,
            debug=debug,
            additional_tools=tools,
            config=agent_config,
            **kwargs,
        )
        self.langsmith_client = Client()

        if thread_id is None:
            self.thread_id = str(uuid4())
        else:
            self.thread_id = thread_id

        # Get project name from environment variable or use a default
        self.project_name = os.environ.get("LANGCHAIN_PROJECT", "RELACE")
        print(f"Using LangSmith project: {self.project_name}")

        # Store SWEBench metadata if provided
        self.run_id = metadata.get("run_id")
        self.instance_id = metadata.get("instance_id")
        # Extract difficulty value from "difficulty_X" format
        difficulty_str = metadata.get("difficulty", "")
        self.difficulty = int(difficulty_str.split("_")[1]) if difficulty_str and "_" in difficulty_str else None

        # Initialize tags for agent trace
        self.tags = [*tags, self.model_name]

        # set logger if provided
        self.logger = logger

        # Initialize metadata for agent trace
        self.metadata = {
            "project": self.project_name,
            "model": self.model_name,
            **metadata,
        }

    def _recreate_agent(self, model_provider: str, model_name: str):
        """Recreate the agent with new model configuration."""
        self.model_provider = model_provider
        self.model_name = model_name
        
        self.agent = create_codebase_agent(
            self.codebase,
            model_provider=model_provider,
            model_name=model_name,
            memory=self.memory,
            debug=self.debug,
            additional_tools=self.tools,
            config=self.agent_config,
            **self.kwargs,
        )
        
        # Update metadata with new model
        self.metadata["model"] = model_name

    def run(self, prompt: str, image_urls: Optional[list[str]] = None) -> str:
        """Run the agent with a prompt and optional images, using streaming-first with fallback.

        Args:
            prompt: The prompt to run
            image_urls: Optional list of base64-encoded image strings. Example: ["data:image/png;base64,<base64_str>"]

        Returns:
            The agent's response
        """
        def run_func(*args, **kwargs):
            return self._run_internal(prompt, image_urls)
        
        # Use rate limiter to handle rate limits and model fallbacks
        return self.rate_limiter.handle_rate_limit_with_retry(
            run_func,
            agent_instance=self,
            model_provider=self.model_provider,
            model_name=self.model_name
        )

    def _run_internal(self, prompt: str, image_urls: Optional[list[str]] = None) -> str:
        """Internal run method without rate limiting."""
        self.config = {
            "configurable": {
                "thread_id": self.thread_id,
                "metadata": {"project": self.project_name},
            },
            "recursion_limit": 200,
        }

        # Prepare content with prompt and images if provided
        content = [{"type": "text", "text": prompt}]
        if image_urls:
            content += [{"type": "image_url", "image_url": {"url": image_url}} for image_url in image_urls]

        config = RunnableConfig(configurable={"thread_id": self.thread_id}, tags=self.tags, metadata=self.metadata, recursion_limit=200)
        input_data = {"messages": [HumanMessage(content=content)]}

        # Try streaming first (default mode)
        try:
            if self.condensed_logging and not self.debug:
                return self._run_with_condensed_logging(input_data, config, prompt)

            else:
                return self._run_with_verbose_logging_and_tracer(input_data, config, content)
                
        except Exception as e:
            return self._handle_fallback_and_errors(input_data, config, e)

    def get_agent_trace_url(self) -> str | None:
        """Get the URL for the most recent agent run in LangSmith.

        Returns:
            The URL for the run in LangSmith if found, None otherwise
        """
        try:
            # TODO - this is definitely not correct, we should be able to get the URL directly...
            return find_and_print_langsmith_run_url(client=self.langsmith_client, project_name=self.project_name)
        except Exception as e:
            separator = "=" * 60
            print(f"\n{separator}\nCould not retrieve LangSmith URL: {e}")
            import traceback

            print(traceback.format_exc())
            print(separator)
            return None

    def get_tools(self) -> list[BaseTool]:
        return list(self.agent.get_graph().nodes["tools"].data.tools_by_name.values())

    def get_state(self) -> dict:
        return self.agent.get_state(self.config)

    def get_tags_metadata(self) -> tuple[list[str], dict]:
        tags = [self.model_name]
        metadata = {"project": self.project_name, "model": self.model_name}
        # Add SWEBench run ID and instance ID to the metadata and tags for filtering
        if self.run_id is not None:
            metadata["swebench_run_id"] = self.run_id
            tags.append(self.run_id)

        if self.instance_id is not None:
            metadata["swebench_instance_id"] = self.instance_id
            tags.append(self.instance_id)

        if self.difficulty is not None:
            metadata["swebench_difficulty"] = self.difficulty
            tags.append(f"difficulty_{self.difficulty}")

        return tags, metadata

    def _run_with_condensed_logging(self, input_data: dict, config: dict, prompt: str) -> str:
        """Execute agent with condensed logging."""
        from src.langchain.condensed_logger import CondensedAgentLogger, create_condensed_stream
        
        logger = CondensedAgentLogger(short_format=self.short_format)
        task_description = prompt if isinstance(prompt, str) else "Code task"
        logger.log_start(task_description)
        
        # Stream with condensed logging
        stream = self.agent.stream(input_data, config=config, stream_mode="values")
        condensed_stream = create_condensed_stream(stream, logger)
        
        # Consume the stream
        last_result = None
        for result in condensed_stream:
            last_result = result
        
        return last_result.get("final_answer", "") if last_result else ""

    def _run_with_verbose_logging_and_tracer(self, input_data: dict, config: dict, content: list) -> str:
        """Execute agent with verbose logging and message tracing."""
        tracer = MessageStreamTracer(logger=self.logger)
        
        if self.debug:
            print(f"\n🚀 Starting agent execution...")
            print(f"📝 Task: {content[0]['text'][:100]}...")
        
        # Stream with verbose logging and tracing
        stream = self.agent.stream(input_data, config=config, stream_mode="values")
        traced_stream = tracer.process_stream(stream)
        
        # Consume the stream with verbose output
        last_result = None
        for result in traced_stream:
            last_result = result
            if self.debug and result:
                print(f"📊 Stream result: {str(result)[:100]}...")
        
        response = last_result.get("final_answer", "") if last_result else ""
        
        if self.debug:
            print(f"✅ Agent execution completed: {response[:100]}...")
        
        return response

    def _handle_fallback_and_errors(self, input_data: dict, config: dict, original_error: Exception) -> str:
        """Handle fallback to regular invoke mode when streaming fails."""
        # Fallback to regular invoke if streaming fails
        if self.debug or not self.condensed_logging:
            print(f"\n⚠️  Streaming failed ({str(original_error)[:50]}...), falling back to regular mode")
        
        try:
            result = self.agent.invoke(input_data, config=config)
            
            # Extract the response from the result
            response = self._extract_response_from_result(result)
            
            if self.debug:
                print(f"\n✅ Fallback mode successful: {response[:100]}...")
            
            return response
            
        except Exception as fallback_error:
            error_msg = f"Both streaming and regular mode failed. Streaming error: {str(original_error)[:100]}. Fallback error: {str(fallback_error)[:100]}"
            if self.debug:
                print(f"\n❌ {error_msg}")
            raise Exception(error_msg)

    def _extract_response_from_result(self, result) -> str:
        """Extract the response content from agent result."""
        if isinstance(result, dict):
            if "final_answer" in result:
                return result["final_answer"]
            elif "messages" in result and result["messages"]:
                last_message = result["messages"][-1]
                if isinstance(last_message, AIMessage):
                    return last_message.content
                else:
                    return str(last_message)
            else:
                return str(result)
        else:
            return str(result)
