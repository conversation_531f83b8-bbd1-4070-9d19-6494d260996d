{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from codegen.agents.code_agent import CodeAgent"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from codegen.sdk.core.codebase import Codebase\n", "\n", "\n", "codebase = Codebase.from_repo(\"codegen-sh/Kevin-s-Adventure-Game\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from typing import Any, Dict, Union\n", "from codegen.agents.data import BaseMessage\n", "from codegen.agents.loggers import ExternalLogger\n", "\n", "\n", "class ConsoleLogger(ExternalLogger):\n", "    def log(self, data: Union[Dict[str, Any], BaseMessage]) -> None:\n", "        print(data.content)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["image = \"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["agent = CodeAgent(codebase)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["agent.run(\"What is the main character's name?\")"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 2}