from typing import TYPE_CHECKING, Optional
from uuid import uuid4

from langchain.tools import BaseTool
from langchain_core.messages import AIMessage, HumanMessage, SystemMessage

from src.langchain.agent import create_codebase_agent
from src.langchain.prompts import INTERACTIVE_SYSTEM_MESSAGE, REASONER_SYSTEM_MESSAGE
from src.langchain.rate_limiter import RateLimitHandler

if TYPE_CHECKING:
    from codegen import Codebase


class ChatAgent:
    """Agent for interacting with a codebase."""

    def __init__(self, codebase: "Codebase", model_provider: str = "copilot", model_name: str = "gpt-4.1", memory: bool = True, tools: Optional[list[BaseTool]] = None, interactive: bool = True, condensed_logging: bool = True, short_format: bool = True, **kwargs):
        """Initialize a ChatAgent.

        Args:
            codebase: The codebase to operate on
            model_provider: The model provider to use ("anthropic" or "openai" or "google" or "copilot")
            model_name: Name of the model to use
            memory: Whether to let LLM keep track of the conversation history
            tools: Additional tools to use
            interactive: Whether to use the interactive system message (default: True)
            condensed_logging: Whether to use condensed logging instead of verbose output (default: True)
            short_format: Whether to use short format for tool calls (default: True). When True, shows "tool call --> view_file x.py". When False, shows detailed format with timestamps.
            **kwargs: Additional LLM configuration options. Supported options:
                - temperature: Temperature parameter (0-1)
                - top_p: Top-p sampling parameter (0-1)
                - top_k: Top-k sampling parameter (>= 1)
                - max_tokens: Maximum number of tokens to generate
        """
        self.codebase = codebase
        self.model_provider = model_provider
        self.model_name = model_name
        self.memory = memory
        self.tools = tools
        self.interactive = interactive
        self.condensed_logging = condensed_logging
        self.short_format = short_format
        self.kwargs = kwargs
        self.rate_limiter = RateLimitHandler()

        # Choose system message based on interactive flag
        if interactive:
            system_message = SystemMessage(INTERACTIVE_SYSTEM_MESSAGE)
        else:
            system_message = SystemMessage(REASONER_SYSTEM_MESSAGE)

        self.system_message = system_message

        self.agent = create_codebase_agent(
            self.codebase,
            model_provider=model_provider,
            model_name=model_name,
            memory=memory,
            additional_tools=tools,
            system_message=system_message,
            **kwargs
        )

    def _recreate_agent(self, model_provider: str, model_name: str):
        """Recreate the agent with new model configuration."""
        self.model_provider = model_provider
        self.model_name = model_name

        self.agent = create_codebase_agent(
            self.codebase,
            model_provider=model_provider,
            model_name=model_name,
            memory=self.memory,
            additional_tools=self.tools,
            system_message=self.system_message,
            **self.kwargs
        )

    def run(self, prompt: str, thread_id: Optional[str] = None, stream_output: bool = False) -> str:
        """Run the agent with a prompt, defaulting to streaming with fallback to regular mode.

        Args:
            prompt: The prompt to run
            thread_id: Optional thread ID for message history. If None, a new thread is created.
            stream_output: Whether to print output during streaming (useful for interactive mode)

        Returns:
            The agent's response
        """
        def run_func():
            return self._run_internal(prompt, thread_id, stream_output)

        # Use rate limiter to handle rate limits and model fallbacks
        return self.rate_limiter.handle_rate_limit_with_retry(
            run_func,
            agent_instance=self,
            model_provider=self.model_provider,
            model_name=self.model_name
        )

    def _run_internal(self, prompt: str, thread_id: Optional[str] = None, stream_output: bool = False) -> str:
        """Internal run method without rate limiting."""
        if thread_id is None:
            thread_id = str(uuid4())

        # Use simple string content for better compatibility with Copilot API
        config = {"configurable": {"thread_id": thread_id}, "recursion_limit": 200}
        input_data = {"messages": [HumanMessage(content=prompt)]}

        # Try streaming first (default mode)
        try:
            if stream_output:
                return self._run_with_realtime_streaming(input_data, config)
            elif self.condensed_logging:
                return self._run_with_condensed_logging(input_data, config, prompt)
            else:
                return self._run_with_silent_streaming(input_data, config)

        except Exception as e:
            return self._handle_fallback_and_errors(input_data, config, e, stream_output)

    def _run_with_realtime_streaming(self, input_data: dict, config: dict) -> str:
        """Execute agent with real-time streaming output."""
        from src.langchain.real_time_stream import create_realtime_stream

        # Stream with real-time output
        stream = self.agent.stream(input_data, config=config, stream_mode="values")
        realtime_stream = create_realtime_stream(
            stream,
            show_tools=True,
            show_reasoning=True
        )

        # Consume the stream with real-time output
        last_result = None
        for result in realtime_stream:
            last_result = result

        return last_result.get("final_answer", "") if last_result else ""

    def _run_with_condensed_logging(self, input_data: dict, config: dict, prompt: str) -> str:
        """Execute agent with condensed logging."""
        from src.langchain.condensed_logger import CondensedAgentLogger, create_condensed_stream

        logger = CondensedAgentLogger(short_format=self.short_format)
        task_description = prompt if isinstance(prompt, str) else "Chat interaction"
        logger.log_start(task_description)

        # Stream with condensed logging
        stream = self.agent.stream(input_data, config=config, stream_mode="values")
        condensed_stream = create_condensed_stream(stream, logger)

        # Consume the stream
        last_result = None
        for result in condensed_stream:
            last_result = result

        return last_result.get("final_answer", "") if last_result else ""

    def _run_with_silent_streaming(self, input_data: dict, config: dict) -> str:
        """Execute agent with silent streaming (no output)."""
        stream = self.agent.stream(input_data, config=config, stream_mode="values")
        last_result = None
        for s in stream:
            last_result = s
        return last_result.get("final_answer", "") if last_result else ""

    def _handle_fallback_and_errors(self, input_data: dict, config: dict, original_error: Exception, show_output: bool = False) -> str:
        """Handle fallback to regular invoke mode when streaming fails."""
        # Fallback to regular invoke if streaming fails
        if show_output:
            print(f"\n⚠️  Streaming failed ({str(original_error)[:50]}...), falling back to regular mode")

        try:
            result = self.agent.invoke(input_data, config=config)

            # Extract the response from the result
            response = self._extract_response_from_result(result)

            if show_output:
                print(f"\n{response}")

            return response

        except Exception as fallback_error:
            error_msg = f"Both streaming and regular mode failed. Streaming error: {str(original_error)[:100]}. Fallback error: {str(fallback_error)[:100]}"
            if show_output:
                print(f"\n❌ {error_msg}")
            raise Exception(error_msg)

    def _extract_response_from_result(self, result) -> str:
        """Extract the response content from agent result."""
        if isinstance(result, dict):
            if "final_answer" in result:
                return result["final_answer"]
            elif "messages" in result and result["messages"]:
                last_message = result["messages"][-1]
                if isinstance(last_message, AIMessage):
                    return last_message.content
                else:
                    return str(last_message)
            else:
                return str(result)
        else:
            return str(result)

    def chat(self, prompt: str, thread_id: Optional[str] = None) -> tuple[str, str]:
        """Chat with the agent, maintaining conversation history with streaming by default.

        Args:
            prompt: The user message
            thread_id: Optional thread ID for message history. If None, a new thread is created.

        Returns:
            A tuple of (response_content, thread_id) to allow continued conversation
        """
        if thread_id is None:
            thread_id = str(uuid4())
            print(f"Starting new chat thread: {thread_id}")
        else:
            print(f"Continuing chat thread: {thread_id}")

        # Default to streaming output for interactive chat with fallback
        response = self.run(prompt, thread_id=thread_id, stream_output=True)
        return response, thread_id

    def get_chat_history(self, thread_id: str) -> list:
        """Retrieve the chat history for a specific thread.

        Args:
            thread_id: The thread ID to retrieve history for

        Returns:
            List of messages in the conversation history
        """
        # Access the agent's memory to get conversation history
        if hasattr(self.agent, "get_state"):
            state = self.agent.get_state({"configurable": {"thread_id": thread_id}, "recursion_limit": 200})
            if state and "messages" in state.values:
                return state.values["messages"]
        return []
