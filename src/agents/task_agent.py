"""Task agent for running iterative tasks with condensed logging."""

import time
from typing import Dict, Any, Optional
from langchain_core.messages import AIMessage, HumanMessage, ToolMessage

from src.agents.code_agent import CodeAgent


class TaskResult:
    """Result of a task execution."""

    def __init__(self, success: bool, message: str, iterations: int, final_response: str = ""):
        self.success = success
        self.message = message
        self.iterations = iterations
        self.final_response = final_response


class CondensedLogger:
    """Logger that provides condensed, readable output for task execution."""

    def __init__(self, show_tools: bool = True, show_errors: bool = True):
        self.show_tools = show_tools
        self.show_errors = show_errors
        self.current_step = 0
        self.start_time = time.time()

    def log_step_start(self, step: int, task: str):
        """Log the start of a new step."""
        self.current_step = step
        elapsed = time.time() - self.start_time
        print(f"\n🔄 Step {step} [{elapsed:.1f}s]: {task}")

    def log_tool_use(self, tool_name: str, description: str = ""):
        """Log tool usage in a condensed format."""
        if self.show_tools:
            print(f"   🔧 {tool_name}: {description}")

    def log_ai_response(self, response: str, max_length: int = 100):
        """Log AI response in condensed format."""
        if len(response) > max_length:
            response = response[:max_length] + "..."
        print(f"   💭 {response}")

    def log_error(self, error: str):
        """Log errors."""
        if self.show_errors:
            print(f"   ❌ Error: {error}")

    def log_completion(self, success: bool, iterations: int, final_message: str = ""):
        """Log task completion."""
        elapsed = time.time() - self.start_time
        status = "✅ Completed" if success else "❌ Failed"
        print(f"\n{status} after {iterations} iterations ({elapsed:.1f}s)")
        if final_message:
            print(f"📝 {final_message}")


class TaskAgent:
    """Agent that runs iterative tasks with condensed logging."""

    def __init__(self, code_agent: CodeAgent, max_iterations: int = 10, condensed_logging: bool = True):
        """Initialize TaskAgent.

        Args:
            code_agent: The underlying CodeAgent to use
            max_iterations: Maximum number of iterations before stopping
            condensed_logging: Whether to use condensed logging (default: True)
        """
        self.code_agent = code_agent
        self.max_iterations = max_iterations
        self.condensed_logging = condensed_logging
        self.logger = CondensedLogger() if condensed_logging else None

    def run_task(self, task: str) -> Dict[str, Any]:
        """Run a task with iterative execution and condensed logging.

        Args:
            task: The task description to execute

        Returns:
            Dictionary with success status, message, iterations, and response
        """
        if self.logger:
            self.logger.log_step_start(1, task)

        try:
            # Run the task with the code agent and intercept the stream for logging
            response = self._run_with_logging(task)

            if self.logger:
                self.logger.log_completion(True, 1, "Task completed successfully")

            return {
                "success": True,
                "message": "Task completed successfully",
                "iterations": 1,
                "response": response
            }

        except Exception as e:
            if self.logger:
                self.logger.log_error(str(e))
                self.logger.log_completion(False, 1, f"Task failed: {str(e)}")

            return {
                "success": False,
                "message": f"Task failed: {str(e)}",
                "iterations": 1,
                "response": ""
            }

    def _run_with_logging(self, task: str) -> str:
        """Run the task with condensed logging by intercepting the agent stream."""
        from langchain_core.runnables.config import RunnableConfig
        from langchain_core.messages import HumanMessage, AIMessage, ToolMessage

        # Prepare the input
        content = [{"type": "text", "text": task}]
        config = RunnableConfig(
            configurable={"thread_id": self.code_agent.thread_id},
            tags=self.code_agent.tags,
            metadata=self.code_agent.metadata,
            recursion_limit=200
        )

        # Stream the agent execution
        stream = self.code_agent.agent.stream(
            {"messages": [HumanMessage(content=content)]},
            config=config,
            stream_mode="values"
        )

        step_count = 0
        for s in stream:
            if len(s["messages"]) == 0 or isinstance(s["messages"][-1], HumanMessage):
                continue

            message = s["messages"][-1]
            step_count += 1

            if isinstance(message, AIMessage):
                # Log AI reasoning
                if hasattr(message, 'tool_calls') and message.tool_calls:
                    for tool_call in message.tool_calls:
                        if self.logger:
                            self.logger.log_tool_use(
                                tool_call['name'],
                                f"with {len(str(tool_call.get('args', {})))} chars of input"
                            )
                elif message.content:
                    if self.logger:
                        content_str = message.content
                        if isinstance(content_str, list) and len(content_str) > 0:
                            content_str = content_str[0].get('text', str(content_str))
                        self.logger.log_ai_response(str(content_str))

            elif isinstance(message, ToolMessage):
                # Log tool results
                if self.logger and message.content:
                    result_preview = str(message.content)[:100]
                    if len(str(message.content)) > 100:
                        result_preview += "..."
                    self.logger.log_tool_use(
                        f"Result from {getattr(message, 'name', 'tool')}",
                        result_preview
                    )

        # Return the final result
        return s.get("final_answer", "")
