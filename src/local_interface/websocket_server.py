#!/usr/bin/env python3
"""
Local WebSocket server for real-time agent interaction.
Perfect for local development with streaming feedback.
"""

import asyncio
import json
import uuid
import websockets
from typing import Dict, Set
from datetime import datetime
from pathlib import Path
import sys

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.agents.chat_agent import ChatAgent
from src.agents.code_agent import CodeAgent
from codegen import Codebase


class LocalAgentServer:
    """Local WebSocket server for agent interaction."""
    
    def __init__(self, host: str = "localhost", port: int = 8765):
        self.host = host
        self.port = port
        self.clients: Set[websockets.WebSocketServerProtocol] = set()
        self.agents: Dict[str, object] = {}
        self.codebase = None
        
    async def register_client(self, websocket):
        """Register a new client connection."""
        self.clients.add(websocket)
        await self.send_to_client(websocket, {
            "type": "connected",
            "message": "Connected to Rippr Local Agent Server",
            "timestamp": datetime.now().isoformat()
        })
        print(f"Client connected. Total clients: {len(self.clients)}")
    
    async def unregister_client(self, websocket):
        """Unregister a client connection."""
        self.clients.discard(websocket)
        print(f"Client disconnected. Total clients: {len(self.clients)}")
    
    async def send_to_client(self, websocket, data: dict):
        """Send data to a specific client."""
        try:
            await websocket.send(json.dumps(data))
        except websockets.exceptions.ConnectionClosed:
            await self.unregister_client(websocket)
    
    async def broadcast(self, data: dict):
        """Broadcast data to all connected clients."""
        if self.clients:
            await asyncio.gather(
                *[self.send_to_client(client, data) for client in self.clients],
                return_exceptions=True
            )
    
    async def initialize_codebase(self, path: str = "."):
        """Initialize the codebase."""
        try:
            self.codebase = Codebase(path)
            await self.broadcast({
                "type": "codebase_loaded",
                "message": f"Codebase loaded from {path}",
                "path": path
            })
            return True
        except Exception as e:
            await self.broadcast({
                "type": "error",
                "message": f"Failed to load codebase: {str(e)}"
            })
            return False
    
    async def create_agent(self, agent_config: dict) -> str:
        """Create a new agent."""
        agent_id = str(uuid.uuid4())
        
        try:
            if not self.codebase:
                await self.initialize_codebase(agent_config.get("codebase_path", "."))
            
            agent_type = agent_config.get("type", "chat")
            model_provider = agent_config.get("model_provider", "copilot")
            model_name = agent_config.get("model_name")
            
            if agent_type == "chat":
                agent = ChatAgent(
                    codebase=self.codebase,
                    model_provider=model_provider,
                    model_name=model_name,
                    memory=True,
                    interactive=True,
                    condensed_logging=True,
                    short_format=True
                )
            elif agent_type == "code":
                agent = CodeAgent(
                    codebase=self.codebase,
                    model_provider=model_provider,
                    model_name=model_name,
                    memory=True,
                    condensed_logging=True,
                    short_format=True
                )
            else:
                raise ValueError(f"Unknown agent type: {agent_type}")
            
            self.agents[agent_id] = agent
            
            await self.broadcast({
                "type": "agent_created",
                "agent_id": agent_id,
                "agent_type": agent_type,
                "model_provider": model_provider,
                "model_name": model_name
            })
            
            return agent_id
            
        except Exception as e:
            await self.broadcast({
                "type": "error",
                "message": f"Failed to create agent: {str(e)}"
            })
            raise
    
    async def stream_agent_response(self, agent_id: str, message: str, websocket):
        """Stream agent response in real-time."""
        if agent_id not in self.agents:
            await self.send_to_client(websocket, {
                "type": "error",
                "message": f"Agent {agent_id} not found"
            })
            return
        
        agent = self.agents[agent_id]
        
        try:
            # Send start signal
            await self.send_to_client(websocket, {
                "type": "response_start",
                "agent_id": agent_id,
                "message": message
            })
            
            # For real streaming, we'd need to modify the agent to support streaming
            # For now, we'll simulate streaming by running and chunking the response
            response = agent.run(message, stream_output=False)
            
            # Simulate streaming by sending chunks
            words = response.split()
            current_chunk = ""
            
            for i, word in enumerate(words):
                current_chunk += word + " "
                
                # Send chunk every few words
                if len(current_chunk.split()) >= 3 or i == len(words) - 1:
                    await self.send_to_client(websocket, {
                        "type": "response_chunk",
                        "agent_id": agent_id,
                        "chunk": current_chunk.strip(),
                        "is_final": i == len(words) - 1
                    })
                    current_chunk = ""
                    await asyncio.sleep(0.05)  # Small delay for streaming effect
            
            # Send completion signal
            await self.send_to_client(websocket, {
                "type": "response_complete",
                "agent_id": agent_id,
                "full_response": response
            })
            
        except Exception as e:
            await self.send_to_client(websocket, {
                "type": "error",
                "message": f"Agent execution failed: {str(e)}"
            })
    
    async def handle_message(self, websocket, message_data: dict):
        """Handle incoming messages from clients."""
        message_type = message_data.get("type")
        
        if message_type == "create_agent":
            agent_config = message_data.get("config", {})
            try:
                agent_id = await self.create_agent(agent_config)
                await self.send_to_client(websocket, {
                    "type": "agent_created_response",
                    "agent_id": agent_id,
                    "success": True
                })
            except Exception as e:
                await self.send_to_client(websocket, {
                    "type": "agent_created_response",
                    "success": False,
                    "error": str(e)
                })
        
        elif message_type == "send_message":
            agent_id = message_data.get("agent_id")
            message = message_data.get("message")
            if agent_id and message:
                await self.stream_agent_response(agent_id, message, websocket)
        
        elif message_type == "list_agents":
            agent_list = [
                {"id": aid, "type": type(agent).__name__} 
                for aid, agent in self.agents.items()
            ]
            await self.send_to_client(websocket, {
                "type": "agents_list",
                "agents": agent_list
            })
        
        elif message_type == "ping":
            await self.send_to_client(websocket, {
                "type": "pong",
                "timestamp": datetime.now().isoformat()
            })
        
        else:
            await self.send_to_client(websocket, {
                "type": "error",
                "message": f"Unknown message type: {message_type}"
            })
    
    async def handle_client(self, websocket, path):
        """Handle a client connection."""
        await self.register_client(websocket)
        
        try:
            async for message in websocket:
                try:
                    data = json.loads(message)
                    await self.handle_message(websocket, data)
                except json.JSONDecodeError:
                    await self.send_to_client(websocket, {
                        "type": "error",
                        "message": "Invalid JSON format"
                    })
                except Exception as e:
                    await self.send_to_client(websocket, {
                        "type": "error",
                        "message": f"Error processing message: {str(e)}"
                    })
        except websockets.exceptions.ConnectionClosed:
            pass
        finally:
            await self.unregister_client(websocket)
    
    async def start(self):
        """Start the WebSocket server."""
        print(f"🚀 Starting Local Agent Server on ws://{self.host}:{self.port}")
        print("💡 This server provides real-time streaming for local development")
        
        async with websockets.serve(self.handle_client, self.host, self.port):
            print(f"✅ Server running on ws://{self.host}:{self.port}")
            await asyncio.Future()  # Run forever


def main():
    """Start the local WebSocket server."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Rippr Local WebSocket Server")
    parser.add_argument("--host", default="localhost", help="Server host")
    parser.add_argument("--port", type=int, default=8765, help="Server port")
    
    args = parser.parse_args()
    
    server = LocalAgentServer(host=args.host, port=args.port)
    
    try:
        asyncio.run(server.start())
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")


if __name__ == "__main__":
    main()
