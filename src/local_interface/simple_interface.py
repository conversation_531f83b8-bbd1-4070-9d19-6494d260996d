#!/usr/bin/env python3
"""
Simple Python interface for local agent interaction.
Perfect for quick scripting and automation.
"""

import sys
from pathlib import Path
from typing import Optional, Dict, Any

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.agents.chat_agent import ChatAgent
from src.agents.code_agent import CodeAgent
from codegen import Codebase


class LocalAgentInterface:
    """Simple interface for local agent interaction."""
    
    def __init__(self, codebase_path: str = "."):
        """Initialize with a codebase."""
        self.codebase_path = codebase_path
        self.codebase = None
        self.agents: Dict[str, Any] = {}
        self.current_agent = None
        
        print(f"🚀 Rippr Local Interface")
        print(f"📁 Codebase: {codebase_path}")
        
        try:
            self.codebase = Codebase(codebase_path)
            print("✅ Codebase loaded successfully")
        except Exception as e:
            print(f"❌ Failed to load codebase: {e}")
            sys.exit(1)
    
    def create_chat_agent(self, name: str = "chat_agent", 
                         model_provider: str = "copilot", 
                         model_name: Optional[str] = None) -> str:
        """Create a chat agent."""
        try:
            agent = ChatAgent(
                codebase=self.codebase,
                model_provider=model_provider,
                model_name=model_name,
                memory=True,
                interactive=True,
                condensed_logging=True,
                short_format=True
            )
            
            self.agents[name] = agent
            self.current_agent = agent
            
            print(f"✅ Chat agent '{name}' created with {model_provider}")
            return name
            
        except Exception as e:
            print(f"❌ Failed to create chat agent: {e}")
            raise
    
    def create_code_agent(self, name: str = "code_agent",
                         model_provider: str = "vertex",
                         model_name: Optional[str] = None) -> str:
        """Create a code agent."""
        try:
            agent = CodeAgent(
                codebase=self.codebase,
                model_provider=model_provider,
                model_name=model_name,
                memory=True,
                condensed_logging=True,
                short_format=True
            )
            
            self.agents[name] = agent
            self.current_agent = agent
            
            print(f"✅ Code agent '{name}' created with {model_provider}")
            return name
            
        except Exception as e:
            print(f"❌ Failed to create code agent: {e}")
            raise
    
    def switch_agent(self, name: str):
        """Switch to a different agent."""
        if name not in self.agents:
            print(f"❌ Agent '{name}' not found")
            return False
        
        self.current_agent = self.agents[name]
        print(f"🔄 Switched to agent '{name}'")
        return True
    
    def list_agents(self):
        """List all created agents."""
        if not self.agents:
            print("No agents created yet")
            return
        
        print("📋 Available agents:")
        for name, agent in self.agents.items():
            agent_type = type(agent).__name__
            is_current = "👈 CURRENT" if agent == self.current_agent else ""
            print(f"  - {name} ({agent_type}) {is_current}")
    
    def chat(self, message: str) -> str:
        """Send a message to the current agent."""
        if not self.current_agent:
            print("❌ No agent selected. Create an agent first.")
            return ""
        
        try:
            print(f"\n💬 You: {message}")
            print("🤖 Agent: ", end="", flush=True)
            
            # Use streaming output for real-time feedback
            response = self.current_agent.run(message, stream_output=True)
            print()  # New line after streaming output
            
            return response
            
        except Exception as e:
            print(f"\n❌ Error: {e}")
            return ""
    
    def interactive_mode(self):
        """Start interactive chat mode."""
        print("\n" + "="*60)
        print("🎯 Interactive Mode")
        print("="*60)
        print("Commands:")
        print("  /help     - Show this help")
        print("  /agents   - List agents")
        print("  /switch <name> - Switch agent")
        print("  /create chat <name> [provider] - Create chat agent")
        print("  /create code <name> [provider] - Create code agent")
        print("  /exit     - Exit")
        print("="*60)
        
        while True:
            try:
                user_input = input("\n💬 You: ").strip()
                
                if not user_input:
                    continue
                
                # Handle commands
                if user_input.startswith('/'):
                    if user_input == '/help':
                        print("Commands:")
                        print("  /help     - Show this help")
                        print("  /agents   - List agents")
                        print("  /switch <name> - Switch agent")
                        print("  /create chat <name> [provider] - Create chat agent")
                        print("  /create code <name> [provider] - Create code agent")
                        print("  /exit     - Exit")
                    
                    elif user_input == '/agents':
                        self.list_agents()
                    
                    elif user_input.startswith('/switch '):
                        name = user_input[8:].strip()
                        self.switch_agent(name)
                    
                    elif user_input.startswith('/create '):
                        parts = user_input[8:].split()
                        if len(parts) >= 2:
                            agent_type = parts[0]
                            name = parts[1]
                            provider = parts[2] if len(parts) > 2 else None
                            
                            if agent_type == 'chat':
                                self.create_chat_agent(name, provider or "copilot")
                            elif agent_type == 'code':
                                self.create_code_agent(name, provider or "vertex")
                            else:
                                print("❌ Unknown agent type. Use 'chat' or 'code'")
                        else:
                            print("❌ Usage: /create <chat|code> <name> [provider]")
                    
                    elif user_input == '/exit':
                        print("👋 Goodbye!")
                        break
                    
                    else:
                        print(f"❓ Unknown command: {user_input}")
                    
                    continue
                
                # Send message to agent
                self.chat(user_input)
                
            except KeyboardInterrupt:
                print("\n👋 Goodbye!")
                break
            except EOFError:
                print("\n👋 Goodbye!")
                break


def main():
    """Main entry point."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Rippr Local Interface")
    parser.add_argument("--path", "-p", default=".", help="Codebase path")
    parser.add_argument("--agent-type", choices=["chat", "code"], default="chat", help="Default agent type")
    parser.add_argument("--model-provider", default="copilot", help="Model provider")
    parser.add_argument("--interactive", "-i", action="store_true", help="Start in interactive mode")
    parser.add_argument("--message", "-m", help="Send a single message")
    
    args = parser.parse_args()
    
    # Initialize interface
    interface = LocalAgentInterface(args.path)
    
    # Create default agent
    if args.agent_type == "chat":
        interface.create_chat_agent("default", args.model_provider)
    else:
        interface.create_code_agent("default", args.model_provider)
    
    # Handle different modes
    if args.message:
        # Single message mode
        interface.chat(args.message)
    elif args.interactive:
        # Interactive mode
        interface.interactive_mode()
    else:
        # Quick demo
        print("\n🎯 Quick Demo Mode")
        print("Try these commands:")
        print("  python src/local_interface/simple_interface.py -m 'Analyze this codebase'")
        print("  python src/local_interface/simple_interface.py -i  # Interactive mode")
        
        # Show a quick example
        interface.chat("Hello! Can you tell me about this codebase?")


if __name__ == "__main__":
    main()
