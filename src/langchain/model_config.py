"""Centralized model configuration for all providers."""

from dataclasses import dataclass
from typing import Dict, Optional, Any, List
import os


@dataclass
class ModelConfig:
    """Configuration for a specific model."""
    name: str
    max_tokens: int
    temperature: float = 0.0
    top_p: Optional[float] = None
    top_k: Optional[int] = None
    requires_auth: bool = True
    auth_env_vars: List[str] = None
    litellm_format: Optional[str] = None  # How to format for LiteLLM
    
    def __post_init__(self):
        if self.auth_env_vars is None:
            self.auth_env_vars = []


@dataclass
class ProviderConfig:
    """Configuration for a model provider."""
    name: str
    default_model: str
    models: Dict[str, ModelConfig]
    uses_litellm: bool = False
    auth_env_vars: List[str] = None
    setup_instructions: str = ""
    
    def __post_init__(self):
        if self.auth_env_vars is None:
            self.auth_env_vars = []


# Define all model configurations
MODEL_CONFIGS = {
    # Vertex AI (Google Cloud)
    "vertex": ProviderConfig(
        name="vertex",
        default_model="gemini-2.5-pro-preview-05-06",
        uses_litellm=True,
        auth_env_vars=["GOOGLE_APPLICATION_CREDENTIALS", "VERTEX_PROJECT", "VERTEX_LOCATION"],
        setup_instructions="Set GOOGLE_APPLICATION_CREDENTIALS, VERTEX_PROJECT, and VERTEX_LOCATION environment variables",
        models={
            "gemini-2.5-pro-preview-05-06": ModelConfig(
                name="gemini-2.5-pro-preview-05-06",
                max_tokens=64000,
                litellm_format="vertex_ai/gemini-2.5-pro-preview-05-06"
            ),
            "gemini-2.5-flash-preview-05-20": ModelConfig(
                name="gemini-2.5-flash-preview-05-20",
                max_tokens=64000,
                litellm_format="vertex_ai/gemini-2.5-flash-preview-05-20"
            )
        }
    ),
    
    # Google AI Studio
    "google": ProviderConfig(
        name="google",
        default_model="gemini-2.5-pro-preview-05-06",
        uses_litellm=False,
        auth_env_vars=["GOOGLE_API_KEY"],
        setup_instructions="Set GOOGLE_API_KEY environment variable",
        models={
            "gemini-2.5-pro-preview-05-06": ModelConfig(
                name="gemini-2.5-pro-preview-05-06",
                max_tokens=64000
            ),
            "gemini-2.5-flash-preview-05-20": ModelConfig(
                name="gemini-2.5-flash-preview-05-20",
                max_tokens=64000
            ),  
        }
    ),
    
    # GitHub Copilot
    "copilot": ProviderConfig(
        name="copilot",
        default_model="gpt-4.1",
        uses_litellm=False,
        auth_env_vars=["GITHUB_TOKEN"],
        setup_instructions="Authenticate with GitHub Copilot",
        models={
            "claude-3.7-sonnet": ModelConfig(
                name="claude-3.7-sonnet",
                max_tokens=90000
            ),
            "gpt-4.1": ModelConfig(
                name="gpt-4.1",
                max_tokens=120000
            ),
            "claude-sonnet-4": ModelConfig(
                name="claude-sonnet-4",
                max_tokens=80000
            ),
        }
    ),
    
    # OpenAI
    "openai": ProviderConfig(
        name="openai",
        default_model="gpt-4.1",
        uses_litellm=False,
        auth_env_vars=["OPENAI_API_KEY"],
        setup_instructions="Set OPENAI_API_KEY environment variable",
        models={
            "gpt-4o": ModelConfig(
                name="gpt-4o",
                max_tokens=4096
            ),
            "gpt-4o-mini": ModelConfig(
                name="gpt-4o-mini",
                max_tokens=4096
            ),
            "gpt-4-turbo": ModelConfig(
                name="gpt-4-turbo",
                max_tokens=4096
            ),
        }
    ),
    
    # Anthropic
    "anthropic": ProviderConfig(
        name="anthropic",
        default_model="claude-3.7-sonnet",
        uses_litellm=False,
        auth_env_vars=["ANTHROPIC_API_KEY"],
        setup_instructions="Set ANTHROPIC_API_KEY environment variable",
        models={
            "claude-3-5-sonnet-20241022": ModelConfig(
                name="claude-3-5-sonnet-20241022",
                max_tokens=8192
            ),
            "claude-3-5-haiku-20241022": ModelConfig(
                name="claude-3-5-haiku-20241022",
                max_tokens=8192
            ),
            "claude-3-opus-20240229": ModelConfig(
                name="claude-3-opus-20240229",
                max_tokens=8192
            ),
        }
    ),
    
    # xAI
    "xai": ProviderConfig(
        name="xai",
        default_model="grok-beta",
        uses_litellm=False,
        auth_env_vars=["XAI_API_KEY"],
        setup_instructions="Set XAI_API_KEY environment variable",
        models={
            "grok-beta": ModelConfig(
                name="grok-beta",
                max_tokens=12000
            ),
        }
    ),
}

# Default provider order (fallback chain)
DEFAULT_PROVIDER_ORDER = ["vertex", "google", "copilot", "openai", "anthropic"]

# Default provider
DEFAULT_PROVIDER = "vertex"


def get_provider_config(provider: str) -> ProviderConfig:
    """Get configuration for a specific provider."""
    if provider not in MODEL_CONFIGS:
        available = ", ".join(MODEL_CONFIGS.keys())
        raise ValueError(f"Unknown provider '{provider}'. Available providers: {available}")
    return MODEL_CONFIGS[provider]


def get_model_config(provider: str, model: Optional[str] = None) -> ModelConfig:
    """Get configuration for a specific model."""
    provider_config = get_provider_config(provider)
    
    if model is None:
        model = provider_config.default_model
    
    if model not in provider_config.models:
        available = ", ".join(provider_config.models.keys())
        raise ValueError(f"Unknown model '{model}' for provider '{provider}'. Available models: {available}")
    
    return provider_config.models[model]


def get_default_provider() -> str:
    """Get the default provider."""
    return DEFAULT_PROVIDER


def get_default_model(provider: str) -> str:
    """Get the default model for a provider."""
    return get_provider_config(provider).default_model


def get_litellm_model_name(provider: str, model: str) -> str:
    """Get the LiteLLM formatted model name."""
    model_config = get_model_config(provider, model)
    if model_config.litellm_format:
        return model_config.litellm_format
    
    # Fallback formatting for providers without explicit litellm_format
    if provider == "vertex":
        return f"vertex_ai/{model}"
    elif provider in ["claude", "anthropic"]:
        return model
    elif provider == "replicate":
        return f"replicate/{model}" if not model.startswith("replicate/") else model
    elif provider == "cohere":
        return model
    elif provider == "bedrock":
        return f"bedrock/{model}" if not model.startswith("bedrock/") else model
    elif provider == "azure":
        return f"azure/{model}" if not model.startswith("azure/") else model
    elif provider == "palm":
        return f"palm/{model}" if not model.startswith("palm/") else model
    else:
        return f"{provider}/{model}" if "/" not in model else model


def validate_provider_auth(provider: str) -> bool:
    """Check if a provider has the required authentication configured."""
    provider_config = get_provider_config(provider)
    
    if not provider_config.auth_env_vars:
        return True  # No auth required
    
    # Check if any of the required auth env vars are set
    for env_var in provider_config.auth_env_vars:
        if os.getenv(env_var):
            return True
    
    return False


def get_available_providers() -> List[str]:
    """Get list of all available providers."""
    return list(MODEL_CONFIGS.keys())


def get_available_models(provider: str) -> List[str]:
    """Get list of available models for a provider."""
    provider_config = get_provider_config(provider)
    return list(provider_config.models.keys())


def get_provider_setup_instructions(provider: str) -> str:
    """Get setup instructions for a provider."""
    provider_config = get_provider_config(provider)
    return provider_config.setup_instructions


def find_best_available_provider() -> Optional[str]:
    """Find the best available provider based on authentication and default order."""
    for provider in DEFAULT_PROVIDER_ORDER:
        if validate_provider_auth(provider):
            return provider
    return None


def get_model_kwargs(provider: str, model: Optional[str] = None, **overrides) -> Dict[str, Any]:
    """Get model kwargs for a specific provider/model combination."""
    model_config = get_model_config(provider, model)
    
    kwargs = {
        "temperature": model_config.temperature,
        "max_tokens": model_config.max_tokens,
    }
    
    if model_config.top_p is not None:
        kwargs["top_p"] = model_config.top_p
    
    if model_config.top_k is not None:
        kwargs["top_k"] = model_config.top_k
    
    # Apply any overrides
    kwargs.update(overrides)
    
    return kwargs 