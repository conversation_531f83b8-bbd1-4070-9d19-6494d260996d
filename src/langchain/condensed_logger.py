"""Condensed logging for LangGraph agent execution."""

import time
from typing import Any, Dict, Generator, Optional
from langchain_core.messages import AIMessage, HumanMessage, ToolMessage


class CondensedAgentLogger:
    """Provides condensed, readable logging for LangGraph agent execution."""
    
    def __init__(self, show_tools: bool = True, show_reasoning: bool = True, show_errors: bool = True, short_format: bool = True):
        self.show_tools = show_tools
        self.show_reasoning = show_reasoning
        self.show_errors = show_errors
        self.short_format = short_format
        self.start_time = time.time()
        self.step_count = 0
        self.tool_calls_in_progress = {}
    
    def log_start(self, task: str):
        """Log the start of agent execution."""
        print(f"\n🚀 Starting task: {task}")
        print("=" * 60)
    
    def log_step(self, step_type: str, content: str = "", tool_name: str = "", tool_args: dict = None):
        """Log a step in the agent execution."""
        self.step_count += 1
        elapsed = time.time() - self.start_time
        
        if step_type == "reasoning":
            if self.show_reasoning and content:
                if self.short_format:
                    preview = content[:50] + "..." if len(content) > 50 else content
                    print(f"💭 {preview}")
                else:
                    preview = content[:100] + "..." if len(content) > 100 else content
                    print(f"💭 [{elapsed:.1f}s] Reasoning: {preview}")
        
        elif step_type == "tool_call":
            if self.show_tools:
                if self.short_format:
                    # Extract key info for short format with more detail
                    if tool_args:
                        key_info = ""
                        details = []
                        
                        # File-related parameters
                        if 'filepath' in tool_args or 'file_path' in tool_args or 'target_file' in tool_args:
                            filepath = tool_args.get('filepath') or tool_args.get('file_path') or tool_args.get('target_file')
                            details.append(filepath)
                        
                        # Line number parameters
                        if 'start_line_one_indexed' in tool_args and 'end_line_one_indexed_inclusive' in tool_args:
                            start = tool_args['start_line_one_indexed']
                            end = tool_args['end_line_one_indexed_inclusive']
                            details.append(f"lines {start}-{end}")
                        elif 'line_number' in tool_args:
                            details.append(f"line {tool_args['line_number']}")
                        elif 'start_line' in tool_args and 'end_line' in tool_args:
                            details.append(f"lines {tool_args['start_line']}-{tool_args['end_line']}")
                        
                        # Query/search parameters
                        if 'query' in tool_args:
                            query = str(tool_args['query'])[:25] + "..." if len(str(tool_args['query'])) > 25 else str(tool_args['query'])
                            details.append(f"'{query}'")
                        elif 'search_term' in tool_args:
                            term = str(tool_args['search_term'])[:25] + "..." if len(str(tool_args['search_term'])) > 25 else str(tool_args['search_term'])
                            details.append(f"'{term}'")
                        elif 'pattern' in tool_args:
                            pattern = str(tool_args['pattern'])[:25] + "..." if len(str(tool_args['pattern'])) > 25 else str(tool_args['pattern'])
                            details.append(f"pattern '{pattern}'")
                        
                        # Directory/path parameters
                        if 'relative_workspace_path' in tool_args:
                            details.append(f"dir '{tool_args['relative_workspace_path']}'")
                        elif 'directory' in tool_args:
                            details.append(f"dir '{tool_args['directory']}'")
                        
                        # Symbol/function parameters
                        if 'symbol_name' in tool_args:
                            details.append(f"symbol '{tool_args['symbol_name']}'")
                        elif 'function_name' in tool_args:
                            details.append(f"func '{tool_args['function_name']}'")
                        
                        # Content length for edits
                        if 'content' in tool_args:
                            content_len = len(str(tool_args['content']))
                            if content_len > 100:
                                details.append(f"{content_len} chars")
                        
                        # Old/new text for replacements
                        if 'old_text' in tool_args and 'new_text' in tool_args:
                            old_len = len(str(tool_args['old_text']))
                            new_len = len(str(tool_args['new_text']))
                            details.append(f"replace {old_len}→{new_len} chars")
                        
                        # Include/exclude patterns
                        if 'include_pattern' in tool_args:
                            details.append(f"include '{tool_args['include_pattern']}'")
                        if 'exclude_pattern' in tool_args:
                            details.append(f"exclude '{tool_args['exclude_pattern']}'")
                        
                        # Depth for directory listings
                        if 'depth' in tool_args:
                            details.append(f"depth {tool_args['depth']}")
                        
                        # Boolean flags
                        if 'should_read_entire_file' in tool_args and tool_args['should_read_entire_file']:
                            details.append("entire file")
                        if 'case_sensitive' in tool_args and tool_args['case_sensitive']:
                            details.append("case-sensitive")
                        
                        if details:
                            key_info = f" {' '.join(details)}"
                        
                        print(f"tool call --> {tool_name}{key_info}")
                    else:
                        print(f"tool call --> {tool_name}")
                else:
                    print(f"🔧 [{elapsed:.1f}s] {content}")
        
        elif step_type == "tool_result":
            if self.show_tools:
                if self.short_format:
                    # Just show a simple completion indicator
                    print(f"✓")
                else:
                    print(f"📋 [{elapsed:.1f}s] {content}")
        
        elif step_type == "error":
            if self.show_errors:
                if self.short_format:
                    print(f"❌ {content}")
                else:
                    print(f"❌ [{elapsed:.1f}s] Error: {content}")
    
    def log_completion(self, success: bool, final_answer: str = ""):
        """Log the completion of agent execution."""
        elapsed = time.time() - self.start_time
        status = "✅ Completed" if success else "❌ Failed"
        print(f"\n{status} in {elapsed:.1f}s after {self.step_count} steps")
        
        if final_answer:
            print(f"📝 Result: {final_answer}")
        print("=" * 60)


def create_condensed_stream(stream: Generator, logger: Optional[CondensedAgentLogger] = None) -> Generator:
    """Wrap a LangGraph stream to provide condensed logging."""
    if logger is None:
        logger = CondensedAgentLogger(short_format=True)
    
    last_state = None
    
    for chunk in stream:
        # Track the state
        last_state = chunk
        
        # Extract messages from the chunk
        messages = chunk.get("messages", [])
        if not messages:
            yield chunk
            continue
        
        last_message = messages[-1]
        
        # Log based on message type
        if isinstance(last_message, AIMessage):
            # Check for tool calls
            if hasattr(last_message, 'tool_calls') and last_message.tool_calls:
                for tool_call in last_message.tool_calls:
                    tool_name = tool_call.get('name', 'unknown_tool')
                    args = tool_call.get('args', {})
                    
                    # Create a detailed summary of the tool call
                    if isinstance(args, dict):
                        # Show key arguments for better visibility
                        key_args = []
                        for key, value in args.items():
                            if key in ['filepath', 'file_path', 'path', 'filename', 'target_file']:
                                key_args.append(f"file: {value}")
                            elif key in ['start_line_one_indexed', 'end_line_one_indexed_inclusive']:
                                if key == 'start_line_one_indexed' and 'end_line_one_indexed_inclusive' in args:
                                    key_args.append(f"lines: {value}-{args['end_line_one_indexed_inclusive']}")
                                elif key == 'end_line_one_indexed_inclusive' and 'start_line_one_indexed' not in [k for k, _ in key_args]:
                                    # Only add if start_line wasn't already processed
                                    pass
                            elif key in ['line_number']:
                                key_args.append(f"line: {value}")
                            elif key in ['query', 'search_term', 'pattern']:
                                preview = str(value)[:25] + "..." if len(str(value)) > 25 else str(value)
                                key_args.append(f"{key}: {preview}")
                            elif key in ['content', 'text', 'message']:
                                content_len = len(str(value))
                                if content_len > 100:
                                    key_args.append(f"{key}: {content_len} chars")
                                else:
                                    preview = str(value)[:30] + "..." if len(str(value)) > 30 else str(value)
                                    key_args.append(f"{key}: {preview}")
                            elif key in ['relative_workspace_path', 'directory']:
                                key_args.append(f"dir: {value}")
                            elif key in ['symbol_name', 'function_name']:
                                key_args.append(f"symbol: {value}")
                            elif key in ['old_text', 'new_text'] and 'old_text' in args and 'new_text' in args:
                                if key == 'old_text':
                                    old_len = len(str(args['old_text']))
                                    new_len = len(str(args['new_text']))
                                    key_args.append(f"replace: {old_len}→{new_len} chars")
                            elif key in ['should_read_entire_file'] and value:
                                key_args.append("entire file")
                            elif key in ['case_sensitive'] and value:
                                key_args.append("case-sensitive")
                            elif key in ['depth']:
                                key_args.append(f"depth: {value}")
                            elif len(str(value)) < 20:
                                key_args.append(f"{key}: {value}")
                        
                        if key_args:
                            arg_summary = f"{len(args)} args ({', '.join(key_args)})"
                        else:
                            arg_summary = f"{len(args)} args"
                    else:
                        arg_summary = f"{len(str(args))} chars"
                    
                    logger.log_step("tool_call", f"Calling {tool_name} with {arg_summary}", tool_name=tool_name, tool_args=args)
            
            # Log reasoning if no tool calls
            elif last_message.content and not hasattr(last_message, 'tool_calls'):
                content = last_message.content
                if isinstance(content, list) and len(content) > 0:
                    content = content[0].get('text', str(content))
                logger.log_step("reasoning", str(content))
        
        elif isinstance(last_message, ToolMessage):
            # Log tool results
            tool_name = getattr(last_message, 'name', 'tool')
            content = last_message.content
            
            if content:
                # Create a preview of the result
                content_str = str(content)
                if len(content_str) > 100:
                    preview = content_str[:100] + "..."
                else:
                    preview = content_str
                
                # Try to extract meaningful info from common tool outputs
                if "[VIEW FILE]" in content_str:
                    lines = content_str.split('\n')
                    file_line = next((line for line in lines if "[VIEW FILE]" in line), "")
                    preview = file_line.strip()
                elif "[EDIT FILE]" in content_str:
                    preview = "File edited successfully"
                elif '"status": "success"' in content_str:
                    preview = "Operation completed successfully"
                
                logger.log_step("tool_result", f"{tool_name}: {preview}")
            else:
                # Always log tool completion, even if no content
                logger.log_step("tool_result", f"{tool_name}: completed")
        
        yield chunk
    
    # Log completion
    if last_state:
        final_answer = last_state.get("final_answer", "")
        logger.log_completion(True, final_answer)


def run_agent_with_condensed_logging(agent, input_data: Dict[str, Any], config: Dict[str, Any], task_description: str = "") -> Dict[str, Any]:
    """Run an agent with condensed logging, using streaming-first with fallback."""
    logger = CondensedAgentLogger(short_format=True)
    
    # Log start
    task = task_description or input_data.get("query", "Unknown task")
    logger.log_start(task)
    
    # Try streaming first (default mode)
    try:
        # Stream the agent execution with condensed logging
        stream = agent.stream(input_data, config=config, stream_mode="values")
        condensed_stream = create_condensed_stream(stream, logger)
        
        # Consume the stream
        last_result = None
        for result in condensed_stream:
            last_result = result
        
        return last_result
    
    except Exception as e:
        # Fallback to regular invoke if streaming fails
        logger.log_step("error", f"Streaming failed ({str(e)[:50]}...), falling back to regular mode")
        
        try:
            result = agent.invoke(input_data, config=config)
            
            # Extract the response from the result
            if isinstance(result, dict):
                if "final_answer" in result:
                    final_answer = result["final_answer"]
                elif "messages" in result and result["messages"]:
                    last_message = result["messages"][-1]
                    if isinstance(last_message, AIMessage):
                        final_answer = last_message.content
                    else:
                        final_answer = str(last_message)
                else:
                    final_answer = str(result)
                
                # Create a result dict similar to streaming output
                fallback_result = result.copy() if isinstance(result, dict) else {"final_answer": str(result)}
                if "final_answer" not in fallback_result:
                    fallback_result["final_answer"] = final_answer
                
                logger.log_completion(True, final_answer)
                return fallback_result
            else:
                final_answer = str(result)
                logger.log_completion(True, final_answer)
                return {"final_answer": final_answer}
                
        except Exception as fallback_error:
            error_msg = f"Both streaming and regular mode failed. Streaming error: {str(e)[:100]}. Fallback error: {str(fallback_error)[:100]}"
            logger.log_step("error", error_msg)
            logger.log_completion(False)
            raise Exception(error_msg)
