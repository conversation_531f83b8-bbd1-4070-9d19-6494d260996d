import time
import logging
from typing import Callable, Any, Optional, List, Dict
from functools import wraps

logger = logging.getLogger(__name__)


class RateLimitHandler:
    """Handles rate limiting with automatic retries and model fallback."""
    
    def __init__(self):
        # Define fallback chain: copilot -> claude-3.7-sonnet -> claude-3.5-sonnet
        self.fallback_models = [
            {"provider": "anthropic", "name": "claude-3-5-sonnet-20241022"},
            {"provider": "anthropic", "name": "claude-3-5-sonnet-20240620"}
        ]
        self.retry_delay = 60  # 60 seconds wait time
        self.max_retries = 2  # Try twice before switching models
    
    def is_rate_limit_error(self, error: Exception) -> bool:
        """Check if the error is a rate limit error."""
        error_str = str(error).lower()
        rate_limit_indicators = [
            "rate limit",
            "rate_limit", 
            "ratelimit",
            "too many requests",
            "quota exceeded",
            "429",
            "rate exceeded"
        ]
        return any(indicator in error_str for indicator in rate_limit_indicators)
    
    def handle_rate_limit_with_retry(self, func: Callable, agent_instance=None, *args, **kwargs) -> Any:
        """
        Execute a function with rate limit handling and automatic retries.
        
        Args:
            func: The function to execute
            agent_instance: The agent instance to recreate if needed
            *args: Arguments to pass to the function
            **kwargs: Keyword arguments to pass to the function
            
        Returns:
            The result of the function execution
            
        Raises:
            Exception: If all retries and fallbacks fail
        """
        original_model_provider = kwargs.get('model_provider')
        original_model_name = kwargs.get('model_name')
        
        # Try with original model first
        for attempt in range(self.max_retries):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                if self.is_rate_limit_error(e):
                    if attempt < self.max_retries - 1:
                        print(f"⏳ Rate limit hit with {original_model_provider} - {original_model_name}. Waiting {self.retry_delay} seconds before retry {attempt + 2}/{self.max_retries}...")
                        time.sleep(self.retry_delay)
                        continue
                    else:
                        print(f"⚠️  Rate limit hit with {original_model_provider} - {original_model_name}. Max retries reached, trying fallback models...")
                        break
                else:
                    # Not a rate limit error, re-raise immediately
                    raise e
        
        # Try fallback models
        for fallback_model in self.fallback_models:
            print(f"🔄 Switching to fallback model: {fallback_model['provider']} - {fallback_model['name']}")
            
            # Recreate agent with new model if agent_instance provided
            if agent_instance and hasattr(agent_instance, '_recreate_agent'):
                agent_instance._recreate_agent(fallback_model['provider'], fallback_model['name'])
            
            for attempt in range(self.max_retries):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    if self.is_rate_limit_error(e):
                        if attempt < self.max_retries - 1:
                            print(f"⏳ Rate limit hit with {fallback_model['provider']} - {fallback_model['name']}. Waiting {self.retry_delay} seconds before retry {attempt + 2}/{self.max_retries}...")
                            time.sleep(self.retry_delay)
                            continue
                        else:
                            print(f"⚠️  Rate limit hit with {fallback_model['provider']} - {fallback_model['name']}. Max retries reached, trying next fallback...")
                            break
                    else:
                        # Not a rate limit error, re-raise immediately
                        raise e
        
        # If we get here, all models failed
        fallback_names = [f"{m['provider']} - {m['name']}" for m in self.fallback_models]
        raise Exception(f"All models failed with rate limits. Original: {original_model_provider} - {original_model_name}, Fallbacks: {fallback_names}")


def with_rate_limit_handling(rate_limiter: Optional[RateLimitHandler] = None):
    """
    Decorator to add rate limit handling to agent methods.
    
    Args:
        rate_limiter: Optional RateLimitHandler instance. If None, creates a new one.
    """
    if rate_limiter is None:
        rate_limiter = RateLimitHandler()
    
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(self, *args, **kwargs):
            # Create a wrapper function that recreates the agent with new model config
            def recreate_and_run(*args, **kwargs):
                # If model config changed, recreate the agent
                if hasattr(self, 'model_provider') and hasattr(self, 'model_name'):
                    current_provider = getattr(self, 'model_provider', None)
                    current_name = getattr(self, 'model_name', None)
                    new_provider = kwargs.get('model_provider', current_provider)
                    new_name = kwargs.get('model_name', current_name)
                    
                    if new_provider != current_provider or new_name != current_name:
                        print(f"🔄 Recreating agent with new model: {new_provider} - {new_name}")
                        self._recreate_agent(new_provider, new_name)
                
                # Remove model config from kwargs before calling original function
                filtered_kwargs = {k: v for k, v in kwargs.items() if k not in ['model_provider', 'model_name']}
                return func(self, *args, **filtered_kwargs)
            
            return rate_limiter.handle_rate_limit_with_retry(recreate_and_run, *args, **kwargs)
        
        return wrapper
    return decorator


class AgentRateLimitMixin:
    """Mixin class to add rate limit handling capabilities to agents."""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.rate_limiter = RateLimitHandler()
        self._original_model_provider = getattr(self, 'model_provider', None)
        self._original_model_name = getattr(self, 'model_name', None)
    
    def _recreate_agent(self, model_provider: str, model_name: str):
        """Recreate the agent with new model configuration."""
        # Store the new model config
        self.model_provider = model_provider
        self.model_name = model_name
        
        # This method should be implemented by the concrete agent class
        # to recreate the agent with the new model configuration
        raise NotImplementedError("Subclasses must implement _recreate_agent method")
    
    def run_with_rate_limit_handling(self, prompt: str, **kwargs) -> str:
        """
        Run the agent with automatic rate limit handling and model fallback.
        
        Args:
            prompt: The prompt to run
            **kwargs: Additional arguments to pass to the run method
            
        Returns:
            The agent's response
        """
        def run_func(*args, **run_kwargs):
            return self.run(prompt, **kwargs)
        
        return self.rate_limiter.handle_rate_limit_with_retry(
            run_func,
            model_provider=self.model_provider,
            model_name=self.model_name
        ) 