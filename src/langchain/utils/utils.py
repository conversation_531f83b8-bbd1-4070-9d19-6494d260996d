from langchain_core.language_models import LLM


def get_max_model_input_tokens(llm: LLM) -> int:
    """Get the maximum input tokens for the current model.

    Returns:
        int: Maximum number of input tokens supported by the model
    """
    # Get the model name from the LLM instance - try multiple possible attributes
    model_name = getattr(llm, 'model_name', None)
    if not model_name:
        model_name = getattr(llm, '_model', None)
    if not model_name:
        # Try to get from the underlying model if it exists
        underlying_model = getattr(llm, 'model', None)
        if underlying_model:
            model_name = getattr(underlying_model, 'model_name', None)

    # Convert to lowercase string for comparison
    model_name = str(model_name).lower() if model_name else ''

    # For Claude models not explicitly listed, if model name contains "claude", use <PERSON>'s limit
    if "claude" in model_name:
        return 200000
    # For GPT-4 models - GitHub Copilot has different limits than OpenAI
    elif "gpt-4o" in model_name:
        return 64000  # GitHub Copilot limit for gpt-4o
    elif "gpt-4" in model_name:
        return 128000
    # For Grok models
    elif "grok" in model_name:
        return 1000000
    # For Gemini models
    elif "gemini" in model_name:
        return 1000000  # Gemini 1.5 supports up to 1M tokens

    # default to gpt as it's lower bound
    return 128000
