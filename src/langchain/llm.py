"""LLM implementation supporting both OpenAI and Anthropic models."""

import logging
import os
from collections.abc import Sequence
from typing import Any, Optional

from src.copilot.client import CopilotChat
from src.langchain.model_config import (
    get_provider_config,
    get_model_config,
    get_default_provider,
    get_default_model,
    get_litellm_model_name,
    get_model_kwargs,
    validate_provider_auth,
    get_provider_setup_instructions,
)
from langchain_anthropic import Chat<PERSON>nthropic
from langchain_core.callbacks import CallbackManagerForLLMRun
from langchain_core.language_models.base import LanguageModelInput
from langchain_core.language_models.chat_models import BaseChatModel
from langchain_core.messages import BaseMessage
from langchain_core.outputs import ChatResult
from langchain_core.runnables import Runnable
from langchain_core.tools import BaseTool
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_openai import ChatOpenAI
from langchain_xai import ChatXA<PERSON>
from pydantic import Field

# Import LiteLLM for additional provider support
try:
    from langchain_litellm import ChatLiteLLM
    LITELLM_AVAILABLE = True
except ImportError:
    try:
        # Fallback to deprecated version for backwards compatibility
        from langchain_community.chat_models import ChatLiteLLM
        LITELLM_AVAILABLE = True
    except ImportError:
        LITELLM_AVAILABLE = False

logger = logging.getLogger(__name__)


class LLM(BaseChatModel):
    """A unified chat model that supports OpenAI, Anthropic, Google, XAI, Copilot, and many other providers through LiteLLM."""

    model_provider: str = Field(default_factory=get_default_provider, description="The model provider to use.")

    model_name: str = Field(default="", description="Name of the model to use.")

    temperature: float = Field(default=0, description="Temperature parameter for the model.", ge=0, le=1)

    top_p: Optional[float] = Field(default=None, description="Top-p sampling parameter.", ge=0, le=1)

    top_k: Optional[int] = Field(default=None, description="Top-k sampling parameter.", ge=1)

    max_tokens: Optional[int] = Field(default=None, description="Maximum number of tokens to generate.", ge=1)

    def __init__(self, model_provider: Optional[str] = None, model_name: Optional[str] = None, **kwargs: Any) -> None:
        """Initialize the LLM.

        Args:
            model_provider: Provider name. If None, uses default provider from config.
            model_name: Name of the model to use. If None, uses default model for provider.
            **kwargs: Additional configuration options. Supported options:
                - temperature: Temperature parameter (0-1)
                - top_p: Top-p sampling parameter (0-1)
                - top_k: Top-k sampling parameter (>= 1)
                - max_tokens: Maximum number of tokens to generate
        """
        # Use defaults from centralized config if not provided
        if model_provider is None:
            model_provider = get_default_provider()
        
        if model_name is None:
            model_name = get_default_model(model_provider)
        
        # Validate provider and model
        try:
            get_model_config(model_provider, model_name)
        except ValueError as e:
            raise ValueError(f"Invalid model configuration: {e}")
        
        # Check authentication
        if not validate_provider_auth(model_provider):
            instructions = get_provider_setup_instructions(model_provider)
            raise ValueError(f"Authentication not configured for provider '{model_provider}'. {instructions}")

        # Set model provider and name before calling super().__init__
        kwargs["model_provider"] = model_provider
        kwargs["model_name"] = model_name

        # Filter out unsupported kwargs
        supported_kwargs = {"model_provider", "model_name", "temperature", "top_p", "top_k", "max_tokens", "callbacks", "tags", "metadata"}
        filtered_kwargs = {k: v for k, v in kwargs.items() if k in supported_kwargs}

        super().__init__(**filtered_kwargs)
        self._model = self._get_model()

    @property
    def _llm_type(self) -> str:
        """Return identifier for this LLM class."""
        return "unified_chat_model"

    def _get_model_kwargs(self) -> dict[str, Any]:
        """Get kwargs for the specific model provider."""
        # Get base kwargs from centralized config
        kwargs = get_model_kwargs(
            self.model_provider, 
            self.model_name,
            temperature=self.temperature,
            top_p=self.top_p,
            top_k=self.top_k,
            max_tokens=self.max_tokens
        )

        # Add provider-specific parameters
        if self.model_provider in ["anthropic", "google", "openai"]:
            kwargs["model"] = self.model_name
        elif self.model_provider == "xai":
            xai_api_base = os.getenv("XAI_API_BASE", "https://api.x.ai/v1/")
            kwargs.update({"model": self.model_name, "xai_api_base": xai_api_base})
        elif self.model_provider == "copilot":
            kwargs["model_name"] = self.model_name
        else:
            # For LiteLLM providers, use model parameter
            kwargs["model"] = self.model_name
        
        return kwargs

    def _is_litellm_provider(self) -> bool:
        """Check if the provider should use LiteLLM."""
        provider_config = get_provider_config(self.model_provider)
        return provider_config.uses_litellm

    def _get_model(self) -> BaseChatModel:
        """Get the appropriate model instance based on configuration."""
        if self.model_provider == "anthropic":
            return ChatAnthropic(**self._get_model_kwargs(), max_retries=10, timeout=1000)

        elif self.model_provider == "google":
            return ChatGoogleGenerativeAI(**self._get_model_kwargs(), max_retries=10, timeout=1000)

        elif self.model_provider == "copilot":
            return CopilotChat(**self._get_model_kwargs(), max_retries=10, timeout=1000)

        elif self.model_provider == "openai":
            return ChatOpenAI(**self._get_model_kwargs(), max_retries=10, timeout=1000)

        elif self.model_provider == "xai":
            return ChatXAI(**self._get_model_kwargs())

        # Use LiteLLM for all other providers
        elif self._is_litellm_provider():
            if not LITELLM_AVAILABLE:
                msg = "LiteLLM is not installed. Please install it with: pip install litellm"
                raise ValueError(msg)
            
            return self._get_litellm_model()

        msg = f"Unknown model provider: {self.model_provider}. Supported providers: anthropic, openai, google, xai, copilot, vertex, claude, replicate, cohere, bedrock, azure, palm, and more via LiteLLM"
        raise ValueError(msg)

    def _get_litellm_model(self) -> ChatLiteLLM:
        """Get a LiteLLM model instance for the specified provider."""
        # Get base kwargs from centralized config
        kwargs = self._get_model_kwargs()
        
        # Format model name using centralized config
        kwargs["model"] = get_litellm_model_name(self.model_provider, self.model_name)
        
        # Add provider-specific parameters
        if self.model_provider == "vertex":
            # Support for vertex_credentials parameter
            vertex_credentials = os.getenv("VERTEX_CREDENTIALS")
            if vertex_credentials:
                kwargs["vertex_credentials"] = vertex_credentials
            
            # Add vertex project and location if available
            vertex_project = os.getenv("VERTEX_PROJECT")
            vertex_location = os.getenv("VERTEX_LOCATION")
            if vertex_project:
                kwargs["vertex_project"] = vertex_project
            if vertex_location:
                kwargs["vertex_location"] = vertex_location
        
        return ChatLiteLLM(**kwargs)



    def _generate(
        self,
        messages: list[BaseMessage],
        stop: Optional[list[str]] = None,
        run_manager: Optional[CallbackManagerForLLMRun] = None,
        **kwargs: Any,
    ) -> ChatResult:
        """Generate chat completion using the underlying model.

        Args:
            messages: The messages to generate from
            stop: Optional list of stop sequences
            run_manager: Optional callback manager for tracking the run
            **kwargs: Additional arguments to pass to the model

        Returns:
            ChatResult containing the generated completion
        """
        return self._model._generate(messages, stop=stop, run_manager=run_manager, **kwargs)

    def bind_tools(
        self,
        tools: Sequence[BaseTool],
        **kwargs: Any,
    ) -> Runnable[LanguageModelInput, BaseMessage]:
        """Bind tools to the underlying model.

        Args:
            tools: List of tools to bind
            **kwargs: Additional arguments to pass to the model

        Returns:
            Runnable that can be used to invoke the model with tools
        """
        return self._model.bind_tools(tools, **kwargs)
