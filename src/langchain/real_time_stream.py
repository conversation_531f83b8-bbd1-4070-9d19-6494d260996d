"""Real-time streaming implementation for immediate output display."""

import sys
import time
from typing import Generator, Optional, Dict, Any
from langchain_core.messages import AIMessage, HumanMessage, ToolMessage


class RealTimeStreamHandler:
    """Handles real-time streaming output without buffering."""
    
    def __init__(self, show_tools: bool = True, show_reasoning: bool = True, show_final_answer: bool = True):
        self.show_tools = show_tools
        self.show_reasoning = show_reasoning
        self.show_final_answer = show_final_answer
        self.start_time = time.time()
        self.step_count = 0
        self.current_tool_call = None
        self.reasoning_buffer = ""
        
    def process_stream_realtime(self, stream: Generator) -> Generator:
        """Process stream with immediate output display."""
        last_state = None
        
        for chunk in stream:
            last_state = chunk
            
            # Extract messages from the chunk
            messages = chunk.get("messages", [])
            if not messages:
                yield chunk
                continue
            
            last_message = messages[-1]
            
            # Handle different message types with immediate output
            if isinstance(last_message, AIMessage):
                self._handle_ai_message(last_message)
            elif isinstance(last_message, ToolMessage):
                self._handle_tool_message(last_message)
            
            yield chunk
        
        # Show final answer if available
        if last_state and self.show_final_answer:
            final_answer = last_state.get("final_answer", "")
            if final_answer:
                elapsed = time.time() - self.start_time
                print(f"\n✅ Completed in {elapsed:.1f}s")
                print(f"📝 Result: {final_answer}")
    
    def _handle_ai_message(self, message: AIMessage):
        """Handle AI message with immediate output."""
        # Check for tool calls
        if hasattr(message, 'tool_calls') and message.tool_calls:
            for tool_call in message.tool_calls:
                self._show_tool_call(tool_call)
        
        # Handle reasoning/content
        elif message.content:
            content = message.content
            if isinstance(content, list) and len(content) > 0:
                content = content[0].get('text', str(content))
            
            if self.show_reasoning:
                # Stream reasoning content character by character for real-time effect
                self._stream_text(str(content))
    
    def _handle_tool_message(self, message: ToolMessage):
        """Handle tool message with immediate output."""
        if not self.show_tools:
            return
            
        tool_name = getattr(message, 'name', 'tool')
        content = message.content
        
        if content:
            # Show tool result immediately
            content_str = str(content)
            if len(content_str) > 150:
                preview = content_str[:150] + "..."
            else:
                preview = content_str
            
            # Extract meaningful info from common tool outputs
            if "[VIEW FILE]" in content_str:
                lines = content_str.split('\n')
                file_line = next((line for line in lines if "[VIEW FILE]" in line), "")
                showing_line = next((line for line in lines if line.startswith("Showing lines")), "")
                
                if file_line:
                    import re
                    # Extract filename from [VIEW FILE]: filename (xxx lines total)
                    file_match = re.search(r'\[VIEW FILE\]: (.+?) \((\d+) lines total\)', file_line)
                    if file_match:
                        filename = file_match.group(1)
                        total_lines = file_match.group(2)
                        
                        # Check if there's a "Showing lines X-Y" line
                        if showing_line:
                            range_match = re.search(r'Showing lines (\d+)-(\d+)', showing_line)
                            if range_match:
                                start_line, end_line = range_match.groups()
                                preview = f"📁 {filename} (lines {start_line}-{end_line} of {total_lines})"
                            else:
                                preview = f"📁 {filename} ({total_lines} lines total)"
                        else:
                            preview = f"📁 {filename} ({total_lines} lines total)"
                    else:
                        # Fallback to original line
                        preview = file_line.strip()
            elif "[EDIT FILE]" in content_str:
                preview = "✅ File edited successfully"
            elif '"status": "success"' in content_str:
                preview = "✅ Operation completed successfully"
            
            print(f"  → {preview}")
        else:
            print(f"  → {tool_name} completed")
        
        sys.stdout.flush()
    
    def _show_tool_call(self, tool_call: dict):
        """Show tool call immediately."""
        if not self.show_tools:
            return
            
        tool_name = tool_call.get('name', 'unknown_tool')
        args = tool_call.get('args', {})
        
        # Create a concise summary
        if isinstance(args, dict):
            key_info = []
            for key, value in args.items():
                if key in ['filepath', 'file_path', 'path', 'filename', 'target_file']:
                    key_info.append(f"📁 {value}")
                elif key in ['query', 'search_term', 'pattern']:
                    preview = str(value)[:30] + "..." if len(str(value)) > 30 else str(value)
                    key_info.append(f"🔍 {preview}")
                elif key in ['content', 'text', 'message'] and len(str(value)) > 50:
                    key_info.append(f"📝 {len(str(value))} chars")
                elif key in ['line_number']:
                    key_info.append(f"📍 line {value}")
                elif key in ['start_line_one_indexed', 'end_line_one_indexed_inclusive']:
                    if key == 'start_line_one_indexed' and 'end_line_one_indexed_inclusive' in args:
                        key_info.append(f"📍 lines {value}-{args['end_line_one_indexed_inclusive']}")
            
            if key_info:
                info_str = " ".join(key_info[:2])  # Show max 2 key pieces of info
            else:
                info_str = f"{len(args)} args"
        else:
            info_str = f"{len(str(args))} chars"
        
        print(f"\n🔧 {tool_name} {info_str}")
        sys.stdout.flush()
        self.step_count += 1
    
    def _stream_text(self, text: str, delay: float = 0.01):
        """Stream text character by character for real-time effect."""
        if not text.strip():
            return
            
        print(f"\n🤖 ", end="")
        sys.stdout.flush()
        
        # Stream the text
        for char in text:
            print(char, end="")
            sys.stdout.flush()
            if delay > 0:
                time.sleep(delay)
        
        print()  # New line at the end


def create_realtime_stream(stream: Generator, show_tools: bool = True, show_reasoning: bool = True) -> Generator:
    """Create a real-time streaming wrapper that shows output immediately."""
    handler = RealTimeStreamHandler(show_tools=show_tools, show_reasoning=show_reasoning)
    return handler.process_stream_realtime(stream)


def run_agent_with_realtime_streaming(agent, input_data: Dict[str, Any], config: Dict[str, Any], 
                                     show_tools: bool = True, show_reasoning: bool = True) -> Dict[str, Any]:
    """Run an agent with real-time streaming output."""
    print("🚀 Starting task...")
    print("=" * 60)
    
    try:
        # Stream the agent execution with real-time output
        stream = agent.stream(input_data, config=config, stream_mode="values")
        realtime_stream = create_realtime_stream(stream, show_tools=show_tools, show_reasoning=show_reasoning)
        
        # Consume the stream
        last_result = None
        for result in realtime_stream:
            last_result = result
        
        print("=" * 60)
        return last_result
    
    except Exception as e:
        print(f"\n❌ Streaming failed: {str(e)}")
        print("🔄 Falling back to regular mode...")
        
        try:
            result = agent.invoke(input_data, config=config)
            
            # Extract the response from the result
            if isinstance(result, dict):
                if "final_answer" in result:
                    final_answer = result["final_answer"]
                elif "messages" in result and result["messages"]:
                    last_message = result["messages"][-1]
                    if isinstance(last_message, AIMessage):
                        final_answer = last_message.content
                    else:
                        final_answer = str(last_message)
                else:
                    final_answer = str(result)
                
                print(f"📝 Result: {final_answer}")
                print("=" * 60)
                
                # Create a result dict similar to streaming output
                fallback_result = result.copy() if isinstance(result, dict) else {"final_answer": str(result)}
                if "final_answer" not in fallback_result:
                    fallback_result["final_answer"] = final_answer
                
                return fallback_result
            else:
                final_answer = str(result)
                print(f"📝 Result: {final_answer}")
                print("=" * 60)
                return {"final_answer": final_answer}
                
        except Exception as fallback_error:
            error_msg = f"Both streaming and regular mode failed. Streaming error: {str(e)[:100]}. Fallback error: {str(fallback_error)[:100]}"
            print(f"❌ {error_msg}")
            print("=" * 60)
            raise Exception(error_msg) 