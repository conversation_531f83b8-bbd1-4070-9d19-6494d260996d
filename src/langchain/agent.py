"""Demo implementation of an agent with Codegen tools."""

import os
from typing import TYPE_CHECKING, Any, Optional

# Load environment variables from .env file
try:
    from dotenv import load_dotenv
    load_dotenv()  # Load .env file from current directory
    load_dotenv(".env.local")  # Also try .env.local for local overrides
except ImportError:
    # python-dotenv not installed, skip loading
    pass

from langchain.tools import BaseTool
from langchain_core.messages import SystemMessage
from langgraph.checkpoint.memory import MemorySaver
from langgraph.graph.graph import CompiledGraph

if TYPE_CHECKING:
    from src.agents.utils import AgentConfig
from src.langchain.llm import LLM
from src.langchain.model_config import get_default_provider, get_default_model
from src.langchain.prompts import REASONER_SYSTEM_MESSAGE
from src.langchain.prompts import TEST_SYSTEM_MESSAGE
from src.langchain.tools import get_workspace_tools
from src.langchain.tools import get_coder_agent_tools

from src.langchain.graph import create_react_agent

from codegen import Codebase

def create_codebase_agent(
    codebase: "Codebase",
    model_provider: Optional[str] = None,
    model_name: Optional[str] = None,
    system_message: SystemMessage = SystemMessage(REASONER_SYSTEM_MESSAGE),
    memory: bool = True,
    debug: bool = False,
    additional_tools: list[BaseTool] | None = None,
    config: "AgentConfig | None" = None,
    **kwargs,
) -> CompiledGraph:
    """Create an agent with all codebase tools.

    Args:
        codebase: The codebase to operate on
        model_provider: The model provider to use ("anthropic", "openai", "google", "xai", "copilot", or "vertex")
        model_name: Name of the model to use
        verbose: Whether to print agent's thought process (default: True)
        chat_history: Optional list of messages to initialize chat history with
        **kwargs: Additional LLM configuration options. Supported options:
            - temperature: Temperature parameter (0-1)
            - top_p: Top-p sampling parameter (0-1)
            - top_k: Top-k sampling parameter (>= 1)
            - max_tokens: Maximum number of tokens to generate

    Returns:
        Initialized agent with message history
    """
    # Use centralized config for defaults
    if model_provider is None:
        model_provider = get_default_provider()
    if model_name is None:
        model_name = get_default_model(model_provider)
        
    llm = LLM(model_provider=model_provider, model_name=model_name, **kwargs)
    print(f"Using model: {model_provider} - {model_name}")
    # Initialize default tools
    if model_provider == "copilot":
        # GitHub Copilot API supports tools, give it the full workspace toolset
        tools = get_coder_agent_tools(codebase)
        print("🔥 Using GitHub Copilot - full tool support available!")
    elif model_provider == "vertex":
        # Vertex AI through LiteLLM supports tools, but needs compatible schemas
        from src.langchain.tools import get_workspace_tools_vertex_ai
        tools = get_workspace_tools_vertex_ai(codebase)
        print("🔥 Using Vertex AI through LiteLLM - Vertex AI compatible tools loaded!")
    else:
        # Use full tool set for other providers
        tools = get_workspace_tools(codebase)

    if additional_tools:
        # Get names of additional tools
        additional_names = {t.get_name() for t in additional_tools}
        # Keep only tools that don't have matching names in additional_tools
        tools = [t for t in tools if t.get_name() not in additional_names]
        tools.extend(additional_tools)

    memory = MemorySaver() if memory else None

    return create_react_agent(model=llm, tools=tools, system_message=system_message, checkpointer=memory, debug=debug, config=config)


def create_chat_agent(
    codebase: "Codebase",
    model_provider: Optional[str] = None,
    model_name: Optional[str] = None,
    system_message: SystemMessage = SystemMessage(REASONER_SYSTEM_MESSAGE),
    memory: bool = True,
    debug: bool = False,
    additional_tools: list[BaseTool] | None = None,
    config: dict[str, Any] | None = None,  # over here you can pass in the max length of the number of messages
    **kwargs,
) -> CompiledGraph:
    """Create an agent with all codebase tools.

    Args:
        codebase: The codebase to operate on
        model_provider: The model provider to use ("anthropic", "openai", "google", "xai", "copilot", or "vertex")
        model_name: Name of the model to use
        verbose: Whether to print agent's thought process (default: True)
        chat_history: Optional list of messages to initialize chat history with
        **kwargs: Additional LLM configuration options. Supported options:
            - temperature: Temperature parameter (0-1)
            - top_p: Top-p sampling parameter (0-1)
            - top_k: Top-k sampling parameter (>= 1)
            - max_tokens: Maximum number of tokens to generate

    Returns:
        Initialized agent with message history
    """
    # Use centralized config for defaults
    if model_provider is None:
        model_provider = get_default_provider()
    if model_name is None:
        model_name = get_default_model(model_provider)
        
    llm = LLM(model_provider=model_provider, model_name=model_name, **kwargs)

    # Use Vertex AI compatible tools if using Vertex AI provider
    if model_provider == "vertex":
        from src.langchain.tools import get_workspace_tools_vertex_ai
        tools = get_workspace_tools_vertex_ai(codebase)
    else:
        tools = get_workspace_tools(codebase)

    if additional_tools:
        # Get names of additional tools
        additional_names = {t.get_name() for t in additional_tools}
        # Keep only tools that don\'t have matching names in additional_tools
        tools = [t for t in tools if t.get_name() not in additional_names]
        tools.extend(additional_tools)

    memory = MemorySaver() if memory else None

    return create_react_agent(model=llm, tools=tools, system_message=system_message, checkpointer=memory, debug=debug, config=config)


def create_codebase_inspector_agent(
    codebase: "Codebase",
    model_provider: Optional[str] = None,
    model_name: Optional[str] = None,
    system_message: SystemMessage = SystemMessage(REASONER_SYSTEM_MESSAGE),
    memory: bool = True,
    debug: bool = False,
    config: dict[str, Any] | None = None,
    **kwargs,
) -> CompiledGraph:
    """Create an inspector agent with read-only codebase tools.

    Args:
        codebase: The codebase to operate on
        model_provider: The model provider to use ("anthropic", "openai", "google", "xai", "copilot", or "vertex")
        model_name: Name of the model to use
        system_message: Custom system message to use (defaults to standard reasoner message)
        memory: Whether to enable memory/checkpointing
        **kwargs: Additional LLM configuration options

    Returns:
        Compiled langgraph agent
    """
    # Use centralized config for defaults
    if model_provider is None:
        model_provider = get_default_provider()
    if model_name is None:
        model_name = get_default_model(model_provider)
        
    llm = LLM(model_provider=model_provider, model_name=model_name, **kwargs)

    # Get read-only codebase tools - use Vertex AI compatible tools if using Vertex AI provider
    if model_provider == "vertex":
        from src.langchain.tools import get_workspace_tools_vertex_ai
        tools = get_workspace_tools_vertex_ai(codebase)
    else:
        tools = get_workspace_tools(codebase)

    memory = MemorySaver() if memory else None
    return create_react_agent(model=llm, tools=tools, system_message=system_message, checkpointer=memory, debug=debug, config=config)


def create_agent_with_tools(
    tools: list[BaseTool],
    model_provider: Optional[str] = None,
    model_name: Optional[str] = None,
    system_message: SystemMessage = SystemMessage(REASONER_SYSTEM_MESSAGE),
    memory: bool = True,
    debug: bool = False,
    config: dict[str, Any] | None = None,
    **kwargs,
) -> CompiledGraph:
    """Create an agent with a specific set of tools.

    Args:
        codebase: The codebase to operate on
        tools: List of tools to provide to the agent
        model_provider: The model provider to use ("anthropic", "openai", "google", "xai", "copilot", or "vertex")
        model_name: Name of the model to use
        system_message: Custom system message to use (defaults to standard reasoner message)
        memory: Whether to enable memory/checkpointing
        **kwargs: Additional LLM configuration options. Supported options:
            - temperature: Temperature parameter (0-1)
            - top_p: Top-p sampling parameter (0-1)
            - top_k: Top-k sampling parameter (>= 1)
            - max_tokens: Maximum number of tokens to generate

    Returns:
        Compiled langgraph agent
    """
    # Use centralized config for defaults
    if model_provider is None:
        model_provider = get_default_provider()
    if model_name is None:
        model_name = get_default_model(model_provider)
        
    llm = LLM(model_provider=model_provider, model_name=model_name, **kwargs)

    memory = MemorySaver() if memory else None

    return create_react_agent(model=llm, tools=tools, system_message=system_message, checkpointer=memory, debug=debug, config=config)

if __name__ == "__main__":
    import time
    import sys

    # Initialize codebase
    print("Initializing codebase...")
    try:
        from codegen import Codebase
        
        # Get GitHub access token from environment
        github_token = os.getenv("GITHUB_ACCESS_TOKEN")
        if not github_token:
            print("Warning: GITHUB_ACCESS_TOKEN not found in environment. PR creation may fail.")
        else:
            print("✅ GitHub access token found in environment")
            # Set additional environment variables that Codegen might use
            os.environ["GITHUB_TOKEN"] = github_token
            os.environ["GH_TOKEN"] = github_token
        
        # Initialize codebase - try different approaches for GitHub authentication
        print("🔧 Initializing Codebase with GitHub authentication...")
        
        # Try different parameter names for GitHub token
        try:
            # Try access_token parameter first
            codebase = Codebase.from_repo("H0ARK/rippr", language="python", access_token=github_token)
            print("✅ Codebase initialized with access_token parameter")
        except TypeError as e:
            if "access_token" in str(e):
                try:
                    # Try github_token parameter
                    codebase = Codebase.from_repo("H0ARK/rippr", language="python", github_token=github_token)
                    print("✅ Codebase initialized with github_token parameter")
                except TypeError as e2:
                    if "github_token" in str(e2):
                        try:
                            # Try token parameter
                            codebase = Codebase.from_repo("H0ARK/rippr", language="python", token=github_token)
                            print("✅ Codebase initialized with token parameter")
                        except TypeError as e3:
                            # Fall back to environment variables only
                            print("⚠️ No token parameter supported, using environment variables only")
                            codebase = Codebase.from_repo("H0ARK/rippr", language="python")
                            print("✅ Codebase initialized with environment variables")
                    else:
                        raise e2
            else:
                raise e
        
    except Exception as e:
        print(f"Error initializing codebase: {e}")
        sys.exit(1)

    # Create agent with condensed logging
    print("Creating agent...")
    try:
        from src.agents.code_agent import CodeAgent
        
        # Define a list of model configurations to try
        # Setting skip_auth=True will avoid Copilot authentication and use only non-Copilot models
        skip_auth = False  # Set to False if you want to try Copilot models and authenticate
        
        model_configs = []
        if skip_auth:
            # Use Vertex AI with latest Gemini model as primary choice
            model_configs.append(
                {"provider": "vertex", "name": "gemini-2.0-flash-001"}
            )
            # Also add Google Gemini as fallback
            model_configs.append(
                {"provider": "google", "name": "gemini-2.0-flash-001"}
            )
        else:
            # Prioritize Vertex AI with latest Gemini model
            model_configs.extend([
                {"provider": "vertex", "name": "gemini-2.0-flash-001"},  # Primary model - Latest Gemini via Vertex AI
                {"provider": "google", "name": "gemini-2.0-flash-001"},  
                {"provider": "copilot", "name": "claude-3.7-sonnet"},              # Copilot fallback
                {"provider": "copilot", "name": "gpt-4.1"},                         # Final fallback
            ])
        
        # Ensure model_configs is not empty, especially if skip_auth=False and the list might be empty
        if not model_configs:
            print("Error: model_configs list is empty. Defaulting to Vertex AI.")
            model_configs.append({"provider": "vertex", "name": "gemini-2.0-flash-001"})
        
        success = False
        
        for config in model_configs:
            provider = config["provider"]
            model = config["name"]
            
            print(f"\nTrying with model: {provider} - {model}")
            
            try:
                agent = CodeAgent(
                    codebase, 
                    condensed_logging=True, 
                    debug=False, 
                    model_provider=provider, 
                    model_name=model
                )
                
                print("\\nAsking agent to update readme.md...")
                result = agent.run("You are an independent agent and must complete the task by yourself. Completing a task means committing your changes and pushing a pull request to the repository. \\n\\nTask: Update the README.md to reflect the current state of the codebase. Make sure to:\\n1. Review the current codebase structure\\n2. Update the README.md with accurate information\\n3. Commit your changes with a descriptive message\\n4. Create a pull request with your changes\\n\\nIMPORTANT: You must use the create_pr tool to create a pull request after making your changes.")
                print(f"\\nFinal result: {result[:200]}{'...' if len(result) > 200 else ''}")
                success = True
                break
                
            except ValueError as e:
                if "rate limit" in str(e).lower():
                    print(f"Rate limit hit with {provider} - {model}. Trying next model configuration...")
                    time.sleep(5)  # Small delay before trying the next configuration
                else:
                    print(f"\nError with {provider} - {model}: {e}")
            except Exception as e:
                print(f"\nUnexpected error with {provider} - {model}: {e}")
        
        if not success:
            print("\nFailed to run with any model configuration. Please check API keys and rate limits.")
            
    except Exception as e:
        print(f"Error setting up model configurations: {e}")
        sys.exit(1)
