"""Langchain tools for workspace operations."""

from langchain_core.tools.base import BaseTool

from codegen.sdk.core.codebase import Codebase

from .tools import (
    CommitTool,
    CreateFileTool,
    DeleteFileTool,
    EditFileTool,
    ListDirectoryTool,
    RevealSymbolTool,
    RipGrepTool,
    SemanticEditTool,
    ViewFileTool,
)

__all__ = [
    # Tool classes
    "CommitTool",
    "CreateFileTool",
    "DeleteFileTool",
    "EditFileTool",
    "ListDirectoryTool",
    "RevealSymbolTool",
    "RipGrepTool",
    "SemanticEditTool",
    "ViewFileTool",
    # Helper functions
    "get_workspace_tools",
]


def get_workspace_tools(codebase: Codebase) -> list[BaseTool]:
    """Get all workspace tools initialized with a codebase.

    Args:
        codebase: The codebase to operate on

    Returns:
        List of initialized Langchain tools
    """
    return [
        ViewFileTool(codebase),
        ListDirectoryTool(codebase),
        <PERSON>ipGrepTool(codebase),
        <PERSON><PERSON>ile<PERSON>ool(codebase),
        <PERSON><PERSON>FileTool(codebase),
        <PERSON>eteFileT<PERSON>(codebase),
        CommitTool(codebase),
        <PERSON>ealSymbolTool(codebase),
        SemanticEditTool(codebase),
    ]
