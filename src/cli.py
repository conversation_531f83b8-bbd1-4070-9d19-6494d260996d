#!/usr/bin/env python3
"""Interactive CLI for Rippr - A powerful code agent framework."""

import argparse
import os
import sys
from typing import Optional
from uuid import uuid4

# Add the project root to the Python path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

try:
    from dotenv import load_dotenv
    load_dotenv()
    load_dotenv(".env.local")
except ImportError:
    pass

# Import centralized model config
from src.langchain.model_config import (
    get_default_provider,
    get_default_model,
    get_available_providers,
    get_available_models,
)

# Fix for langchain.verbose attribute error
try:
    import langchain
    if not hasattr(langchain, 'verbose'):
        langchain.verbose = False
    if not hasattr(langchain, 'debug'):
        langchain.debug = False
    if not hasattr(langchain, 'llm_cache'):
        langchain.llm_cache = None
except ImportError:
    pass

try:
    from codegen import Codebase
    from src.agents.chat_agent import ChatAgent
    from src.agents.code_agent import CodeAgent
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("💡 Make sure you're running from the project root directory")
    print("💡 Try: cd /path/to/rippr && python rippr.py")
    sys.exit(1)


class RipprCLI:
    """Interactive CLI for Rippr agents."""

    def __init__(self, codebase_path: str = ".", model_provider: Optional[str] = None, model_name: Optional[str] = None):
        """Initialize the CLI with a codebase and model configuration."""
        self.codebase_path = os.path.abspath(codebase_path)

        # Use centralized config for defaults
        if model_provider is None:
            model_provider = get_default_provider()
        if model_name is None:
            model_name = get_default_model(model_provider)

        self.model_provider = model_provider
        self.model_name = model_name
        self.thread_id = None
        self.agent = None

        print(f"🚀 Initializing Rippr CLI")
        print(f"📁 Codebase: {self.codebase_path}")
        print(f"🤖 Model: {model_provider} - {model_name}")

        try:
            self.codebase = Codebase(self.codebase_path)
            print("✅ Codebase loaded successfully")
        except Exception as e:
            print(f"❌ Failed to load codebase: {e}")
            sys.exit(1)

    def create_agent(self, agent_type: str = "chat", interactive: bool = True) -> None:
        """Create an agent of the specified type."""
        try:
            if agent_type == "chat":
                self.agent = ChatAgent(
                    self.codebase,
                    model_provider=self.model_provider,
                    model_name=self.model_name,
                    memory=True,
                    interactive=interactive,
                    condensed_logging=True,
                    short_format=True
                )
                agent_mode = "interactive" if interactive else "standard"
                print(f"💬 Chat agent created ({agent_mode} mode)")
            elif agent_type == "code":
                self.agent = CodeAgent(
                    self.codebase,
                    model_provider=self.model_provider,
                    model_name=self.model_name,
                    memory=True,
                    condensed_logging=True,
                    short_format=True
                )
                print("🔧 Code agent created")
            else:
                raise ValueError(f"Unknown agent type: {agent_type}")

            self.thread_id = str(uuid4())
            print(f"🧵 Started new conversation thread: {self.thread_id[:8]}...")

        except Exception as e:
            print(f"❌ Failed to create {agent_type} agent: {e}")
            print("💡 Try using a different model provider or check your API keys")
            sys.exit(1)

    def interactive_chat(self) -> None:
        """Start an interactive chat session."""
        print("\n" + "="*60)
        print("🎯 Interactive Chat Mode")
        print("="*60)
        print("Type your messages and press Enter to send.")
        print("Commands:")
        print("  /help     - Show this help message")
        print("  /history  - Show conversation history")
        print("  /clear    - Start a new conversation")
        print("  /switch   - Switch between chat and code agent")
        print("  /exit     - Exit the CLI")
        print("="*60)

        while True:
            try:
                # Get user input
                user_input = input("\n💬 You: ").strip()

                if not user_input:
                    continue

                # Handle commands
                if user_input.startswith('/'):
                    if user_input == '/help':
                        self._show_help()
                    elif user_input == '/history':
                        self._show_history()
                    elif user_input == '/clear':
                        self._clear_conversation()
                    elif user_input == '/switch':
                        self._switch_agent()
                    elif user_input == '/exit':
                        print("👋 Goodbye!")
                        break
                    else:
                        print(f"❓ Unknown command: {user_input}")
                        print("Type /help for available commands")
                    continue

                # Send message to agent
                print("\n🤖 Agent:", end=" ")
                try:
                    if hasattr(self.agent, 'chat'):
                        # Use real-time streaming for interactive chat
                        response = self.agent.run(user_input, thread_id=self.thread_id, stream_output=True)
                        # Don't print response again - it was already streamed during run()
                    else:
                        response = self.agent.run(user_input)
                        print(f"\n{response}")

                except KeyboardInterrupt:
                    print("\n\n⏸️  Interrupted by user. Type /exit to quit or continue chatting.")
                    continue
                except Exception as e:
                    error_msg = str(e)
                    print(f"\n❌ Error: {error_msg}")

                    # Provide helpful suggestions based on error type
                    if "rate limit" in error_msg.lower():
                        if "all models failed" in error_msg.lower():
                            print("💡 All available models hit rate limits. Please wait a few minutes before trying again.")
                        else:
                            print("💡 Rate limit reached. The system will automatically retry and switch models if needed.")
                    elif "api key" in error_msg.lower() or "authentication" in error_msg.lower():
                        print("💡 Check your API keys in the .env file or environment variables.")
                    elif "connection" in error_msg.lower() or "network" in error_msg.lower():
                        print("💡 Check your internet connection and try again.")
                    else:
                        print("💡 Try rephrasing your message or use /help for available commands.")

            except KeyboardInterrupt:
                print("\n\n👋 Goodbye!")
                break
            except EOFError:
                print("\n\n👋 Goodbye!")
                break

    def _show_help(self) -> None:
        """Show help information."""
        print("\n📖 Help - Available Commands:")
        print("  /help     - Show this help message")
        print("  /history  - Show conversation history")
        print("  /clear    - Start a new conversation")
        print("  /switch   - Switch between chat and code agent")
        print("  /exit     - Exit the CLI")
        print("\n💡 Tips:")
        print("  - Ask questions about your codebase")
        print("  - Request code analysis or modifications")
        print("  - Use natural language to describe what you want")

    def _show_history(self) -> None:
        """Show conversation history."""
        if hasattr(self.agent, 'get_chat_history') and self.thread_id:
            try:
                history = self.agent.get_chat_history(self.thread_id)
                if history:
                    print(f"\n📜 Conversation History (Thread: {self.thread_id[:8]}...):")
                    for i, msg in enumerate(history[-10:], 1):  # Show last 10 messages
                        role = getattr(msg, 'type', 'unknown')
                        content = getattr(msg, 'content', str(msg))
                        print(f"  {i}. [{role}] {content[:100]}{'...' if len(content) > 100 else ''}")
                else:
                    print("\n📜 No conversation history found")
            except Exception as e:
                print(f"\n❌ Failed to retrieve history: {e}")
        else:
            print("\n📜 History not available for this agent type")

    def _clear_conversation(self) -> None:
        """Start a new conversation."""
        self.thread_id = str(uuid4())
        print(f"\n🧹 Started new conversation thread: {self.thread_id[:8]}...")

    def _switch_agent(self) -> None:
        """Switch between agent types."""
        current_type = "chat" if isinstance(self.agent, ChatAgent) else "code"
        new_type = "code" if current_type == "chat" else "chat"

        print(f"\n🔄 Switching from {current_type} agent to {new_type} agent...")
        self.create_agent(new_type)

    def run_single_command(self, command: str, realtime_streaming: bool = False) -> None:
        """Run a single command and exit."""
        print(f"\n🎯 Running command: {command}")
        try:
            # Enable streaming output for ChatAgent to show tool calls
            if hasattr(self.agent, 'chat'):
                if realtime_streaming:
                    # Use real-time streaming for immediate output
                    response = self.agent.run(command, stream_output=True)
                else:
                    response, _ = self.agent.chat(command, None)
                # Response was already printed during streaming
            else:
                response = self.agent.run(command)
                print(f"\n🤖 Response:\n{response}")
        except Exception as e:
            error_msg = str(e)
            print(f"\n❌ Error: {error_msg}")

            # Provide helpful suggestions based on error type
            if "rate limit" in error_msg.lower():
                if "all models failed" in error_msg.lower():
                    print("💡 All available models hit rate limits. Please wait a few minutes before trying again.")
                else:
                    print("💡 Rate limit reached. The system automatically retried and switched models.")
            elif "api key" in error_msg.lower() or "authentication" in error_msg.lower():
                print("💡 Check your API keys in the .env file or environment variables.")

            sys.exit(1)


def main():
    """Main CLI entry point."""
    parser = argparse.ArgumentParser(
        description="Rippr - Interactive CLI for code agents",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  rippr                                         # Start interactive chat mode
  rippr --test                                  # Test CLI initialization without starting interactive mode
  rippr --command "analyze this codebase"       # Run a single command
  rippr --agent code --model copilot gpt-4.1    # Use code agent with copilot
  rippr --path /path/to/project                 # Specify codebase path
        """
    )

    parser.add_argument(
        "--path", "-p",
        default=".",
        help="Path to the codebase (default: current directory)"
    )

    parser.add_argument(
        "--agent", "-a",
        choices=["chat", "code"],
        default="chat",
        help="Type of agent to use (default: chat)"
    )

    parser.add_argument(
        "--model-provider", "-mp",
        choices=get_available_providers(),
        default=get_default_provider(),
        help=f"Model provider to use (default: {get_default_provider()})"
    )

    parser.add_argument(
        "--model-name", "-mn",
        help="Model name to use (if not specified, uses default for provider)"
    )

    parser.add_argument(
        "--command", "-c",
        help="Run a single command and exit (non-interactive mode)"
    )

    parser.add_argument(
        "--debug",
        action="store_true",
        help="Enable debug mode"
    )

    parser.add_argument(
        "--test",
        action="store_true",
        help="Test CLI initialization and exit (useful for debugging)"
    )

    parser.add_argument(
        "--interactive",
        action="store_true",
        default=True,
        help="Use interactive system message for more conversational experience (default: True)"
    )

    parser.add_argument(
        "--standard",
        action="store_true",
        help="Use standard system message instead of interactive mode"
    )

    parser.add_argument(
        "--realtime-streaming",
        action="store_true",
        help="Enable real-time streaming output (shows output immediately as it's generated)"
    )

    parser.add_argument(
        "--api-server",
        action="store_true",
        help="Start the REST API server"
    )

    parser.add_argument(
        "--api-host",
        default="0.0.0.0",
        help="API server host (only used with --api-server)"
    )

    parser.add_argument(
        "--api-port",
        type=int,
        default=8000,
        help="API server port (only used with --api-server)"
    )

    parser.add_argument(
        "--local-server",
        action="store_true",
        help="Start the local WebSocket server for real-time streaming"
    )

    parser.add_argument(
        "--local-host",
        default="localhost",
        help="Local server host (only used with --local-server)"
    )

    parser.add_argument(
        "--local-port",
        type=int,
        default=8765,
        help="Local server port (only used with --local-server)"
    )

    parser.add_argument(
        "--simple-interface",
        action="store_true",
        help="Start the simple Python interface"
    )

    args = parser.parse_args()

    # Handle different server modes
    if args.api_server:
        print("🚀 Starting Rippr REST API Server...")
        try:
            from src.api.server import start_server
            start_server(host=args.api_host, port=args.api_port, reload=True)
        except ImportError as e:
            print(f"❌ Failed to import API server: {e}")
            print("💡 Make sure FastAPI dependencies are installed: pip install fastapi uvicorn")
            sys.exit(1)
        except Exception as e:
            print(f"❌ Failed to start API server: {e}")
            sys.exit(1)
        return

    if args.local_server:
        print("🚀 Starting Rippr Local WebSocket Server...")
        try:
            from src.local_interface.websocket_server import LocalAgentServer
            import asyncio
            server = LocalAgentServer(host=args.local_host, port=args.local_port)
            asyncio.run(server.start())
        except ImportError as e:
            print(f"❌ Failed to import local server: {e}")
            print("💡 Make sure websockets is installed: pip install websockets")
            sys.exit(1)
        except Exception as e:
            print(f"❌ Failed to start local server: {e}")
            sys.exit(1)
        return

    if args.simple_interface:
        print("🚀 Starting Rippr Simple Interface...")
        try:
            from src.local_interface.simple_interface import LocalAgentInterface
            interface = LocalAgentInterface(args.path)

            # Create default agent
            if args.agent == "chat":
                interface.create_chat_agent("default", args.model_provider)
            else:
                interface.create_code_agent("default", args.model_provider)

            interface.interactive_mode()
        except ImportError as e:
            print(f"❌ Failed to import simple interface: {e}")
            sys.exit(1)
        except Exception as e:
            print(f"❌ Failed to start simple interface: {e}")
            sys.exit(1)
        return

    # Determine interactive mode
    interactive_mode = args.interactive and not args.standard

    # Initialize CLI
    cli = RipprCLI(
        codebase_path=args.path,
        model_provider=args.model_provider,
        model_name=args.model_name
    )

    # Create agent
    cli.create_agent(args.agent, interactive=interactive_mode)

    # Run in appropriate mode
    if args.test:
        print("✅ CLI test completed successfully!")
        print("🎯 Interactive mode is ready to use. Run without --test to start chatting.")
    elif args.command:
        cli.run_single_command(args.command, realtime_streaming=args.realtime_streaming)
    else:
        cli.interactive_chat()


if __name__ == "__main__":
    main()