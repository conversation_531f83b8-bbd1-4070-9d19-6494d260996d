import json
import logging
from datetime import datetime, timezone
from enum import Enum
from typing import Any, Dict, List, Literal, Optional, Union

from pydantic import BaseModel, Field, PrivateAttr, ValidationError, model_validator, model_serializer, SecretStr # Import SecretStr

logger = logging.getLogger(__name__)

# --- Pydantic Models for API Requests and Responses (Mirroring Rust) ---


class Role(str, Enum):
    """Defines the roles in a chat conversation."""

    USER = "user"
    ASSISTANT = "assistant"
    SYSTEM = "system"
    TOOL = "tool"


class ImageUrl(BaseModel):
    """Represents an image URL in a multipart message."""

    url: str


class ChatMessagePart(BaseModel):
    """Represents a single part of a multipart message."""

    type: Literal["text", "image_url"]
    text: Optional[str] = None
    image_url: Optional[ImageUrl] = None

    @model_validator(mode="after")
    def check_content(self):
        if self.type == "text" and self.text is None:
            raise ValueError("Text part must have 'text' content.")
        if self.type == "image_url" and self.image_url is None:
            raise ValueError("Image part must have 'image_url' content.")
        return self


class ChatMessageContent:
    """
    Represents the content of a chat message, which can be plain text or multipart.
    Handles serialization/deserialization for the 'untagged' enum behavior in Rust.
    This is NOT a Pydantic model to avoid serialization issues.
    """

    def __init__(self, content: Union[str, List[ChatMessagePart]]):
        if isinstance(content, str):
            self._content_value = content
        elif isinstance(content, list):
            # Validate and convert list items to ChatMessagePart objects
            parsed_parts = []
            for item in content:
                if isinstance(item, ChatMessagePart):
                    parsed_parts.append(item)
                elif isinstance(item, dict):
                    if "type" in item and item["type"] == "text":
                        parsed_parts.append(ChatMessagePart(type="text", text=item.get("text")))
                    elif "type" in item and item["type"] == "image_url":
                        parsed_parts.append(
                            ChatMessagePart(type="image_url", image_url=ImageUrl(url=item.get("image_url", {}).get("url")))
                        )
                    else:
                        raise ValueError(f"Unknown chat message part type: {item.get('type')}")
                else:
                    raise ValueError("Chat message content list must contain ChatMessagePart objects or dictionaries.")
            self._content_value = parsed_parts
        else:
            raise ValueError("Chat message content must be a string or a list of parts.")

    def model_dump(self, **kwargs):
        # Custom serialization to match API's untagged behavior
        if isinstance(self._content_value, str):
            return self._content_value
        elif isinstance(self._content_value, list):
            # Serialize ChatMessagePart objects to dictionaries
            return [part.model_dump(**kwargs) for part in self._content_value]
        return self._content_value

    def __str__(self):
        if isinstance(self._content_value, str):
            return self._content_value
        return json.dumps([part.model_dump() for part in self._content_value])

    def __repr__(self):
        return f"ChatMessageContent(content={self._content_value!r})"

    @property
    def is_multipart(self) -> bool:
        return isinstance(self._content_value, list)

    @property
    def text_content(self) -> Optional[str]:
        if isinstance(self._content_value, str):
            return self._content_value
        # Concatenate text from multipart if present
        texts = [p.text for p in self._content_value if p.type == "text" and p.text is not None]
        return " ".join(texts) if texts else None

    @property
    def image_urls(self) -> List[str]:
        if isinstance(self._content_value, list):
            return [p.image_url.url for p in self._content_value if p.type == "image_url" and p.image_url is not None]
        return []


class FunctionContent(BaseModel):
    """Content of a tool function call."""

    name: str
    arguments: str  # Copilot expects stringified JSON


class ToolCall(BaseModel):
    """Represents a tool call within an assistant message."""

    id: str
    type: Literal["function"] = "function"
    function: FunctionContent


class ChatMessage(BaseModel):
    """Represents a message in the chat conversation."""

    role: Role
    content: Optional[Union[str, List[ChatMessagePart]]] = None  # Direct Union type
    tool_calls: List[ToolCall] = Field(default_factory=list)
    tool_call_id: Optional[str] = None
    # name: Optional[str] = None # For function messages (deprecated in LangChain, but exists in some APIs)

    @model_validator(mode="after")
    def validate_message_roles(self):
        if self.role == Role.ASSISTANT:
            if not self.content and not self.tool_calls:
                raise ValueError("Assistant message must have content or tool_calls.")
            if self.tool_call_id:
                raise ValueError("Assistant message should not have tool_call_id.")
        elif self.role == Role.TOOL:
            if not self.content or not self.tool_call_id:
                raise ValueError("Tool message must have content and tool_call_id.")
            if self.tool_calls:
                raise ValueError("Tool message should not have tool_calls.")
        elif self.role in [Role.USER, Role.SYSTEM]:
            if self.tool_calls or self.tool_call_id:
                raise ValueError(f"{self.role.value} message should not have tool_calls or tool_call_id.")
        return self

    @model_serializer(mode="wrap")
    def serialize_model(self, serializer, info):
        import logging
        logger = logging.getLogger(__name__)
        
        data = {}
        data["role"] = self.role.value

        # Handle content - only include if it exists
        if self.content is not None:
            if isinstance(self.content, str):
                data["content"] = self.content
            elif isinstance(self.content, list):
                # Serialize ChatMessagePart objects to dictionaries
                data["content"] = [part.model_dump() for part in self.content]

        # Handle tool_calls - only include if not empty and role supports it
        if self.tool_calls and self.role in [Role.ASSISTANT]:
            data["tool_calls"] = [tc.model_dump() for tc in self.tool_calls]
            logger.debug(f"Including tool_calls for {self.role} message: {len(self.tool_calls)} calls")
        else:
            logger.debug(f"Excluding tool_calls for {self.role} message (empty: {not self.tool_calls}, role check: {self.role in [Role.ASSISTANT]})")

        # Handle tool_call_id - only include for tool messages
        if self.tool_call_id is not None and self.role == Role.TOOL:
            data["tool_call_id"] = self.tool_call_id
            logger.debug(f"Including tool_call_id for {self.role} message: {self.tool_call_id}")
        else:
            logger.debug(f"Excluding tool_call_id for {self.role} message (None: {self.tool_call_id is None}, role check: {self.role == Role.TOOL})")

        logger.debug(f"ChatMessage.serialize_model() for {self.role} returning: {data}")
        return data

    @property
    def is_multipart(self) -> bool:
        return isinstance(self.content, list)

    @property
    def text_content(self) -> Optional[str]:
        if isinstance(self.content, str):
            return self.content
        elif isinstance(self.content, list):
            # Concatenate text from multipart if present
            texts = [p.text for p in self.content if p.type == "text" and p.text is not None]
            return " ".join(texts) if texts else None
        return None

    @property
    def image_urls(self) -> List[str]:
        if isinstance(self.content, list):
            return [p.image_url.url for p in self.content if p.type == "image_url" and p.image_url is not None]
        return []


class ToolFunction(BaseModel):
    """Schema for a tool function."""

    name: str
    description: str
    parameters: Dict[str, Any]  # This would be a JSON schema


class Tool(BaseModel):
    """Represents a tool definition."""

    type: Literal["function"] = "function"
    function: ToolFunction


class ToolChoice(str, Enum):
    """Strategy for tool selection."""

    AUTO = "auto"
    ANY = "any"
    NONE = "none"


class Request(BaseModel):
    """Request payload for chat completions."""

    intent: bool = False  # Rust has this, might be a Copilot specific param
    n: int = 1
    stream: bool
    temperature: float
    model: str
    messages: List[ChatMessage]
    tools: List[Tool] = Field(default_factory=list)
    tool_choice: Optional[ToolChoice] = None
    max_tokens: Optional[int] = None
    top_p: Optional[float] = None

    def model_dump(self, **kwargs):
        # Use default serialization but clean up None values and empty lists
        data = super().model_dump(**kwargs)
        cleaned_data = {}
        for k, v in data.items():
            # Skip None values
            if v is None:
                continue
            # Skip empty lists
            if isinstance(v, list) and len(v) == 0:
                continue
            # Skip tool_choice if tools is empty
            if k == "tool_choice" and not data.get("tools"):
                continue
            cleaned_data[k] = v
        return cleaned_data


class FunctionChunk(BaseModel):
    """Incremental update for a tool function."""

    name: Optional[str] = None
    arguments: Optional[str] = None


class ToolCallChunk(BaseModel):
    """Incremental update for a tool call."""

    index: int
    id: Optional[str] = None
    function: Optional[FunctionChunk] = None


class ResponseDelta(BaseModel):
    """Incremental changes in a streaming response."""

    content: Optional[str] = None
    role: Optional[Role] = None
    tool_calls: List[ToolCallChunk] = Field(default_factory=list)


class ResponseMessage(BaseModel):
    """Full message in a non-streaming response."""

    content: Optional[str] = None
    role: Optional[Role] = None
    tool_calls: List[ToolCall] = Field(default_factory=list)


class ResponseChoice(BaseModel):
    """A choice in the response (streaming or non-streaming)."""

    index: Optional[int] = None  # Make index optional since some responses don't include it
    finish_reason: Optional[str] = None
    delta: Optional[ResponseDelta] = None  # For streaming
    message: Optional[ResponseMessage] = None  # For non-streaming (full message)


class ResponseEvent(BaseModel):
    """Full response event from the API."""

    choices: List[ResponseChoice]
    id: str
    # Add other fields like usage if present in non-streaming responses, e.g.:
    # usage: Optional[Dict[str, Any]] = None


class ModelLimits(BaseModel):
    """Limits for a specific model."""

    max_context_window_tokens: int = 0
    max_output_tokens: int = 0
    max_prompt_tokens: int = 0


class ModelSupportedFeatures(BaseModel):
    """Supported features of a model."""

    streaming: bool = False
    tool_calls: bool = False
    parallel_tool_calls: bool = False
    vision: bool = False


class ModelCapabilities(BaseModel):
    """Capabilities of a model."""

    family: str
    limits: ModelLimits = Field(default_factory=ModelLimits)
    supports: ModelSupportedFeatures = Field(default_factory=ModelSupportedFeatures)
    object: str = "model_capabilities"  # Matches Rust's literal "object" field


class ModelPolicy(BaseModel):
    """Policy information for a model."""

    state: str


class ModelVendor(str, Enum):
    """Vendor of the model."""

    AZURE_OPENAI = "Azure OpenAI"  # Rust uses alias "Azure OpenAI" for OpenAI
    OPENAI = "OpenAI"  # Direct OpenAI vendor
    GOOGLE = "Google"
    ANTHROPIC = "Anthropic"


class Model(BaseModel):
    """Represents an available model from the /models endpoint."""

    capabilities: ModelCapabilities
    id: str
    name: str
    policy: Optional[ModelPolicy] = None
    vendor: ModelVendor
    model_picker_enabled: bool
    object: str = "model"  # Matches Rust's literal "object" field
    preview: bool
    version: str

    def uses_streaming(self) -> bool:
        return self.capabilities.supports.streaming

    def display_name(self) -> str:
        return self.name

    def max_token_count(self) -> int:
        return self.capabilities.limits.max_prompt_tokens

    def supports_tools(self) -> bool:
        return self.capabilities.supports.tool_calls

    def supports_vision(self) -> bool:
        return self.capabilities.supports.vision

    def supports_parallel_tool_calls(self) -> bool:
        return self.capabilities.supports.parallel_tool_calls


class ModelSchema(BaseModel):
    """Wrapper for the list of models from the /models endpoint."""

    data: List[Model]
    object: str = "list"  # Matches Rust's literal "object" field

    @model_validator(mode="before")
    @classmethod
    def parse_data_resiliently(cls, values: Dict[str, Any]) -> Dict[str, Any]:
        """Custom validator to skip malformed model entries during deserialization."""
        if "data" in values and isinstance(values["data"], list):
            parsed_models = []
            for item in values["data"]:
                try:
                    # Attempt to validate each item as a Model
                    parsed_models.append(Model.model_validate(item))
                except ValidationError as e:
                    logger.warning(f"GitHub Copilot Chat model failed to deserialize: {e}, raw data: {item}")
                except Exception as e:
                    logger.warning(f"Unexpected error deserializing model: {e}, raw data: {item}")
            values["data"] = parsed_models
        return values


class ApiTokenResponse(BaseModel):
    """Response from the /copilot_internal/v2/token endpoint."""

    token: str
    expires_at: int  # Unix timestamp


class ApiToken(BaseModel):
    """Internal representation of the Copilot API token with expiry."""

    api_key: SecretStr # Changed to SecretStr
    expires_at: datetime

    @classmethod
    def from_response(cls, response: ApiTokenResponse) -> "ApiToken":
        expires_at_dt = datetime.fromtimestamp(response.expires_at, tz=timezone.utc)
        return cls(api_key=response.token, expires_at=expires_at_dt)

    def remaining_seconds(self) -> int:
        return max(0, int((self.expires_at - datetime.now(timezone.utc)).total_seconds()))

