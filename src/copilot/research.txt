async fn stream_completion(
    client: Arc<dyn HttpClient>,
    api_key: String,
    request: Request,
) -> Result<BoxStream<'static, Result<ResponseEvent>>> {
    let request_builder = HttpRequest::builder()
        .method(Method::POST)
        .uri(COPILOT_CHAT_COMPLETION_URL)
        .header(
            "Editor-Version",
            format!(
                "Zed/{}",
                option_env!("CARGO_PKG_VERSION").unwrap_or("unknown")
            ),
        )
        .header("Authorization", format!("Bearer {}", api_key))
        .header("Content-Type", "application/json")
        .header("Copilot-Integration-Id", "vscode-chat")
        .header("Copilot-Vision-Request", "true");
 
    let is_streaming = request.stream;


    curl -L \
-H "Accept: application/vnd.github+json" \
-H "Authorization: Bearer ****************************************" \
-H "X-GitHub-Api-Version: 2022-11-28" \
https://models.github.ai/catalog/models