[{"id": "openai/gpt-4.1", "name": "OpenAI GPT-4.1", "publisher": "OpenAI", "summary": "gpt-4.1 outperforms gpt-4o across the board, with major gains in coding, instruction following, and long-context understanding", "rate_limit_tier": "high", "supported_input_modalities": ["text", "image"], "supported_output_modalities": ["text"], "tags": ["multipurpose", "multilingual", "multimodal"]}, {"id": "openai/gpt-4.1-mini", "name": "OpenAI GPT-4.1-mini", "publisher": "OpenAI", "summary": "gpt-4.1-mini outperform gpt-4o-mini across the board, with major gains in coding, instruction following, and long-context handling", "rate_limit_tier": "low", "supported_input_modalities": ["text", "image"], "supported_output_modalities": ["text"], "tags": ["multipurpose", "multilingual", "multimodal"]}, {"id": "openai/gpt-4.1-nano", "name": "OpenAI GPT-4.1-nano", "publisher": "OpenAI", "summary": "gpt-4.1-nano provides gains in coding, instruction following, and long-context handling along with lower latency and cost", "rate_limit_tier": "low", "supported_input_modalities": ["text", "image"], "supported_output_modalities": ["text"], "tags": ["multipurpose", "multilingual", "multimodal"]}, {"id": "openai/gpt-4o", "name": "OpenAI GPT-4o", "publisher": "OpenAI", "summary": "OpenAI's most advanced multimodal model in the gpt-4o family. Can handle both text and image inputs.", "rate_limit_tier": "high", "supported_input_modalities": ["text", "image", "audio"], "supported_output_modalities": ["text"], "tags": ["multipurpose", "multilingual", "multimodal"]}, {"id": "openai/gpt-4o-mini", "name": "OpenAI GPT-4o mini", "publisher": "OpenAI", "summary": "An affordable, efficient AI solution for diverse text and image tasks.", "rate_limit_tier": "low", "supported_input_modalities": ["text", "image", "audio"], "supported_output_modalities": ["text"], "tags": ["multipurpose", "multilingual", "multimodal"]}, {"id": "openai/o1", "name": "OpenAI o1", "publisher": "OpenAI", "summary": "Focused on advanced reasoning and solving complex problems, including math and science tasks. Ideal for applications that require deep contextual understanding and agentic workflows.", "rate_limit_tier": "custom", "supported_input_modalities": ["text", "image"], "supported_output_modalities": ["text"], "tags": ["reasoning", "multilingual", "coding"]}, {"id": "openai/o1-mini", "name": "OpenAI o1-mini", "publisher": "OpenAI", "summary": "Smaller, faster, and 80% cheaper than o1-preview, performs well at code generation and small context operations.", "rate_limit_tier": "custom", "supported_input_modalities": ["text"], "supported_output_modalities": ["text"], "tags": ["reasoning", "multilingual", "coding"]}, {"id": "openai/o1-preview", "name": "OpenAI o1-preview", "publisher": "OpenAI", "summary": "Focused on advanced reasoning and solving complex problems, including math and science tasks. Ideal for applications that require deep contextual understanding and agentic workflows.", "rate_limit_tier": "custom", "supported_input_modalities": ["text"], "supported_output_modalities": ["text"], "tags": ["reasoning", "multilingual", "coding"]}, {"id": "openai/o3", "name": "OpenAI o3", "publisher": "OpenAI", "summary": "o3 includes significant improvements on quality and safety while supporting the existing features of o1 and delivering comparable or better performance.", "rate_limit_tier": "custom", "supported_input_modalities": ["text", "image"], "supported_output_modalities": ["text"], "tags": ["multipurpose", "multilingual", "multimodal"]}, {"id": "openai/o3-mini", "name": "OpenAI o3-mini", "publisher": "OpenAI", "summary": "o3-mini includes the o1 features with significant cost-efficiencies for scenarios requiring high performance.", "rate_limit_tier": "custom", "supported_input_modalities": ["text"], "supported_output_modalities": ["text"], "tags": ["reasoning", "multilingual", "coding"]}, {"id": "openai/o4-mini", "name": "OpenAI o4-mini", "publisher": "OpenAI", "summary": "o4-mini includes significant improvements on quality and safety while supporting the existing features of o3-mini and delivering comparable or better performance.", "rate_limit_tier": "custom", "supported_input_modalities": ["text", "image"], "supported_output_modalities": ["text"], "tags": ["multipurpose", "multilingual", "multimodal"]}, {"id": "openai/text-embedding-3-large", "name": "OpenAI Text Embedding 3 (large)", "publisher": "OpenAI", "summary": "Text-embedding-3 series models are the latest and most capable embedding model from OpenAI.", "rate_limit_tier": "embeddings", "supported_input_modalities": ["text"], "supported_output_modalities": ["embeddings"], "tags": ["rag"]}, {"id": "openai/text-embedding-3-small", "name": "OpenAI Text Embedding 3 (small)", "publisher": "OpenAI", "summary": "Text-embedding-3 series models are the latest and most capable embedding model from OpenAI.", "rate_limit_tier": "embeddings", "supported_input_modalities": ["text"], "supported_output_modalities": ["embeddings"], "tags": ["rag"]}, {"id": "ai21-labs/AI21-Jamba-1.5-Large", "name": "AI21 Jamba 1.5 Large", "publisher": "AI21 Labs", "summary": "A 398B parameters (94B active) multilingual model, offering a 256K long context window, function calling, structured output, and grounded generation.", "rate_limit_tier": "high", "supported_input_modalities": ["text"], "supported_output_modalities": ["text"], "tags": ["rag", "multilingual", "large context"]}, {"id": "ai21-labs/AI21-Jamba-1.5-Mini", "name": "AI21 Jamba 1.5 Mini", "publisher": "AI21 Labs", "summary": "A 52B parameters (12B active) multilingual model, offering a 256K long context window, function calling, structured output, and grounded generation.", "rate_limit_tier": "low", "supported_input_modalities": ["text"], "supported_output_modalities": ["text"], "tags": ["rag", "multilingual", "large context"]}, {"id": "cohere/cohere-command-a", "name": "Cohere Command A", "publisher": "Cohere", "summary": "Command A is a highly efficient generative model that excels at agentic and multilingual use cases.", "rate_limit_tier": "low", "supported_input_modalities": ["text"], "supported_output_modalities": ["text"], "tags": ["rag", " multilingual"]}, {"id": "cohere/Cohere-command-r", "name": "Cohere Command R", "publisher": "Cohere", "summary": "Command R is a scalable generative model targeting RAG and Tool Use to enable production-scale AI for enterprise.", "rate_limit_tier": "low", "supported_input_modalities": ["text"], "supported_output_modalities": ["text"], "tags": ["rag", "multilingual"]}, {"id": "cohere/Cohere-command-r-08-2024", "name": "Cohere Command R 08-2024", "publisher": "Cohere", "summary": "Command R is a scalable generative model targeting RAG and Tool Use to enable production-scale AI for enterprise.", "rate_limit_tier": "low", "supported_input_modalities": ["text"], "supported_output_modalities": ["text"], "tags": ["rag", "multilingual"]}, {"id": "cohere/Cohere-command-r-plus", "name": "Cohere Command R+", "publisher": "Cohere", "summary": "Command R+ is a state-of-the-art RAG-optimized model designed to tackle enterprise-grade workloads.", "rate_limit_tier": "high", "supported_input_modalities": ["text"], "supported_output_modalities": ["text"], "tags": ["rag", "multilingual"]}, {"id": "cohere/Cohere-command-r-plus-08-2024", "name": "Cohere Command R+ 08-2024", "publisher": "Cohere", "summary": "Command R+ is a state-of-the-art RAG-optimized model designed to tackle enterprise-grade workloads.", "rate_limit_tier": "high", "supported_input_modalities": ["text"], "supported_output_modalities": ["text"], "tags": ["rag", "multilingual"]}, {"id": "cohere/Cohere-embed-v3-english", "name": "Cohere Embed v3 English", "publisher": "Cohere", "summary": "Cohere Embed English is the market's leading text representation model used for semantic search, retrieval-augmented generation (RAG), classification, and clustering.", "rate_limit_tier": "embeddings", "supported_input_modalities": ["text"], "supported_output_modalities": ["embeddings"], "tags": ["rag"]}, {"id": "cohere/Cohere-embed-v3-multilingual", "name": "Cohere Embed v3 Multilingual", "publisher": "Cohere", "summary": "Cohere Embed Multilingual is the market's leading text representation model used for semantic search, retrieval-augmented generation (RAG), classification, and clustering.", "rate_limit_tier": "embeddings", "supported_input_modalities": ["text"], "supported_output_modalities": ["embeddings"], "tags": ["rag"]}, {"id": "core42/jais-30b-chat", "name": "JAIS 30b Chat", "publisher": "Core42", "summary": "JAIS 30b Chat is an auto-regressive bilingual LLM for Arabic & English with state-of-the-art capabilities in Arabic.", "rate_limit_tier": "low", "supported_input_modalities": ["text"], "supported_output_modalities": ["text"], "tags": ["conversation", "multilingual", "rag"]}, {"id": "deepseek/DeepSeek-R1", "name": "DeepSeek-R1", "publisher": "DeepSeek", "summary": "DeepSeek-R1 excels at reasoning tasks using a step-by-step training process, such as language, scientific reasoning, and coding tasks.", "rate_limit_tier": "custom", "supported_input_modalities": ["text"], "supported_output_modalities": ["text"], "tags": ["reasoning", "coding", "agents"]}, {"id": "deepseek/DeepSeek-V3-0324", "name": "DeepSeek-V3-0324", "publisher": "DeepSeek", "summary": "DeepSeek-V3-0324 demonstrates notable improvements over its predecessor, DeepSeek-V3, in several key aspects, including enhanced reasoning, improved function calling, and superior code generation capabilities.", "rate_limit_tier": "high", "supported_input_modalities": ["text"], "supported_output_modalities": ["text"], "tags": ["coding", "agents"]}, {"id": "meta/Llama-3.2-11B-Vision-Instruct", "name": "Llama-3.2-11B-Vision-Instruct", "publisher": "Meta", "summary": "Excels in image reasoning capabilities on high-res images for visual understanding apps.", "rate_limit_tier": "low", "supported_input_modalities": ["text", "image", "audio"], "supported_output_modalities": ["text"], "tags": ["multimodal", "reasoning", "conversation"]}, {"id": "meta/Llama-3.2-90B-Vision-Instruct", "name": "Llama-3.2-90B-Vision-Instruct", "publisher": "Meta", "summary": "Advanced image reasoning capabilities for visual understanding agentic apps.", "rate_limit_tier": "high", "supported_input_modalities": ["text", "image", "audio"], "supported_output_modalities": ["text"], "tags": ["multimodal", "reasoning", "conversation"]}, {"id": "meta/Llama-3.3-70B-Instruct", "name": "Llama-3.3-70B-Instruct", "publisher": "Meta", "summary": "Llama 3.3 70B Instruct offers enhanced reasoning, math, and instruction following with performance comparable to Llama 3.1 405B.", "rate_limit_tier": "high", "supported_input_modalities": ["text"], "supported_output_modalities": ["text"], "tags": ["conversation"]}, {"id": "meta/Llama-4-Maverick-17B-128E-Instruct-FP8", "name": "Llama 4 Maverick 17B 128E Instruct FP8", "publisher": "Meta", "summary": "Llama 4 Maverick 17B 128E Instruct FP8 is great at precise image understanding and creative writing, offering high quality at a lower price compared to Llama 3.3 70B", "rate_limit_tier": "high", "supported_input_modalities": ["text", "image"], "supported_output_modalities": ["text"], "tags": ["multimodal", "conversation", "multilingual"]}, {"id": "meta/Llama-4-Scout-17B-16E-Instruct", "name": "Llama 4 Scout 17B 16E Instruct", "publisher": "Meta", "summary": "Llama 4 Scout 17B 16E Instruct is great at multi-document summarization, parsing extensive user activity for personalized tasks, and reasoning over vast codebases.", "rate_limit_tier": "high", "supported_input_modalities": ["text", "image"], "supported_output_modalities": ["text"], "tags": ["multimodal", "conversation", "multilingual"]}, {"id": "meta/Meta-Llama-3.1-405B-Instruct", "name": "Meta-Llama-3.1-405B-Instruct", "publisher": "Meta", "summary": "The Llama 3.1 instruction tuned text only models are optimized for multilingual dialogue use cases and outperform many of the available open source and closed chat models on common industry benchmarks.", "rate_limit_tier": "high", "supported_input_modalities": ["text"], "supported_output_modalities": ["text"], "tags": ["conversation"]}, {"id": "meta/Meta-Llama-3.1-70B-Instruct", "name": "Meta-Llama-3.1-70B-Instruct", "publisher": "Meta", "summary": "The Llama 3.1 instruction tuned text only models are optimized for multilingual dialogue use cases and outperform many of the available open source and closed chat models on common industry benchmarks.", "rate_limit_tier": "high", "supported_input_modalities": ["text"], "supported_output_modalities": ["text"], "tags": ["conversation"]}, {"id": "meta/Meta-Llama-3.1-8B-Instruct", "name": "Meta-Llama-3.1-8B-Instruct", "publisher": "Meta", "summary": "The Llama 3.1 instruction tuned text only models are optimized for multilingual dialogue use cases and outperform many of the available open source and closed chat models on common industry benchmarks.", "rate_limit_tier": "low", "supported_input_modalities": ["text"], "supported_output_modalities": ["text"], "tags": ["conversation"]}, {"id": "meta/Meta-Llama-3-70B-Instruct", "name": "Meta-Llama-3-70B-Instruct", "publisher": "Meta", "summary": "A powerful 70-billion parameter model excelling in reasoning, coding, and broad language applications.", "rate_limit_tier": "high", "supported_input_modalities": ["text"], "supported_output_modalities": ["text"], "tags": ["conversation"]}, {"id": "meta/Meta-Llama-3-8B-Instruct", "name": "Meta-Llama-3-8B-Instruct", "publisher": "Meta", "summary": "A versatile 8-billion parameter model optimized for dialogue and text generation tasks.", "rate_limit_tier": "low", "supported_input_modalities": ["text"], "supported_output_modalities": ["text"], "tags": ["conversation"]}, {"id": "mistral-ai/Codestral-2501", "name": "Codestral 25.01", "publisher": "Mistral AI", "summary": "Codestral 25.01 by Mistral AI is designed for code generation, supporting 80+ programming languages, and optimized for tasks like code completion and fill-in-the-middle", "rate_limit_tier": "low", "supported_input_modalities": ["text"], "supported_output_modalities": ["text"], "tags": ["reasoning", "coding"]}, {"id": "mistral-ai/Ministral-3B", "name": "Ministral 3B", "publisher": "Mistral AI", "summary": "Ministral 3B is a state-of-the-art Small Language Model (SLM) optimized for edge computing and on-device applications. As it is designed for low-latency and compute-efficient inference, it it also the perfect model for standard GenAI applications that have", "rate_limit_tier": "low", "supported_input_modalities": ["text"], "supported_output_modalities": ["text"], "tags": ["low latency", "agents", "reasoning"]}, {"id": "mistral-ai/Mistral-Large-2411", "name": "Mistral Large 24.11", "publisher": "Mistral AI", "summary": "Mistral Large 24.11 offers enhanced system prompts, advanced reasoning and function calling capabilities.", "rate_limit_tier": "high", "supported_input_modalities": ["text"], "supported_output_modalities": ["text"], "tags": ["reasoning", "rag", "agents"]}, {"id": "mistral-ai/mistral-medium-2505", "name": "Mistral Medium 3 (25.05)", "publisher": "Mistral AI", "summary": "Mistral Medium 3 is an advanced Large Language Model (LLM) with state-of-the-art reasoning, knowledge, coding and vision capabilities.", "rate_limit_tier": "low", "supported_input_modalities": ["text", "image"], "supported_output_modalities": ["text"], "tags": ["multipurpose", "vision", "multimodal"]}, {"id": "mistral-ai/Mistral-<PERSON><PERSON>o", "name": "Mi<PERSON><PERSON> Nemo", "publisher": "Mistral AI", "summary": "Mistral Nemo is a cutting-edge Language Model (LLM) boasting state-of-the-art reasoning, world knowledge, and coding capabilities within its size category.", "rate_limit_tier": "low", "supported_input_modalities": ["text"], "supported_output_modalities": ["text"], "tags": ["reasoning", "rag", "agents"]}, {"id": "mistral-ai/mistral-small-2503", "name": "Mistral Small 3.1", "publisher": "Mistral AI", "summary": "Enhanced Mistral Small 3 with multimodal capabilities and a 128k context length.", "rate_limit_tier": "low", "supported_input_modalities": ["text", "image"], "supported_output_modalities": ["text"], "tags": ["multipurpose", "vision", "multimodal"]}, {"id": "xai/grok-3", "name": "Grok 3", "publisher": "xAI", "summary": "Grok 3 is xAI's debut model, pretrained by Colossus at supermassive scale to excel in specialized domains like finance, healthcare, and the law.", "rate_limit_tier": "custom", "supported_input_modalities": ["text"], "supported_output_modalities": ["text"], "tags": ["understanding", "instruction", "summarization"]}, {"id": "xai/grok-3-mini", "name": "Grok 3 Mini", "publisher": "xAI", "summary": "Grok 3 Mini is a lightweight model that thinks before responding. Trained on mathematic and scientific problems, it is great for logic-based tasks.", "rate_limit_tier": "custom", "supported_input_modalities": ["text"], "supported_output_modalities": ["text"], "tags": ["agents", "reasoning", "coding"]}, {"id": "microsoft/MAI-DS-R1", "name": "MAI-DS-R1", "publisher": "Microsoft", "summary": "MAI-DS-R1 is a DeepSeek-R1 reasoning model that has been post-trained by the Microsoft AI team to fill in information gaps in the previous version of the model and improve its harm protections while maintaining R1 reasoning capabilities.", "rate_limit_tier": "custom", "supported_input_modalities": ["text"], "supported_output_modalities": ["text"], "tags": ["reasoning", "coding", "agents"]}, {"id": "microsoft/Phi-3.5-mini-instruct", "name": "Phi-3.5-mini instruct (128k)", "publisher": "Microsoft", "summary": "Refresh of Phi-3-mini model.", "rate_limit_tier": "low", "supported_input_modalities": ["text"], "supported_output_modalities": ["text"], "tags": ["reasoning", "understanding", "low latency"]}, {"id": "microsoft/Phi-3.5-Mo<PERSON>-instruct", "name": "Phi-3.5-<PERSON><PERSON> instruct (128k)", "publisher": "Microsoft", "summary": "A new mixture of experts model", "rate_limit_tier": "low", "supported_input_modalities": ["text"], "supported_output_modalities": ["text"], "tags": ["reasoning", "understanding", "low latency"]}, {"id": "microsoft/Phi-3.5-vision-instruct", "name": "Phi-3.5-vision instruct (128k)", "publisher": "Microsoft", "summary": "Refresh of Phi-3-vision model.", "rate_limit_tier": "low", "supported_input_modalities": ["text", "image"], "supported_output_modalities": [], "tags": ["multimodal", "reasoning", "low latency"]}, {"id": "microsoft/Phi-3-medium-128k-instruct", "name": "Phi-3-medium instruct (128k)", "publisher": "Microsoft", "summary": "Same Phi-3-medium model, but with a larger context size for RAG or few shot prompting.", "rate_limit_tier": "low", "supported_input_modalities": ["text"], "supported_output_modalities": ["text"], "tags": ["reasoning", "understanding", "large context"]}, {"id": "microsoft/Phi-3-medium-4k-instruct", "name": "Phi-3-medium instruct (4k)", "publisher": "Microsoft", "summary": "A 14B parameters model, proves better quality than Phi-3-mini, with a focus on high-quality, reasoning-dense data.", "rate_limit_tier": "low", "supported_input_modalities": ["text"], "supported_output_modalities": ["text"], "tags": ["reasoning", "understanding"]}, {"id": "microsoft/Phi-3-mini-128k-instruct", "name": "Phi-3-mini instruct (128k)", "publisher": "Microsoft", "summary": "Same Phi-3-mini model, but with a larger context size for RAG or few shot prompting.", "rate_limit_tier": "low", "supported_input_modalities": ["text"], "supported_output_modalities": ["text"], "tags": ["reasoning", "understanding", "low latency"]}, {"id": "microsoft/Phi-3-mini-4k-instruct", "name": "Phi-3-mini instruct (4k)", "publisher": "Microsoft", "summary": "Tiniest member of the Phi-3 family. Optimized for both quality and low latency.", "rate_limit_tier": "low", "supported_input_modalities": ["text"], "supported_output_modalities": ["text"], "tags": ["reasoning", "understanding", "low latency"]}, {"id": "microsoft/Phi-3-small-128k-instruct", "name": "Phi-3-small instruct (128k)", "publisher": "Microsoft", "summary": "Same Phi-3-small model, but with a larger context size for RAG or few shot prompting.", "rate_limit_tier": "low", "supported_input_modalities": ["text"], "supported_output_modalities": ["text"], "tags": ["reasoning", "understanding", "large context"]}, {"id": "microsoft/Phi-3-small-8k-instruct", "name": "Phi-3-small instruct (8k)", "publisher": "Microsoft", "summary": "A 7B parameters model, proves better quality than Phi-3-mini, with a focus on high-quality, reasoning-dense data.", "rate_limit_tier": "low", "supported_input_modalities": ["text"], "supported_output_modalities": ["text"], "tags": ["reasoning", "understanding"]}, {"id": "microsoft/Phi-4", "name": "Phi-4", "publisher": "Microsoft", "summary": "Phi-4 14B, a highly capable model for low latency scenarios.", "rate_limit_tier": "low", "supported_input_modalities": ["text"], "supported_output_modalities": ["text"], "tags": ["reasoning", "understanding", "low latency"]}, {"id": "microsoft/Phi-4-mini-instruct", "name": "Phi-4-mini-instruct", "publisher": "Microsoft", "summary": "3.8B parameters Small Language Model outperforming larger models in reasoning, math, coding, and function-calling", "rate_limit_tier": "low", "supported_input_modalities": ["text"], "supported_output_modalities": ["text"], "tags": ["reasoning", "agents", "multilingual"]}, {"id": "microsoft/Phi-4-mini-reasoning", "name": "Phi-4-mini-reasoning", "publisher": "Microsoft", "summary": "Lightweight math reasoning model optimized for multi-step problem solving", "rate_limit_tier": "low", "supported_input_modalities": ["text"], "supported_output_modalities": ["text"], "tags": ["reasoning", " large context", " low latency"]}, {"id": "microsoft/Phi-4-multimodal-instruct", "name": "Phi-4-multimodal-instruct", "publisher": "Microsoft", "summary": "First small multimodal model to have 3 modality inputs (text, audio, image), excelling in quality and efficiency", "rate_limit_tier": "low", "supported_input_modalities": ["audio", "image", "text"], "supported_output_modalities": ["text"], "tags": ["vision", " audio", " summarization"]}, {"id": "microsoft/Phi-4-reasoning", "name": "Phi-4-<PERSON><PERSON>", "publisher": "Microsoft", "summary": "State-of-the-art open-weight reasoning model.", "rate_limit_tier": "low", "supported_input_modalities": ["text"], "supported_output_modalities": ["text"], "tags": ["reasoning", " large context", " low latency"]}]