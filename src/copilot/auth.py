import json
import logging
import os
import time
import webbrowser
from typing import Any, Dict, Optional, Union, Tuple # Import Union and Tuple

import requests
import httpx

from .constants import (
    COPILOT_OAUTH_CLIENT_ID,
    COPILOT_AUTH_URL,
    GITHUB_ACCESS_TOKEN_URL,
    GITHUB_DEVICE_CODE_URL,
    GITHUB_OAUTH_TOKEN_FILE,
)
from .models import ApiToken, ApiTokenResponse

logger = logging.getLogger(__name__)


def _find_config_path() -> Optional[str]:
    """Find the configuration path for GitHub Copilot tokens."""
    # Check environment variable first
    if os.getenv("CODECOMPANION_TOKEN_PATH"):
        return os.getenv("CODECOMPANION_TOKEN_PATH")
    
    # Check XDG_CONFIG_HOME
    config = os.getenv("XDG_CONFIG_HOME")
    if config and os.path.isdir(config):
        return config
    
    # Windows specific path
    if os.name == 'nt':  # Windows
        config = os.path.expanduser("~/AppData/Local")
        if os.path.isdir(config):
            return config
    
    # Default Unix-like path
    config = os.path.expanduser("~/.config")
    if os.path.isdir(config):
        return config
    
    return None


def _get_github_token_from_copilot_files() -> Optional[str]:
    """Get GitHub OAuth token from GitHub Copilot configuration files (similar to Lua implementation)."""
    config_path = _find_config_path()
    if not config_path:
        return None
    
    # Check for GitHub token in environment (for Codespaces)
    token = os.getenv("GITHUB_TOKEN")
    codespaces = os.getenv("CODESPACES")
    if token and codespaces:
        logger.info("Found GitHub token in Codespaces environment")
        return token
    
    # File paths to check (similar to Lua implementation)
    file_paths = [
        os.path.join(config_path, "github-copilot", "hosts.json"),
        os.path.join(config_path, "github-copilot", "apps.json"),
    ]
    
    for file_path in file_paths:
        if os.path.isfile(file_path):
            try:
                logger.debug(f"Checking for token in {file_path}")
                with open(file_path, 'r') as f:
                    userdata = json.load(f)
                
                # Look for github.com entries
                for key, value in userdata.items():
                    if "github.com" in key and isinstance(value, dict):
                        oauth_token = value.get("oauth_token")
                        if oauth_token:
                            logger.info(f"Found GitHub OAuth token in {file_path}")
                            return oauth_token
            except (json.JSONDecodeError, IOError) as e:
                logger.debug(f"Could not read {file_path}: {e}")
                continue
    
    return None


def _load_github_oauth_token_from_file_static() -> Optional[str]:
    """Load GitHub OAuth token from file (static class method)."""
    # First try the enhanced discovery method
    token = _get_github_token_from_copilot_files()
    if token:
        return token
    
    # Fall back to the original method
    token_filepath = os.path.expanduser(f"~/{GITHUB_OAUTH_TOKEN_FILE}")
    try:
        if os.path.exists(token_filepath):
            with open(token_filepath, "r") as file:
                data = json.load(file)
                token = data.get("token")
                if token:
                    return token
    except Exception as e:
        logging.error(f"Error loading GitHub OAuth token from {token_filepath}: {e}")
    return None


def _save_github_oauth_token_to_file(token: str) -> None:
    """Save GitHub OAuth token to file."""
    token_filepath = os.path.expanduser(f"~/{GITHUB_OAUTH_TOKEN_FILE}")
    try:
        with open(token_filepath, "w") as file:
            json.dump({"token": token, "saved_at": time.time()}, file)
        os.chmod(token_filepath, 0o600)  # Restrict permissions for security
        logging.debug(f"Successfully saved GitHub OAuth token to {token_filepath}")
    except Exception as e:
        logging.error(f"Error saving GitHub OAuth token to {token_filepath}: {e}")


def _request_device_and_user_codes(request_timeout: Optional[Union[float, Tuple[float, float]]]) -> Optional[Dict[str, Any]]:
    """Requests device and user codes from GitHub to initiate the device flow."""
    payload = {"client_id": COPILOT_OAUTH_CLIENT_ID, "scope": "read:user"}
    headers = {
        "Accept": "application/json",
        "accept-encoding": "gzip,deflate,br",
    }
    try:
        logger.info(f"Requesting device and user codes from {GITHUB_DEVICE_CODE_URL}")
        response = requests.post(GITHUB_DEVICE_CODE_URL, data=payload, headers=headers, timeout=request_timeout)
        response.raise_for_status()
        data = response.json()
        if "error" in data:
            logger.error(f"Error in device code response: {data.get('error_description', data.get('error'))}")
            return None
        return data
    except requests.exceptions.RequestException as e:
        logger.error(f"Error requesting GitHub device code: {e}")
        return None


def _poll_for_access_token(
    device_code: str, interval: int, expires_in: int, request_timeout: Optional[Union[float, Tuple[float, float]]]
) -> Optional[str]:
    """Polls GitHub for the access token after the user authorizes the device."""
    payload = {
        "client_id": COPILOT_OAUTH_CLIENT_ID,
        "device_code": device_code,
        "grant_type": "urn:ietf:params:oauth:grant-type:device_code",
    }
    headers = {
        "Accept": "application/json",
        "accept-encoding": "gzip,deflate,br",
    }

    polling_start_time = time.time()
    max_polling_duration = expires_in - min(30, interval)
    if max_polling_duration <= 0:
        max_polling_duration = expires_in

    logger.info(
        f"Polling for access token. Interval: {interval}s. Device code expires in: {expires_in}s. Max poll duration: {max_polling_duration}s."
    )

    while True:
        current_time = time.time()
        if (current_time - polling_start_time) > max_polling_duration:
            logger.error("Timeout waiting for GitHub device authorization.")
            return None

        time.sleep(interval)
        try:
            response = requests.post(GITHUB_ACCESS_TOKEN_URL, data=payload, headers=headers, timeout=request_timeout)
            logger.debug(f"Polling response status: {response.status_code}")
            logger.debug(f"Polling response headers: {dict(response.headers)}")
            logger.debug(f"Polling response text: {response.text}")
            
            try:
                data = response.json()
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse JSON response: {e}. Response text: {response.text}")
                continue

            error = data.get("error")
            if error:
                if error == "authorization_pending":
                    logger.info("Authorization pending from user...")
                elif error == "slow_down":
                    new_interval = data.get("interval", interval + 5)
                    logger.warning(f"GitHub requested to slow down. Increasing polling interval to {new_interval}s.")
                    interval = new_interval
                elif error == "access_denied":
                    logger.error("GitHub device authorization was denied by the user.")
                    return None
                elif error == "expired_token":
                    logger.error("GitHub device code has expired.")
                    return None
                else:
                    logger.error(f"GitHub returned an error during token polling: {data.get('error_description', error)}")
                    if response.status_code == 400:
                        return None
                    return None
            elif "access_token" in data:
                logger.info("Successfully obtained GitHub access token.")
                return data.get("access_token")
            else:
                logger.warning(f"Unexpected response during token polling (status {response.status_code}): {data}")
                if response.status_code >= 400:
                    logger.error(f"Polling failed with status {response.status_code} and unexpected data: {data}")
                    return None

        except requests.exceptions.RequestException as e:
            logger.error(f"Request error during GitHub token polling: {e}")
            return None


def perform_device_auth_flow(request_timeout: Optional[Union[float, Tuple[float, float]]]) -> Optional[str]:
    """Orchestrates the GitHub OAuth device flow to obtain an access token. Synchronous."""
    logger.info("Starting GitHub OAuth device authentication flow for Copilot.")
    device_code_info = _request_device_and_user_codes(request_timeout)

    if not device_code_info or not all(
        k in device_code_info for k in ["device_code", "user_code", "verification_uri", "interval", "expires_in"]
    ):
        logger.error("Failed to get necessary information for device flow. Aborting device auth.")
        return None

    device_code = device_code_info["device_code"]
    user_code = device_code_info["user_code"]
    verification_uri = device_code_info["verification_uri"]
    interval = int(device_code_info.get("interval", 5))
    expires_in = int(device_code_info.get("expires_in", 900))

    print("-" * 70)
    print("GitHub Copilot Authentication Required")
    print(f"1. Please open your web browser and go to: {verification_uri}")
    print(f"2. When prompted, enter the following code: {user_code}")
    print("-" * 70)
    
    try:
        webbrowser.open(verification_uri)
        logger.info(f"Attempted to open {verification_uri} in a web browser.")
    except Exception as e:
        logger.warning(f"Could not automatically open web browser: {e}. Please open manually.")
    
    print(f"Waiting for you to authorize... This request will expire in approximately {expires_in // 60} minutes.")
    print("Press Enter after you have completed the authorization in your browser...")
    input()  # Wait for user to complete authorization
    
    access_token = _poll_for_access_token(device_code, interval, expires_in, request_timeout)

    if access_token:
        _save_github_oauth_token_to_file(access_token)
        logger.info("Successfully obtained and saved GitHub OAuth token via device flow.")
        return access_token
    else:
        logger.error("Failed to obtain GitHub OAuth token via device flow.")
        return None


def get_copilot_api_token(
    github_oauth_token: str,
    request_timeout: Optional[Union[float, Tuple[float, float]]],
    editor_version: str,
    copilot_integration_id: str,
    sync_client: requests.Session,
) -> Optional[ApiToken]:
    """
    Retrieves the Copilot API key.
    If the key is missing or expired, attempts to fetch/refresh it. (Synchronous)
    """
    headers = {
        "Authorization": f"Bearer {github_oauth_token}",  # Use Bearer instead of token
        "Accept": "application/json",
        "Editor-Version": editor_version,
        "Copilot-Integration-Id": copilot_integration_id,
        "User-Agent": "GitHubCopilotChat/0.28.0",
    }
    try:
        response = sync_client.get(COPILOT_AUTH_URL, headers=headers, timeout=request_timeout)
        response.raise_for_status()
        api_token_response = ApiTokenResponse.model_validate(response.json())
        api_token = ApiToken.from_response(api_token_response)
        logger.info(f"Successfully obtained new Copilot API Token. Expires at: {api_token.expires_at}")
        return api_token
    except requests.exceptions.RequestException as e:
        logger.error(f"Error requesting Copilot API token (sync): {e}")
        if e.response is not None:
            logger.error(f"Response content from auth endpoint: {e.response.text}")
        return None
    except Exception as e:
        logger.error(f"Failed to parse API token response or unexpected error: {e}. Raw: {response.text if 'response' in locals() else 'N/A'}")
        return None


async def aget_copilot_api_token(
    github_oauth_token: str,
    request_timeout: Optional[Union[float, Tuple[float, float]]],
    editor_version: str,
    copilot_integration_id: str,
    async_client: httpx.AsyncClient,
) -> Optional[ApiToken]:
    """
    Retrieves the Copilot API key asynchronously.
    """
    headers = {
        "Authorization": f"Bearer {github_oauth_token}",  # Use Bearer instead of token
        "Accept": "application/json",
        "Editor-Version": editor_version,
        "Copilot-Integration-Id": copilot_integration_id,
        "User-Agent": "GitHubCopilotChat/0.28.0",
    }
    try:
        response = await async_client.get(COPILOT_AUTH_URL, headers=headers, timeout=request_timeout)
        response.raise_for_status()
        api_token_response = ApiTokenResponse.model_validate(response.json())
        api_token = ApiToken.from_response(api_token_response)
        logger.info(f"Successfully obtained new Copilot API Key (async). Expires at: {api_token.expires_at}")
        return api_token
    except httpx.RequestError as e:
        logger.error(f"HTTP error while fetching models asynchronously: {e}")
        if hasattr(e, "response") and e.response is not None:
            logger.error(f"Response content: {e.response.text}")
        return None
    except Exception as e:
        logger.error(f"Failed to parse API token response (async) or unexpected error: {e}. Raw: {response.text if 'response' in locals() else 'N/A'}")
        return None

