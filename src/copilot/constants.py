# --- GitHub OAuth Device Flow Constants ---
COPILOT_OAUTH_CLIENT_ID = "01ab8ac9400c4e429b23"
GITHUB_DEVICE_CODE_URL = "https://github.com/login/device/code"
GITHUB_ACCESS_TOKEN_URL = "https://github.com/login/oauth/access_token"

# Environment variable for the GitHub OAuth token
COPILOT_OAUTH_TOKEN_ENV_VAR = "GITHUB_COPILOT_OAUTH_TOKEN"
# Endpoint to exchange GitHub OAuth token for Copilot API token
COPILOT_AUTH_URL = "https://api.github.com/copilot_internal/v2/token"
# Endpoint for Copilot chat completions
COPILOT_CHAT_URL = "https://api.githubcopilot.com/chat/completions"
# Endpoint for listing Copilot models
COPILOT_MODELS_URL = "https://api.githubcopilot.com/models"
# File to cache the GitHub OAuth token
GITHUB_OAUTH_TOKEN_FILE = ".github_copilot_oauth_token.json"

# Copilot's base model; defined by Microsoft in premium requests table
# This will be moved to the front of the Copilot model list, and will be used for
# 'fast' requests (e.g. title generation)
# https://docs.github.com/en/copilot/managing-copilot/monitoring-usage-and-entitlements/about-premium-requests
DEFAULT_MODEL_ID = "gpt-4.1"

