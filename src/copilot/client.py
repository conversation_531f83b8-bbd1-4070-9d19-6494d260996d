import json
import logging
import os
from datetime import datetime, timezone
from typing import Any, Dict, Generator, List, Optional, Tuple, Union
import re

import httpx
import requests
from pydantic import PrivateAttr, SecretStr, Field, ValidationError, model_validator

from langchain_core.callbacks.manager import (
    AsyncCallbackManagerForLLMRun,
    CallbackManagerForLLMRun,
)
from langchain_core.language_models.chat_models import BaseChatModel
from langchain_core.messages import (
    AIMessage, 
    AIMessageChunk, 
    BaseMessage, 
    HumanMessage, 
    SystemMessage, 
    ToolMessage,
    ToolCall as LCToolCall,
    ToolCallChunk as LCToolCallChunk,
)
from langchain_core.outputs import ChatGeneration, ChatGenerationChunk, ChatResult
from langchain_core.stores import InMemoryBaseStore

from .auth import (
    _load_github_oauth_token_from_file_static,
    perform_device_auth_flow,
    get_copilot_api_token,
    aget_copilot_api_token,
)
from .constants import (
    COPILOT_CHAT_URL,
    COPILOT_MODELS_URL,
    COPILOT_OAUTH_CLIENT_ID,
    COPILOT_OAUTH_TOKEN_ENV_VAR,
    DEFAULT_MODEL_ID,
    GITHUB_OAUTH_TOKEN_FILE,
)
from .models import (
    ApiToken,
    ChatMessage,
    ChatMessagePart,
    FunctionContent,
    ImageUrl,
    Model,
    ModelSchema,
    Request,
    ResponseEvent,
    Role,
    Tool,
    ToolCall,
    ToolCallChunk,
    ToolChoice,
    ToolFunction,
)

logger = logging.getLogger(__name__)
# Ensure logger is configured to show debug messages
# logging.basicConfig(level=logging.DEBUG) # Add this line if logs aren't showing


class CopilotChat(BaseChatModel):
    """
    A Langchain ChatModel wrapper for GitHub Copilot Chat API.

    Handles authentication with GitHub Copilot using a GitHub OAuth token,
    manages API token refresh, and sends chat completion requests.

    To use, you need a GitHub account with an active Copilot subscription.
    The GitHub OAuth token can be provided via the `GITHUB_COPILOT_OAUTH_TOKEN`
    environment variable. If not found, it will attempt to load from
    `.github_copilot_oauth_token.json` or initiate a device authentication flow.

    Example:
        ```python
        from src.copilot.client import CopilotChat # Adjust import path as needed
        from langchain_core.messages import HumanMessage

        # Ensure GITHUB_COPILOT_OAUTH_TOKEN is set in your environment,
        # or run the script once to perform device auth.
        copilot_llm = CopilotChat(model_name="gpt-4o") # Use gpt-4o for Copilot API
        response = copilot_llm.invoke([HumanMessage(content="Hello Copilot!")])
        print(response.content)
        ```
    """

    model_name: str = Field(default="gpt-4o", alias="model")
    """Model name to use e.g. gpt-4, gpt-3.5-turbo.
       The actual model used by Copilot backend might vary."""

    temperature: float = 0.5
    top_p: Optional[float] = None
    max_tokens: Optional[int] = None
    request_timeout: Union[float, Tuple[float, float], None] = Field(
        default=60, description="Timeout for requests to Copilot API."
    )

    _github_oauth_token: Optional[SecretStr] = PrivateAttr(default=None)
    _copilot_api_token: Optional[ApiToken] = PrivateAttr(default=None)
    _models: Optional[List[Model]] = PrivateAttr(default=None)  # Cached models

    # Headers for Copilot API, matching working implementations
    editor_version: str = "VSCode/1.85.1"  # More standard format
    copilot_integration_id: str = "vscode-chat"
    user_agent: str = "GitHubCopilotChat/0.28.0"

    _sync_client: requests.Session = PrivateAttr(default_factory=requests.Session)
    _async_client: httpx.AsyncClient = PrivateAttr(default_factory=httpx.AsyncClient)

    model_config = {
        "populate_by_name": True,
        "arbitrary_types_allowed": True,
    }

    @model_validator(mode="before")
    @classmethod
    def validate_environment(cls, values: Dict[str, Any]) -> Dict[str, Any]:
        """Validate that environment variables are set if needed."""
        github_oauth_token = values.get("_github_oauth_token") or os.getenv(COPILOT_OAUTH_TOKEN_ENV_VAR)

        if github_oauth_token:
            values["_github_oauth_token"] = SecretStr(github_oauth_token)
        else:
            loaded_token_str = _load_github_oauth_token_from_file_static()
            if loaded_token_str:
                values["_github_oauth_token"] = SecretStr(loaded_token_str)
                logger.debug(f"Successfully loaded GitHub OAuth token from {GITHUB_OAUTH_TOKEN_FILE}.")

        return values

    def __init__(self, **data: Any):
        super().__init__(**data)

        github_oauth_token = data.get("_github_oauth_token") or os.getenv(COPILOT_OAUTH_TOKEN_ENV_VAR)

        if github_oauth_token:
            self._github_oauth_token = (
                SecretStr(github_oauth_token) if isinstance(github_oauth_token, str) else github_oauth_token
            )
        else:
            loaded_token_str = _load_github_oauth_token_from_file_static()
            if loaded_token_str:
                self._github_oauth_token = SecretStr(loaded_token_str)
                logger.debug(f"Successfully set GitHub OAuth token from {GITHUB_OAUTH_TOKEN_FILE}.")
            else:
                self._github_oauth_token = None

        if not self._github_oauth_token:
            logger.info(
                f"GitHub Copilot OAuth token not found in environment variable "
                f"({COPILOT_OAUTH_TOKEN_ENV_VAR}) or file ({GITHUB_OAUTH_TOKEN_FILE}). "
                f"Device authentication flow may be initiated on first API call if "
                f"COPILOT_OAUTH_CLIENT_ID is correctly set and not a placeholder."
            )
        elif COPILOT_OAUTH_CLIENT_ID == "YOUR_GITHUB_OAUTH_APP_CLIENT_ID_HERE" and not self._github_oauth_token:
            logger.warning(
                f"GitHub Copilot OAuth token not found and Client ID ({COPILOT_OAUTH_CLIENT_ID}) "
                f"is a placeholder. Device flow cannot be initiated."
            )

    @property
    def _llm_type(self) -> str:
        return "copilot-chat"

    @property
    def _identifying_params(self) -> Dict[str, Any]:
        return {
            "model_name": self.model_name,
            "temperature": self.temperature,
            "top_p": self.top_p,
            "max_tokens": self.max_tokens,
            "request_timeout": self.request_timeout,
            "editor_version": self.editor_version,
            "copilot_integration_id": self.copilot_integration_id,
        }

    def _is_claude_model(self) -> bool:
        """Check if the current model is a Claude model."""
        return "claude" in self.model_name.lower()
    
    def _is_openai_model(self) -> bool:
        """Check if the current model is an OpenAI model."""
        return any(model_prefix in self.model_name.lower() for model_prefix in ["gpt", "o1"])
    
    def _convert_tool_to_anthropic_format(self, openai_tool: Tool) -> Dict[str, Any]:
        """Convert an OpenAI-format tool to Anthropic format.
        
        OpenAI format:
        {
          "type": "function",
          "function": {
            "name": "tool_name",
            "description": "description", 
            "parameters": { JSON Schema }
          }
        }
        
        Anthropic format:
        {
          "name": "tool_name",
          "description": "description",
          "input_schema": { JSON Schema }
        }
        """
        if openai_tool.type != "function":
            raise ValueError(f"Unsupported tool type for Anthropic conversion: {openai_tool.type}")
        
        return {
            "name": openai_tool.function.name,
            "description": openai_tool.function.description,
            "input_schema": openai_tool.function.parameters
        }

    def bind_tools(
        self,
        tools: List[Any],  # Can be LangChain tools or raw dicts
        **kwargs: Any,
    ):
        """Bind tools to the language model."""
        logger.debug(f"[CopilotChat bind_tools] Attempting to bind {len(tools)} tools. Model: {self.model_name}")
        copilot_tools: List[Tool] = []

        # Check if the model supports tools
        supports_tools = self._model_supports_tools()
        logger.debug(f"[CopilotChat bind_tools] Model {self.model_name} support for tools: {supports_tools}")
        if not supports_tools:
            logger.warning(
                f"Model {self.model_name} is known to have issues with tools in GitHub Copilot. "
                f"Tool calls may fail with 500 errors, but automatic fallback without tools is enabled."
            )
        else:
            logger.info(
                f"Model {self.model_name} supports tools in GitHub Copilot. "
                f"If tool calls fail with 500 errors, automatic fallback without tools will be attempted."
            )

        for tool_item in tools:
            if isinstance(tool_item, dict):
                # Assume it's already in the correct Copilot Tool format
                try:
                    copilot_tools.append(Tool.model_validate(tool_item))
                except ValidationError as e:
                    logger.error(f"Failed to validate raw tool dict: {tool_item}, Error: {e}")
                    raise
            else:
                # Convert LangChain tool to Copilot Tool format
                if hasattr(tool_item, "name") and hasattr(tool_item, "description"):
                    # Parse the docstring to get better descriptions
                    main_description, param_descriptions = self._parse_docstring_for_tool_info(tool_item.description)
                    
                    # Use the main description (without Args section) as the tool description
                    tool_description = main_description if main_description else tool_item.description
                    
                    parameters_schema = {}
                    if hasattr(tool_item, "args_schema") and tool_item.args_schema:
                        args_schema = tool_item.args_schema
                        if isinstance(args_schema, type):
                            try:
                                # Check if it's a Pydantic v2 model
                                if hasattr(args_schema, "model_json_schema"):
                                    try:
                                        # Try to generate the full schema first
                                        full_schema = args_schema.model_json_schema()

                                        # Filter out unwanted fields from properties and required
                                        filtered_properties = {}
                                        filtered_required = []

                                        for field_name, field_schema in full_schema.get("properties", {}).items():
                                            # Skip tool_call_id field as it's injected by LangChain
                                            if field_name == "tool_call_id":
                                                logger.debug(
                                                    f"Skipping 'tool_call_id' field for tool {tool_item.name} as it's injected by LangChain."
                                                )
                                                continue
                                            # Exclude fields that are known to cause issues (e.g., InMemoryBaseStore)
                                            if field_name == "store":
                                                logger.debug(
                                                    f"Skipping 'store' field for tool {tool_item.name} as it's not JSON serializable."
                                                )
                                                continue

                                            # Clean up the field schema for GitHub Copilot compatibility
                                            cleaned_field_schema = self._clean_field_schema(field_schema)
                                            
                                            # Override description with docstring parameter description if available
                                            if field_name in param_descriptions:
                                                cleaned_field_schema["description"] = param_descriptions[field_name]
                                            
                                            filtered_properties[field_name] = cleaned_field_schema

                                            # Add to required if it was in the original required list
                                            if field_name in full_schema.get("required", []):
                                                filtered_required.append(field_name)

                                        parameters_schema = {
                                            "type": "object",
                                            "properties": filtered_properties,
                                            "required": filtered_required,
                                        }

                                        # Ensure additionalProperties is set to false for strict schema
                                        parameters_schema["additionalProperties"] = False

                                    except Exception as schema_error:
                                        # If schema generation fails, create a manual schema from model fields
                                        logger.warning(f"Failed to generate full schema for tool {tool_item.name}: {schema_error}")
                                        logger.debug(f"Falling back to manual schema generation for tool {tool_item.name}")

                                        filtered_properties = {}
                                        filtered_required = []

                                        for field_name, field_info in args_schema.model_fields.items():
                                            # Skip tool_call_id field as it's injected by LangChain
                                            if field_name == "tool_call_id":
                                                logger.debug(
                                                    f"Skipping 'tool_call_id' field for tool {tool_item.name} as it's injected by LangChain."
                                                )
                                                continue
                                            # Exclude fields that are known to cause issues (e.g., InMemoryBaseStore)
                                            if field_name == "store":
                                                logger.debug(
                                                    f"Skipping 'store' field for tool {tool_item.name} as it's not JSON serializable."
                                                )
                                                continue

                                            # Create a basic schema for this field based on its type
                                            field_schema = {"title": field_name.replace("_", " ").title()}

                                            # Add description - prefer docstring parameter description, then field description
                                            if field_name in param_descriptions:
                                                field_schema["description"] = param_descriptions[field_name]
                                            elif hasattr(field_info, 'description') and field_info.description:
                                                field_schema["description"] = field_info.description

                                            # Determine type from annotation
                                            annotation = field_info.annotation
                                            if annotation == str:
                                                field_schema["type"] = "string"
                                            elif annotation == int:
                                                field_schema["type"] = "integer"
                                            elif annotation == float:
                                                field_schema["type"] = "number"
                                            elif annotation == bool:
                                                field_schema["type"] = "boolean"
                                            elif hasattr(annotation, "__origin__"):
                                                # Handle Optional types and other generics
                                                if annotation.__origin__ is Union:
                                                    args = annotation.__args__
                                                    if len(args) == 2 and type(None) in args:
                                                        # This is Optional[T]
                                                        non_none_type = args[0] if args[1] is type(None) else args[1]
                                                        if non_none_type == str:
                                                            field_schema["anyOf"] = [{"type": "string"}, {"type": "null"}]
                                                        elif non_none_type == int:
                                                            field_schema["anyOf"] = [{"type": "integer"}, {"type": "null"}]
                                                        elif non_none_type == bool:
                                                            field_schema["anyOf"] = [{"type": "boolean"}, {"type": "null"}]
                                                        else:
                                                            field_schema["type"] = "string"  # fallback
                                                    else:
                                                        field_schema["type"] = "string"  # fallback for complex unions
                                                else:
                                                    field_schema["type"] = "string"  # fallback
                                            else:
                                                field_schema["type"] = "string"  # fallback

                                            # Add default value if present and serializable
                                            if hasattr(field_info, 'default') and field_info.default is not ...:
                                                try:
                                                    # Test if the default value is JSON serializable
                                                    import json
                                                    json.dumps(field_info.default)
                                                    field_schema["default"] = field_info.default
                                                except (TypeError, ValueError):
                                                    # Skip non-serializable defaults
                                                    pass

                                            filtered_properties[field_name] = field_schema

                                            # Add to required if field is required
                                            if field_info.is_required():
                                                filtered_required.append(field_name)

                                        parameters_schema = {
                                            "type": "object",
                                            "properties": filtered_properties,
                                            "required": filtered_required,
                                            "additionalProperties": False,
                                        }
                                # Check if it's a Pydantic v1 model
                                elif hasattr(args_schema, "schema"):
                                    schema = args_schema.schema()
                                    # Clean the schema for GitHub Copilot compatibility
                                    parameters_schema = self._clean_schema_for_copilot(schema)
                                else:
                                    logger.warning(
                                        f"Unsupported Pydantic args_schema type for tool {tool_item.name}: {type(args_schema)}"
                                    )
                                    parameters_schema = {"type": "object", "properties": {}, "additionalProperties": False}
                            except Exception as e:
                                logger.warning(f"Failed to generate schema for tool {tool_item.name}: {e}")
                                parameters_schema = {"type": "object", "properties": {}, "additionalProperties": False}  # Fallback to empty schema
                        elif isinstance(args_schema, dict):  # Already a dict
                            parameters_schema = self._clean_schema_for_copilot(args_schema)
                        else:
                            logger.warning(
                                f"Unsupported args_schema type for tool {tool_item.name}: {type(args_schema)}"
                            )
                            parameters_schema = {"type": "object", "properties": {}, "additionalProperties": False}

                    # Create the tool with cleaned schema
                    tool_function = ToolFunction(
                        name=tool_item.name,
                        description=tool_description,
                        parameters=parameters_schema
                    )

                    copilot_tool = Tool(
                        type="function",
                        function=tool_function,
                    )

                    logger.debug(f"[CopilotChat bind_tools] Converted LangChain tool '{tool_item.name}' to Copilot format: {copilot_tool.model_dump_json(indent=2)}")
                    copilot_tools.append(copilot_tool)
                else:
                    logger.warning(f"[CopilotChat bind_tools] Skipping unsupported tool type during binding: {type(tool_item)}")

        new_instance = self.model_copy()
        new_instance._bound_tools = copilot_tools  # Store as Pydantic Tool objects
        logger.debug(f"[CopilotChat bind_tools] Successfully bound {len(copilot_tools)} tools. Stored in _bound_tools.")
        return new_instance

    def _model_requires_streaming(self) -> bool:
        """Check if the current model requires streaming to work."""
        # Models that only work with streaming enabled
        streaming_required_models = [
            "gpt-4.1",
            "o4-mini",
            "claude-3.5-sonnet",
            "claude-sonnet-4", 
            "claude-3.7-sonnet",
        ]
        
        return any(model in self.model_name for model in streaming_required_models)

    def _model_supports_tools(self) -> bool:
        """Check if the current model supports tools."""
        # Models that are confirmed to work with tools in GitHub Copilot
        # Based on actual testing results
        supported_models = [
            "gpt-4o",              # ✅ Confirmed working (but being deprecated)
            "gemini-2.5-pro",      # ✅ Confirmed working in tests
            "gemini-2.0-flash-001", # ✅ Should work (Gemini family)
            "gpt-4.1",             # ✅ Works with streaming + tools
            "o4-mini",             # ✅ Works with streaming + tools
            "claude-3.5-sonnet",   # ✅ Works with streaming + tools
            "claude-sonnet-4",     # ✅ Works with streaming + tools
            "claude-3.7-sonnet",   # ✅ Works with streaming + tools
        ]
        
        return any(model in self.model_name for model in supported_models)

    def _parse_docstring_for_tool_info(self, docstring: str) -> Tuple[str, Dict[str, str]]:
        """Parse a tool's docstring to extract description and parameter descriptions.
        
        Args:
            docstring: The tool's docstring
            
        Returns:
            Tuple of (main_description, parameter_descriptions_dict)
        """
        if not docstring:
            return "", {}
            
        # Split docstring into lines
        lines = docstring.strip().split('\n')
        
        # Find the main description (everything before Args:)
        main_desc_lines = []
        param_descriptions = {}
        
        in_args_section = False
        current_param = None
        current_param_desc = []
        
        for line in lines:
            line = line.strip()
            
            # Check if we're entering the Args section
            if line.lower().startswith('args:') or line.lower().startswith('arguments:'):
                in_args_section = True
                continue
                
            # Check if we're entering another section (Returns, Raises, etc.)
            if in_args_section and line.lower().startswith(('returns:', 'return:', 'raises:', 'raise:', 'examples:', 'example:', 'note:', 'notes:')):
                # Save the last parameter if we have one
                if current_param and current_param_desc:
                    param_descriptions[current_param] = ' '.join(current_param_desc).strip()
                break
                
            if in_args_section:
                # Look for parameter definitions (param_name: description)
                param_match = re.match(r'^(\w+):\s*(.+)', line)
                if param_match:
                    # Save the previous parameter if we have one
                    if current_param and current_param_desc:
                        param_descriptions[current_param] = ' '.join(current_param_desc).strip()
                    
                    current_param = param_match.group(1)
                    current_param_desc = [param_match.group(2)]
                elif current_param and line:
                    # Continuation of the current parameter description
                    current_param_desc.append(line)
            else:
                # We're in the main description
                if line:
                    main_desc_lines.append(line)
        
        # Save the last parameter if we have one
        if current_param and current_param_desc:
            param_descriptions[current_param] = ' '.join(current_param_desc).strip()
            
        # Join main description lines
        main_description = ' '.join(main_desc_lines).strip()
        
        return main_description, param_descriptions

    def _clean_field_schema(self, field_schema: Dict[str, Any]) -> Dict[str, Any]:
        """Clean a field schema for GitHub Copilot compatibility."""
        cleaned = {}

        # Copy basic properties
        for key in ["type", "description", "title", "default", "enum"]:
            if key in field_schema:
                cleaned[key] = field_schema[key]

        # Handle anyOf/oneOf by simplifying to the first type
        if "anyOf" in field_schema:
            # Take the first non-null type
            for option in field_schema["anyOf"]:
                if option.get("type") != "null":
                    cleaned["type"] = option.get("type", "string")
                    # Copy enum if present
                    if "enum" in option:
                        cleaned["enum"] = option["enum"]
                    break
            else:
                cleaned["type"] = "string"  # fallback
        elif "oneOf" in field_schema:
            # Take the first type
            first_option = field_schema["oneOf"][0]
            cleaned["type"] = first_option.get("type", "string")
            # Copy enum if present
            if "enum" in first_option:
                cleaned["enum"] = first_option["enum"]

        # Ensure we have a type
        if "type" not in cleaned:
            cleaned["type"] = "string"

        return cleaned

    def _clean_schema_for_copilot(self, schema: Dict[str, Any]) -> Dict[str, Any]:
        """Clean a schema for GitHub Copilot compatibility."""
        cleaned = {
            "type": "object",
            "properties": {},
            "additionalProperties": False,
        }

        if "properties" in schema:
            for prop_name, prop_schema in schema["properties"].items():
                # Skip problematic fields
                if prop_name in ["tool_call_id", "store"]:
                    continue
                cleaned["properties"][prop_name] = self._clean_field_schema(prop_schema)

        if "required" in schema:
            # Filter out problematic required fields
            cleaned["required"] = [
                field for field in schema["required"]
                if field not in ["tool_call_id", "store"]
            ]

        return cleaned

    def _ensure_auth(self, is_async: bool = False) -> None:
        """Ensures GitHub OAuth token is available, performing device flow if needed (sync only)."""
        if self._github_oauth_token:
            return

        if is_async:
            logger.error(
                "GitHub OAuth token not found. Cannot perform device auth in async context. "
                "Please run a synchronous operation first or set GITHUB_COPILOT_OAUTH_TOKEN."
            )
            raise ValueError("GitHub OAuth token missing for async operation. Run a sync operation or set token.")

        logger.info("GitHub Copilot OAuth token not found. Attempting to obtain one via device authentication flow.")
        if COPILOT_OAUTH_CLIENT_ID == "YOUR_GITHUB_OAUTH_APP_CLIENT_ID_HERE":
            logger.error(
                "Cannot initiate device auth flow: COPILOT_OAUTH_CLIENT_ID is a placeholder. "
                "Please replace it with your actual GitHub OAuth App Client ID."
            )
            raise ValueError("COPILOT_OAUTH_CLIENT_ID is a placeholder.")

        token_str = perform_device_auth_flow(self.request_timeout)
        if token_str:
            self._github_oauth_token = SecretStr(token_str)
        else:
            logger.error("Failed to obtain GitHub OAuth token via device flow. CopilotChat will not be usable.")
            raise ValueError("Failed to obtain GitHub OAuth token.")

    def _get_copilot_api_token(self) -> Optional[ApiToken]:
        """
        Retrieves the Copilot API key.
        If the key is missing or expired, attempts to fetch/refresh it. (Synchronous)
        """
        self._ensure_auth(is_async=False)
        if not self._github_oauth_token:
            logger.error("Cannot fetch Copilot API key: GitHub OAuth token is not set after auth check.")
            return None

        current_time = datetime.now(timezone.utc)
        # Refresh if token is missing or expires in less than 5 minutes
        if not self._copilot_api_token or self._copilot_api_token.remaining_seconds() < 5 * 60:
            self._copilot_api_token = get_copilot_api_token(
                self._github_oauth_token.get_secret_value(),
                self.request_timeout,
                self.editor_version,
                self.copilot_integration_id,
                self._sync_client,
            )
        return self._copilot_api_token

    async def _aget_copilot_api_token(self) -> Optional[ApiToken]:
        """
        Retrieves the Copilot API key asynchronously.
        """
        await self._aensure_auth()
        if not self._github_oauth_token:
            logger.error("Cannot fetch Copilot API key: GitHub OAuth token is not set after async auth check.")
            return None

        current_time = datetime.now(timezone.utc)
        if not self._copilot_api_token or self._copilot_api_token.remaining_seconds() < 5 * 60:
            self._copilot_api_token = await aget_copilot_api_token(
                self._github_oauth_token.get_secret_value(),
                self.request_timeout,
                self.editor_version,
                self.copilot_integration_id,
                self._async_client,
            )
        return self._copilot_api_token

    async def _aensure_auth(self) -> None:
        """Async check for OAuth token. Does not trigger device flow."""
        if self._github_oauth_token:
            return
        logger.error(
            "GitHub OAuth token not found for async operation. "
            "Please run a synchronous operation first to complete device auth, "
            "or set the GITHUB_COPILOT_OAUTH_TOKEN environment variable."
        )
        raise ValueError("GitHub OAuth token missing for async operation.")

    def _convert_message_to_copilot_format(self, message: BaseMessage) -> ChatMessage:
        """Converts a Langchain BaseMessage to our Pydantic ChatMessage format."""
        if isinstance(message, HumanMessage):
            if isinstance(message.content, str):
                # For simple text messages, use string content directly
                return ChatMessage(role=Role.USER, content=message.content)
            elif isinstance(message.content, list):
                # For multipart messages, convert to ChatMessagePart objects
                content_parts: List[ChatMessagePart] = []
                for item in message.content:
                    if isinstance(item, dict):
                        if item.get("type") == "text":
                            content_parts.append(ChatMessagePart(type="text", text=item.get("text")))
                        elif item.get("type") == "image_url":
                            content_parts.append(
                                ChatMessagePart(type="image_url", image_url=ImageUrl(url=item.get("image_url", {}).get("url")))
                            )
                return ChatMessage(role=Role.USER, content=content_parts)
            else:
                # Fallback for other content types
                return ChatMessage(role=Role.USER, content=str(message.content))
        elif isinstance(message, AIMessage):
            content_text = message.content if isinstance(message.content, str) else ""
            tool_calls = []
            if message.tool_calls:
                for tc in message.tool_calls:
                    tool_calls.append(
                        ToolCall(
                            id=tc["id"],
                            function=FunctionContent(
                                name=tc["name"],
                                arguments=json.dumps(tc["args"])  # Arguments are JSON string in FunctionContent
                            )
                        )
                    )
            return ChatMessage(
                role=Role.ASSISTANT,
                content=content_text if content_text else None,
                tool_calls=tool_calls,
            )
        elif isinstance(message, SystemMessage):
            return ChatMessage(role=Role.SYSTEM, content=message.content)
        elif isinstance(message, ToolMessage):
            return ChatMessage(
                role=Role.TOOL, content=message.content, tool_call_id=message.tool_call_id
            )
        else:
            raise ValueError(f"Unsupported message type: {type(message)}")

    def _prepare_request_payload(
        self,
        messages: List[BaseMessage],
        stream: bool,
        **kwargs: Any,
    ) -> Dict[str, Any]:
        """Prepares the request payload for the Copilot API request."""
        logger.debug("Starting _prepare_request_payload")
        converted_messages = [self._convert_message_to_copilot_format(m) for m in messages]
        logger.debug(f"Converted {len(converted_messages)} messages")

        # Manually serialize messages to ensure proper format
        serialized_messages = []
        for i, msg in enumerate(converted_messages):
            logger.debug(f"Processing message {i}: role={msg.role}")
            msg_dict = {"role": msg.role.value}

            # Handle content
            if msg.content is not None:
                if isinstance(msg.content, str):
                    msg_dict["content"] = msg.content
                elif isinstance(msg.content, list):
                    msg_dict["content"] = [part.model_dump() for part in msg.content]

            # Handle tool_calls - only for assistant messages
            if msg.tool_calls and msg.role == Role.ASSISTANT:
                msg_dict["tool_calls"] = [tc.model_dump() for tc in msg.tool_calls]

            # Handle tool_call_id - only for tool messages
            if msg.tool_call_id is not None and msg.role == Role.TOOL:
                msg_dict["tool_call_id"] = msg.tool_call_id

            serialized_messages.append(msg_dict)
            logger.debug(f"Serialized message {i}: {msg_dict}")

        # Determine if it's a vision request based on user message content
        logger.debug("Checking for vision request")
        is_vision_request = False
        if converted_messages:
            last_user_message = next((m for m in reversed(converted_messages) if m.role == Role.USER), None)
            if last_user_message and last_user_message.is_multipart:
                if any(part.type == "image_url" for part in last_user_message.content):
                    is_vision_request = True

        logger.debug("Building payload dict")
        payload_dict: Dict[str, Any] = {
            "model": self.model_name,
            "messages": serialized_messages,
            "stream": stream,
            "temperature": kwargs.get("temperature", self.temperature),
        }

        # Add optional fields only if they have values
        logger.debug("Adding optional fields")
        if kwargs.get("top_p", self.top_p) is not None:
            payload_dict["top_p"] = kwargs.get("top_p", self.top_p)
        if kwargs.get("max_tokens", self.max_tokens) is not None:
            payload_dict["max_tokens"] = kwargs.get("max_tokens", self.max_tokens)

        # Add bound tools if available
        logger.debug(f"Checking for bound tools: hasattr={hasattr(self, '_bound_tools')}")
        if hasattr(self, "_bound_tools"):
            logger.debug(f"Found _bound_tools: {self._bound_tools}")
            if self._bound_tools:
                logger.debug("Adding tools to payload")
                try:
                    # GitHub Copilot expects OpenAI format for all models, even Claude
                    # This is based on the working Lua implementation that uses OpenAI handlers
                    logger.debug("Using OpenAI format for all models (GitHub Copilot standard)")
                    payload_dict["tools"] = [tool.model_dump() for tool in self._bound_tools]
                    payload_dict["tool_choice"] = kwargs.get("tool_choice", ToolChoice.AUTO.value)
                    logger.debug(f"Added {len(self._bound_tools)} tools in OpenAI format")
                except Exception as e:
                    logger.error(f"Error adding tools to payload: {e}")
                    raise

        logger.debug("Returning payload dict")
        return payload_dict

    def _get_request_headers(self, api_key: str, stream: bool, is_vision_request: bool = False) -> Dict[str, str]:
        """Constructs headers for the Copilot API request."""
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json",
            "Accept": "text/event-stream" if stream else "application/json",
            "Editor-Version": self.editor_version,
            "Copilot-Integration-Id": self.copilot_integration_id,
            "User-Agent": self.user_agent,
        }
        if is_vision_request:
            headers["Copilot-Vision-Request"] = "true"
        return headers

    def _parse_tool_calls(self, raw_tool_calls: List[Dict[str, Any]]) -> List[LCToolCall]:
        """Parses raw tool calls from Copilot response into Langchain ToolCall objects."""
        parsed_tool_calls = []
        for tc_data in raw_tool_calls:
            try:
                tool_call = ToolCall.model_validate(tc_data)
                args_dict = json.loads(tool_call.function.arguments)
                parsed_tool_calls.append(LCToolCall(id=tool_call.id, name=tool_call.function.name, args=args_dict))
            except (ValidationError, json.JSONDecodeError) as e:
                logger.warning(f"Failed to parse tool call: {tc_data}, Error: {e}")
        return parsed_tool_calls

    def _parse_chat_result(self, response_data: Dict[str, Any]) -> ChatResult:
        """Parses a non-streaming JSON response from Copilot into a ChatResult."""
        try:
            response_event = ResponseEvent.model_validate(response_data)
        except ValidationError as e:
            logger.error(f"Failed to validate ResponseEvent: {e}. Raw: {response_data}")
            raise ValueError(f"Failed to parse Copilot API response: {e}")

        generations = []
        if not response_event.choices:
            logger.warning(f"No 'choices' in Copilot response: {response_data}")
            generations.append(ChatGeneration(message=AIMessage(content=""), generation_info={"warning": "No choices in response"}))
        else:
            first_choice = response_event.choices[0]
            message_data = first_choice.message  # For non-streaming, message field is used

            text_content = message_data.content if message_data and message_data.content else ""
            tool_calls = []
            if message_data and message_data.tool_calls:
                tool_calls = self._parse_tool_calls(
                    [tc.model_dump() for tc in message_data.tool_calls]
                )  # Convert Pydantic ToolCall to dict for parsing

            ai_message = AIMessage(
                content=text_content,
                tool_calls=tool_calls,
            )
            generation_info = {"finish_reason": first_choice.finish_reason}
            # Add usage if present in the top-level response_data (not in ResponseEvent Pydantic model currently)
            if "usage" in response_data:
                generation_info["usage"] = response_data["usage"]

            generations.append(ChatGeneration(message=ai_message, generation_info=generation_info))

        llm_output = {"model_name": self.model_name}
        if "usage" in response_data:
            llm_output["token_usage"] = response_data["usage"]

        return ChatResult(generations=generations, llm_output=llm_output)

    def _stream_to_chat_result(
        self,
        messages: List[BaseMessage],
        stop: Optional[List[str]] = None,
        run_manager: Optional[CallbackManagerForLLMRun] = None,
        **kwargs: Any,
    ) -> ChatResult:
        """Convert streaming response to ChatResult for models that require streaming."""
        logger.debug(f"Using streaming mode for model {self.model_name} (required for this model)")
        
        # Collect all chunks from the stream
        content_parts = []
        tool_calls_dict = {}  # Use dict to accumulate by ID
        tool_calls_by_index = {}  # Also track by index for chunks without ID
        generation_info = {}
        
        for chunk in self._stream(messages, stop, run_manager, **kwargs):
            if chunk.message.content:
                content_parts.append(chunk.message.content)
            
            if chunk.message.tool_call_chunks:
                # Accumulate tool call chunks
                for tc_chunk in chunk.message.tool_call_chunks:
                    tc_id = tc_chunk.get("id")
                    tc_index = tc_chunk.get("index")
                    
                    # Debug logging
                    if os.getenv("COPILOT_DEBUG", "").lower() in ("true", "1", "yes"):
                        print(f"DEBUG - Tool call chunk: ID={tc_id}, index={tc_index}, name={tc_chunk.get('name')}, args={tc_chunk.get('args')}")
                    
                    if tc_id:
                        # This is a new tool call with ID and name
                        tool_calls_dict[tc_id] = {
                            "id": tc_id,
                            "name": tc_chunk.get("name") or "",
                            "args": tc_chunk.get("args") or "",
                            "type": "function",
                            "index": tc_index
                        }
                        # Also track by index for future chunks
                        if tc_index is not None:
                            tool_calls_by_index[tc_index] = tc_id
                        
                        if os.getenv("COPILOT_DEBUG", "").lower() in ("true", "1", "yes"):
                            print(f"DEBUG - Created new tool call: {tool_calls_dict[tc_id]}")
                    
                    elif tc_index is not None and tc_index in tool_calls_by_index:
                        # This is a continuation chunk with only index and args
                        tc_id = tool_calls_by_index[tc_index]
                        existing_tc = tool_calls_dict[tc_id]
                        
                        # Append arguments if provided
                        if tc_chunk.get("args"):
                            existing_tc["args"] += tc_chunk["args"]
                            if os.getenv("COPILOT_DEBUG", "").lower() in ("true", "1", "yes"):
                                print(f"DEBUG - Updated tool call args: {existing_tc['args']}")
            
            if chunk.generation_info:
                generation_info.update(chunk.generation_info)
        
        # Debug final tool calls
        if os.getenv("COPILOT_DEBUG", "").lower() in ("true", "1", "yes"):
            print(f"DEBUG - Final tool calls dict: {tool_calls_dict}")
        
        # Combine content
        full_content = "".join(content_parts)
        
        # Convert tool calls to LangChain format
        lc_tool_calls = []
        for tc in tool_calls_dict.values():
            try:
                # Parse arguments if they're a string
                args = tc["args"]
                if os.getenv("COPILOT_DEBUG", "").lower() in ("true", "1", "yes"):
                    print(f"DEBUG - Processing tool call: name={tc['name']}, raw_args='{args}', type={type(args)}")
                
                if isinstance(args, str) and args.strip():
                    try:
                        args = json.loads(args)
                        if os.getenv("COPILOT_DEBUG", "").lower() in ("true", "1", "yes"):
                            print(f"DEBUG - Parsed args: {args}")
                    except json.JSONDecodeError as e:
                        logger.warning(f"Failed to parse tool call arguments: {args}, Error: {e}")
                        args = {}
                elif not args:
                    args = {}
                
                # Only add tool calls that have a name
                if tc["name"]:
                    lc_tool_calls.append(LCToolCall(
                        name=tc["name"],
                        args=args,
                        id=tc["id"]
                    ))
            except Exception as e:
                logger.warning(f"Failed to convert tool call: {tc}, Error: {e}")
        
        # Create the final message
        message = AIMessage(content=full_content, tool_calls=lc_tool_calls)
        
        return ChatResult(
            generations=[ChatGeneration(message=message, generation_info=generation_info)]
        )

    def _generate(
        self,
        messages: List[BaseMessage],
        stop: Optional[List[str]] = None,
        run_manager: Optional[CallbackManagerForLLMRun] = None,
        **kwargs: Any,
    ) -> ChatResult:
        """Makes a request to the Copilot chat completions API."""
        
        # Check if this model requires streaming
        if self._model_requires_streaming():
            logger.debug(f"Model {self.model_name} requires streaming, using streaming mode")
            return self._stream_to_chat_result(messages, stop, run_manager, **kwargs)
        
        # Continue with non-streaming for other models
        if stop:
            logger.warning("CopilotChat does not currently support the 'stop' parameter directly in API calls.")

        api_token = self._get_copilot_api_token()
        if not api_token:
            raise ValueError("Failed to obtain Copilot API token for non-streaming call.")

        payload = self._prepare_request_payload(messages, stream=False, **kwargs)
        is_vision_request = False  # payload is now a dict, so we can't use .get() on it directly
        if isinstance(payload, dict):
            is_vision_request = payload.get("intent", False)
        headers = self._get_request_headers(
            api_token.api_key.get_secret_value(), stream=False, is_vision_request=is_vision_request
        )

        logger.debug(f"Copilot non-streaming request payload: {json.dumps(payload, indent=2)}")
        logger.debug(f"Copilot non-streaming request headers: {headers}")

        # Check if this request includes tools
        has_tools = isinstance(payload, dict) and "tools" in payload and payload["tools"]

        response = None  # Initialize response outside try block
        try:
            if os.getenv("COPILOT_DEBUG", "").lower() in ("true", "1", "yes"):
                print(f"DEBUG - Sending request with model: {payload['model']}")
                print(f"DEBUG - Request payload: {json.dumps(payload, indent=2)}")

            response = self._sync_client.post(
                COPILOT_CHAT_URL,
                headers=headers,
                json=payload,  # Use the dict directly
                timeout=self.request_timeout,
            )
            logger.debug(f"Copilot API response status: {response.status_code}")
            logger.debug(f"Copilot API response headers: {dict(response.headers)}")

            if response.status_code != 200:
                error_content = "Unknown error"
                try:
                    error_data = response.json()
                    logger.debug(f"Error response data type: {type(error_data)}, value: {error_data}")
                    if isinstance(error_data, dict):
                        if isinstance(error_data.get("error"), dict):
                            error_content = error_data.get("error", {}).get("message", json.dumps(error_data))
                        else:
                            error_content = json.dumps(error_data)
                        if "message" in error_data and "code" in error_data:
                            error_content = f"{error_data['code']}: {error_data['message']}"
                    else:
                        # error_data is not a dict (could be a string or other type)
                        error_content = str(error_data)
                        logger.debug(f"Error data is not dict, converted to string: {error_content}")
                except json.JSONDecodeError:
                    error_content = response.text
                    logger.debug(f"JSON decode error, using response text: {error_content}")

                # Handle 500 errors with automatic fallback for tool calls
                if response.status_code == 500 and has_tools:
                    logger.warning(f"Received 500 error from GitHub Copilot API with model {self.model_name} when using tools")
                    logger.info(f"Attempting automatic fallback: retrying request without tools for model {self.model_name}")

                    if os.getenv("COPILOT_DEBUG", "").lower() in ("true", "1", "yes"):
                        print(f"DEBUG - 500 error with tools for model: {self.model_name}")
                        print(f"DEBUG - Error content: {error_content}")
                        print(f"DEBUG - Retrying without tools...")

                    # Create a new payload without tools
                    fallback_payload = payload.copy()
                    fallback_payload.pop("tools", None)
                    fallback_payload.pop("tool_choice", None)

                    try:
                        logger.debug("Retrying request without tools...")
                        fallback_response = self._sync_client.post(
                            COPILOT_CHAT_URL,
                            headers=headers,
                            json=fallback_payload,
                            timeout=self.request_timeout,
                        )

                        if fallback_response.status_code == 200:
                            logger.info(f"Fallback successful: {self.model_name} responded without tools")
                            if os.getenv("COPILOT_DEBUG", "").lower() in ("true", "1", "yes"):
                                print(f"DEBUG - Fallback successful for model: {self.model_name}")

                            fallback_data = fallback_response.json()
                            logger.debug(f"Copilot fallback response data: {fallback_data}")
                            return self._parse_chat_result(fallback_data)
                        else:
                            logger.warning(f"Fallback also failed with status {fallback_response.status_code}")
                            if os.getenv("COPILOT_DEBUG", "").lower() in ("true", "1", "yes"):
                                print(f"DEBUG - Fallback also failed with status: {fallback_response.status_code}")
                    except Exception as fallback_error:
                        logger.warning(f"Fallback request failed: {fallback_error}")
                        if os.getenv("COPILOT_DEBUG", "").lower() in ("true", "1", "yes"):
                            print(f"DEBUG - Fallback request failed: {fallback_error}")

                # Log other 500 errors for debugging
                elif response.status_code == 500:
                    logger.warning(f"Received 500 error from GitHub Copilot API with model {self.model_name}")
                    if os.getenv("COPILOT_DEBUG", "").lower() in ("true", "1", "yes"):
                        print(f"DEBUG - 500 error for model: {self.model_name}")
                        print(f"DEBUG - Error content: {error_content}")
                        if has_tools:
                            print(f"DEBUG - Request included {len(payload.get('tools', []))} tools")

                logger.error(f"HTTP error {response.status_code} during Copilot non-streaming invoke: {error_content}")
                logger.error(f"Response headers: {dict(response.headers)}")
                logger.error(f"Response text: {response.text}")
                raise ValueError(f"Copilot API request failed with status {response.status_code}: {error_content}") from requests.exceptions.HTTPError(response=response)

            response_data = response.json()
            logger.debug(f"Copilot non-streaming response data: {response_data}")
            return self._parse_chat_result(response_data)

        except requests.exceptions.RequestException as e:
            logger.error(f"Request error during Copilot non-streaming invoke: {e}")
            raise ValueError(f"Request to Copilot API failed: {e}") from e
        except ValidationError as e:
            logger.error(
                f"Pydantic validation error for non-streaming response: {e}. Response text: {response.text if response else 'N/A'}"
            )
            raise ValueError("Error: Could not validate response from Copilot.") from e
        except Exception as e:
            logger.exception("An unexpected error occurred during Copilot non-streaming invoke.")
            raise ValueError(f"Unexpected error: {e}") from e

    def _parse_tool_call_chunks(self, raw_tool_calls_delta: List[Dict[str, Any]]) -> List[LCToolCallChunk]:
        """Parses raw tool call deltas from a stream chunk into Langchain ToolCallChunk objects."""
        parsed_chunks = []
        for tc_data in raw_tool_calls_delta:
            try:
                tool_call_chunk = ToolCallChunk.model_validate(tc_data)
                parsed_chunks.append(
                    LCToolCallChunk(
                        id=tool_call_chunk.id,
                        name=tool_call_chunk.function.name if tool_call_chunk.function else None,
                        args=tool_call_chunk.function.arguments if tool_call_chunk.function else "",
                        index=tool_call_chunk.index,
                    )
                )
            except ValidationError as e:
                logger.warning(f"Failed to parse tool call chunk: {tc_data}, Error: {e}")
        return parsed_chunks

    def _stream(
        self,
        messages: List[BaseMessage],
        stop: Optional[List[str]] = None,
        run_manager: Optional[CallbackManagerForLLMRun] = None,
        **kwargs: Any,
    ) -> Generator[ChatGenerationChunk, None, None]:
        """Streams responses from the Copilot chat completions API."""
        if stop:
            logger.warning("CopilotChat does not currently support the 'stop' parameter directly in API calls.")

        api_token = self._get_copilot_api_token()
        if not api_token:
            error_msg = "Failed to obtain Copilot API token for streaming call."
            logger.error(error_msg)
            yield ChatGenerationChunk(message=AIMessageChunk(content=f"Error: {error_msg}"))
            return

        payload = self._prepare_request_payload(messages, stream=True, **kwargs)
        has_tools = isinstance(payload, dict) and "tools" in payload and payload["tools"]
        is_vision_request = False  # payload is now a dict, so we can't use .get() on it directly
        if isinstance(payload, dict):
            is_vision_request = payload.get("intent", False)
        headers = self._get_request_headers(
            api_token.api_key.get_secret_value(), stream=True, is_vision_request=is_vision_request
        )

        try:
            if os.getenv("COPILOT_DEBUG", "").lower() in ("true", "1", "yes"):
                print(f"DEBUG - Sending request with model: {payload['model']}")
                print(f"DEBUG - Request payload: {json.dumps(payload, indent=2)}")

            response = self._sync_client.post(
                COPILOT_CHAT_URL, headers=headers, json=payload, stream=True, timeout=self.request_timeout
            )
            response.raise_for_status()
            for line in response.iter_lines():
                if line:
                    decoded_line = line.decode("utf-8")
                    if decoded_line.startswith("data: "):
                        json_data_str = decoded_line[len("data: ") :].strip()
                        if not json_data_str:
                            continue
                        if json_data_str == "[DONE]":
                            logger.debug("Stream received [DONE] marker.")
                            if run_manager:
                                run_manager.on_llm_end(None)  # Pass None as response object is closed
                            break

                        try:
                            chunk_data = json.loads(json_data_str)
                            response_event = ResponseEvent.model_validate(chunk_data)
                            choices = response_event.choices

                            if not choices:
                                logger.debug(f"Stream chunk without 'choices': {chunk_data}")
                                continue

                            first_choice = choices[0]
                            delta = first_choice.delta

                            content_delta = delta.content if delta and delta.content is not None else ""
                            tool_call_chunks_delta = []
                            if delta and delta.tool_calls:
                                tool_call_chunks_delta = self._parse_tool_call_chunks(
                                    [tc.model_dump() for tc in delta.tool_calls]
                                )

                            finish_reason = first_choice.finish_reason
                            generation_info = {}
                            if finish_reason:
                                generation_info["finish_reason"] = finish_reason

                            chunk = ChatGenerationChunk(
                                message=AIMessageChunk(content=content_delta, tool_call_chunks=tool_call_chunks_delta),
                                generation_info=generation_info if generation_info else None,
                            )
                            yield chunk
                            if run_manager and content_delta:
                                run_manager.on_llm_new_token(content_delta, chunk=chunk)

                        except (json.JSONDecodeError, ValidationError) as e:
                            logger.warning(f"Failed to decode or validate JSON from stream: {json_data_str}, Error: {e}")
                            yield ChatGenerationChunk(
                                message=AIMessageChunk(content=f"[Decode/Validation Error: {json_data_str[:50]}...]")
                            )
                    elif decoded_line.strip() == "[DONE]":
                        logger.debug("Stream received explicit [DONE] marker on its own line.")
                        if run_manager:
                            run_manager.on_llm_end(None)
                        break

        except requests.exceptions.HTTPError as e:
            # Handle 500 errors with automatic fallback for tool calls in streaming
            if e.response and e.response.status_code == 500 and has_tools:
                logger.warning(f"Received 500 error from GitHub Copilot API with model {self.model_name} when using tools (streaming)")
                logger.info(f"Attempting automatic fallback: retrying streaming request without tools for model {self.model_name}")

                if os.getenv("COPILOT_DEBUG", "").lower() in ("true", "1", "yes"):
                    print(f"DEBUG - 500 error with tools for model (streaming): {self.model_name}")
                    print(f"DEBUG - Retrying streaming without tools...")

                # Create a new payload without tools
                fallback_payload = payload.copy()
                fallback_payload.pop("tools", None)
                fallback_payload.pop("tool_choice", None)

                try:
                    logger.debug("Retrying streaming request without tools...")
                    fallback_response = self._sync_client.post(
                        COPILOT_CHAT_URL, headers=headers, json=fallback_payload, stream=True, timeout=self.request_timeout
                    )
                    fallback_response.raise_for_status()

                    logger.info(f"Fallback streaming successful: {self.model_name} responded without tools")
                    if os.getenv("COPILOT_DEBUG", "").lower() in ("true", "1", "yes"):
                        print(f"DEBUG - Fallback streaming successful for model: {self.model_name}")

                    # Process the fallback streaming response
                    for line in fallback_response.iter_lines():
                        if line:
                            decoded_line = line.decode("utf-8")
                            if decoded_line.startswith("data: "):
                                json_data_str = decoded_line[len("data: ") :].strip()
                                if not json_data_str:
                                    continue
                                if json_data_str == "[DONE]":
                                    logger.debug("Fallback stream received [DONE] marker.")
                                    if run_manager:
                                        run_manager.on_llm_end(None)
                                    break

                                try:
                                    chunk_data = json.loads(json_data_str)
                                    response_event = ResponseEvent.model_validate(chunk_data)
                                    choices = response_event.choices

                                    if not choices:
                                        logger.debug(f"Fallback stream chunk without 'choices': {chunk_data}")
                                        continue

                                    first_choice = choices[0]
                                    delta = first_choice.delta

                                    content_delta = delta.content if delta and delta.content is not None else ""
                                    finish_reason = first_choice.finish_reason
                                    generation_info = {}
                                    if finish_reason:
                                        generation_info["finish_reason"] = finish_reason

                                    chunk = ChatGenerationChunk(
                                        message=AIMessageChunk(content=content_delta),
                                        generation_info=generation_info if generation_info else None,
                                    )
                                    yield chunk
                                    if run_manager and content_delta:
                                        run_manager.on_llm_new_token(content_delta, chunk=chunk)

                                except (json.JSONDecodeError, ValidationError) as fallback_e:
                                    logger.warning(f"Failed to decode or validate JSON from fallback stream: {json_data_str}, Error: {fallback_e}")
                                    yield ChatGenerationChunk(
                                        message=AIMessageChunk(content=f"[Fallback Decode/Validation Error: {json_data_str[:50]}...]")
                                    )
                            elif decoded_line.strip() == "[DONE]":
                                logger.debug("Fallback stream received explicit [DONE] marker on its own line.")
                                if run_manager:
                                    run_manager.on_llm_end(None)
                                break
                    return  # Successfully handled with fallback

                except Exception as fallback_error:
                    logger.warning(f"Fallback streaming request failed: {fallback_error}")
                    if os.getenv("COPILOT_DEBUG", "").lower() in ("true", "1", "yes"):
                        print(f"DEBUG - Fallback streaming request failed: {fallback_error}")

            logger.error(f"HTTP error during Copilot stream: {e}")
            if run_manager:
                run_manager.on_llm_error(e)
            error_msg = f"HTTP error {e.response.status_code if e.response else 'N/A'}: {e.response.text if e.response else str(e)}"
            yield ChatGenerationChunk(message=AIMessageChunk(content=f"Stream Error: {error_msg}"))
        except Exception as e:
            logger.exception("Unexpected error during Copilot stream.")
            if run_manager:
                run_manager.on_llm_error(e)
            yield ChatGenerationChunk(message=AIMessageChunk(content=f"Unexpected Stream Error: {e}"))

    def get_models(self) -> List[Model]:
        """
        Fetches, filters, and sorts the list of available Copilot models.

        Returns:
            List of available Model objects, filtered and sorted.
        """
        self._ensure_auth(is_async=False)
        api_token = self._get_copilot_api_token()
        if not api_token:
            raise ValueError("Failed to obtain Copilot API token for fetching models.")

        headers = {
            "Authorization": f"Bearer {api_token.api_key.get_secret_value()}",
            "Editor-Version": self.editor_version,
            "Copilot-Integration-Id": self.copilot_integration_id,
            "Content-Type": "application/json",
            "Accept": "application/json",
        }

        try:
            response = self._sync_client.get(
                COPILOT_MODELS_URL,
                headers=headers,
                timeout=self.request_timeout
            )
            response.raise_for_status()

            # Parse the response using ModelSchema
            from .models import ModelSchema
            model_schema = ModelSchema.model_validate(response.json())
            all_models = model_schema.data

            # Apply filtering and sorting logic
            filtered_models = []
            seen_families = set()

            for model in all_models:
                if model.model_picker_enabled and \
                   (model.policy is None or model.policy.state == "enabled"):
                    if model.capabilities.family not in seen_families:
                        filtered_models.append(model)
                        seen_families.add(model.capabilities.family)

            # Sort by name
            filtered_models.sort(key=lambda m: m.name)

            # Move default model to front if present
            default_model = None
            for i, model in enumerate(filtered_models):
                if model.id == DEFAULT_MODEL_ID:
                    default_model = filtered_models.pop(i)
                    break
            if default_model:
                filtered_models.insert(0, default_model)

            self._models = filtered_models
            return self._models

        except requests.exceptions.RequestException as e:
            logger.error(f"HTTP error while fetching models: {e}")
            if hasattr(e, 'response') and e.response is not None:
                logger.error(f"Response content: {e.response.text}")
            raise
        except ValidationError as e:
            logger.error(f"Failed to validate model schema: {e}. Raw: {response.text if 'response' in locals() else 'N/A'}")
            raise

    def get_models_raw(self) -> Dict[str, Any]:
        """
        Fetches the raw JSON response from the models endpoint without filtering.

        Returns:
            Raw JSON response as a dictionary.
        """
        self._ensure_auth(is_async=False)
        api_token = self._get_copilot_api_token()
        if not api_token:
            raise ValueError("Failed to obtain Copilot API token for fetching models.")

        headers = {
            "Authorization": f"Bearer {api_token.api_key.get_secret_value()}",
            "Editor-Version": self.editor_version,
            "Copilot-Integration-Id": self.copilot_integration_id,
            "Content-Type": "application/json",
            "Accept": "application/json",
        }

        try:
            response = self._sync_client.get(
                COPILOT_MODELS_URL,
                headers=headers,
                timeout=self.request_timeout
            )
            response.raise_for_status()
            return response.json()

        except requests.exceptions.RequestException as e:
            logger.error(f"HTTP error while fetching models: {e}")
            if hasattr(e, 'response') and e.response is not None:
                logger.error(f"Response content: {e.response.text}")
            raise

