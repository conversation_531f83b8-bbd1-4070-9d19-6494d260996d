"""Graph-powered search functionality for the codebase.

This tool provides an agent-friendly interface for advanced codebase queries using the codebase's symbol and relationship graph.
Agents can issue simple queries (e.g., "foo") or add flags for more advanced searches (e.g., "--type=function --relationship=calls").
The tool handles parsing, intent detection, and graph traversal internally.

🚀 ULTIMATE CODEBASE TRAVERSAL FEATURES:
- Natural language query parsing
- Multi-modal search (symbol + text + semantic)
- Advanced graph traversal with path finding
- Smart filtering and scoping
- Contextual intelligence and pattern recognition
- Interactive exploration with follow-up suggestions
- Performance analytics and optimization hints
"""

import re
import json
from typing import Optional, List, Dict, Any, Tuple, Set
from langchain_core.messages import ToolMessage
from pydantic import Field

import difflib
import nltk
import os
from nltk.corpus import wordnet

from codegen.sdk.core.codebase import Codebase
from .observation import Observation
from .reveal_symbol import reveal_symbol, RevealSymbolObservation

# ---------------------------
# Enhanced Query Types
# ---------------------------

class SearchMode:
    """Different search modes for ultimate codebase traversal."""
    SYMBOL = "symbol"           # Find specific symbols
    TEXT = "text"              # Text-based search
    SEMANTIC = "semantic"      # Semantic/meaning-based search
    PATTERN = "pattern"        # Code pattern search
    IMPACT = "impact"          # Impact analysis
    PATH = "path"              # Find paths between symbols
    EXPLORE = "explore"        # Interactive exploration
    ANALYZE = "analyze"        # Code analysis and metrics
    ENTRY_POINTS = "entry_points"  # Find entry points and main functions
    POPULAR = "popular"        # Find most used/called symbols

class QueryIntent:
    """Enhanced query intent detection."""
    FIND = "find"              # Find symbols/code
    TRACE = "trace"            # Trace dependencies/usages
    ANALYZE = "analyze"        # Analyze code structure
    EXPLORE = "explore"        # Explore relationships
    COMPARE = "compare"        # Compare symbols/patterns
    OPTIMIZE = "optimize"      # Find optimization opportunities
    ONBOARD = "onboard"        # Onboarding and entry points

# ---------------------------
# Result Observation Classes
# ---------------------------

class GraphSearchObservation(Observation):
    """Response from graph-based search."""

    query: str = Field(description="The original search query or symbol name.")
    parsed_query: Dict[str, Any] = Field(description="Parsed query components (term, flags, etc.)")
    results: List[Dict[str, Any]] = Field(default_factory=list, description="List of matching symbols and their context.")
    search_metadata: Dict[str, Any] = Field(default_factory=dict, description="Search performance and context metadata.")
    suggestions: List[str] = Field(default_factory=list, description="Follow-up search suggestions.")

    def render(self, tool_call_id: str) -> ToolMessage:
        lines = [f"🔍 [ULTIMATE GRAPH SEARCH]: {self.query}"]
        
        # Add search metadata if available
        if self.search_metadata:
            metadata = self.search_metadata
            if metadata.get('search_mode'):
                lines.append(f"   Mode: {metadata['search_mode'].upper()}")
            if metadata.get('search_time'):
                lines.append(f"   Time: {metadata['search_time']:.2f}s")
            if metadata.get('symbols_scanned'):
                lines.append(f"   Scanned: {metadata['symbols_scanned']} symbols")
        
        if not self.results:
            lines.append("\n❌ No matches found.")
            if self.suggestions:
                lines.append(f"\n💡 Try these searches:")
                for suggestion in self.suggestions[:3]:
                    lines.append(f"   • {suggestion}")
        else:
            # Check for error results first
            error_results = [r for r in self.results if 'error' in r]
            if error_results:
                lines.append("\n❌ **SYMBOL NOT FOUND - EXPLORATION SUGGESTIONS**")
                for i, result in enumerate(error_results, 1):
                    lines.append(f"   {i}. ❌ {result['error']}")
                    
                    # Show suggestions with rich context
                    if 'suggestions' in result and result['suggestions']:
                        suggestions = result['suggestions']
                        lines.append(f"      🎯 **Similar symbols found ({len(suggestions)}):**")
                        
                        # Display rich suggestions with context
                        for j, suggestion in enumerate(suggestions[:6], 1):
                            if isinstance(suggestion, dict):
                                # Rich suggestion with context
                                name = suggestion.get('name', 'unknown')
                                filepath = suggestion.get('filepath', 'unknown')
                                symbol_type = suggestion.get('symbol_type', 'unknown')
                                preview = suggestion.get('preview', '')
                                
                                lines.append(f"         {j}. 🔹 **{name}** ({symbol_type})")
                                lines.append(f"            📁 {filepath}")
                                if preview:
                                    lines.append(f"            📝 {preview}")
                            else:
                                # Fallback for simple string suggestions
                                lines.append(f"         {j}. {suggestion}")
                        
                        if len(suggestions) > 6:
                            lines.append(f"         ... and {len(suggestions) - 6} more")
                    
                    # Show exploration suggestions if available
                    if 'exploration_suggestions' in result and result['exploration_suggestions']:
                        exp_suggestions = result['exploration_suggestions']
                        lines.append(f"      🔍 **Related patterns ({len(exp_suggestions)}):**")
                        for j, suggestion in enumerate(exp_suggestions[:4], 1):
                            lines.append(f"         {j}. {suggestion}")
                    
                    # Show fallback text search results
                    if 'fallback_results' in result and result['fallback_results']:
                        fallback = result['fallback_results']
                        lines.append(f"      📄 **Text search fallback ({len(fallback)}):**")
                        for j, fb_result in enumerate(fallback[:3], 1):
                            filepath = fb_result.get('filepath', 'unknown')
                            line_num = fb_result.get('line_number', '')
                            lines.append(f"         {j}. {filepath}:{line_num}")
                    
                    # Show search feedback
                    if 'search_feedback' in result:
                        lines.append(f"      ℹ️ **Search info:** {result['search_feedback']}")
                    
                    # Show exploration tips
                    if 'exploration_tips' in result and result['exploration_tips']:
                        lines.append(f"      💡 **Try these queries:**")
                        for tip in result['exploration_tips']:
                            lines.append(f"         • {tip}")
                
                lines.append("")
                return ToolMessage(
                    content="\n".join(lines),
                    status=self.status,
                    name="graph_search",
                    tool_call_id=tool_call_id,
                    artifact={
                        "query": self.query,
                        "parsed_query": self.parsed_query,
                        "results": self.results,
                        "search_metadata": self.search_metadata,
                        "suggestions": self.suggestions,
                    },
                )
            
            # Generate intelligent summary
            total_found = self.search_metadata.get('total_found', len(self.results))
            summary = generate_result_summary(self.results, total_found)
            lines.append(f"\n✅ {summary}")
            lines.append("")
            
            # Check if this is a focused query (specific flags)
            parsed_query = self.parsed_query or {}
            flags = parsed_query.get('flags', {})
            
            is_focused_query = any(flag.endswith('_only') or flag in ['dependencies', 'usages', 'analysis'] 
                                 for flag in flags.keys())
            
            if is_focused_query:
                # Show focused/categorized view for specific requests
                lines.extend(self._render_focused_view(flags))
            else:
                # Show unified entity view by default
                lines.extend(self._render_unified_entities())
        
        # Add follow-up suggestions
        if self.suggestions and self.results:
            lines.append("🎯 **NEXT STEPS & SUGGESTIONS**")
            for suggestion in self.suggestions[:3]:
                lines.append(f"   • {suggestion}")
            lines.append("")
        
        return ToolMessage(
            content="\n".join(lines),
            status=self.status,
            name="graph_search",
            tool_call_id=tool_call_id,
            artifact={
                "query": self.query,
                "parsed_query": self.parsed_query,
                "results": self.results,
                "search_metadata": self.search_metadata,
                "suggestions": self.suggestions,
            },
        )
    
    def _render_unified_entities(self) -> List[str]:
        """Render results as unified entities showing complete context."""
        lines = []
        
        # Group results by symbol/entity
        entities = self._group_results_by_entity()
        
        for i, (entity_name, entity_data) in enumerate(entities.items(), 1):
            lines.append(f"🎯 **{entity_name.upper()}** (Complete Entity)")
            
            # Definition/Location
            if entity_data.get('definition'):
                def_info = entity_data['definition']
                filepath = def_info.get('filepath', 'unknown')
                source = def_info.get('source', '')
                complexity = def_info.get('complexity', '')
                
                lines.append(f"   📍 **Definition:** {filepath}")
                if source:
                    # Show signature/header
                    source_lines = source.split('\n')
                    signature = source_lines[0] if source_lines else ''
                    if signature.strip():
                        lines.append(f"      📝 {signature.strip()}")
                if complexity:
                    lines.append(f"      📈 Complexity: {complexity}")
            
            # Usages/References
            if entity_data.get('usages'):
                usage_count = len(entity_data['usages'])
                usage_files = set(u.get('filepath', 'unknown') for u in entity_data['usages'])
                lines.append(f"   🔗 **Used by:** {usage_count} reference(s) across {len(usage_files)} file(s)")
                
                # Show top usages
                for usage in entity_data['usages'][:3]:
                    usage_name = usage.get('name', 'unknown')
                    usage_file = usage.get('filepath', 'unknown')
                    lines.append(f"      • {usage_name} in {usage_file}")
                
                if len(entity_data['usages']) > 3:
                    lines.append(f"      • ... and {len(entity_data['usages']) - 3} more")
            
            # Dependencies
            if entity_data.get('dependencies'):
                dep_count = len(entity_data['dependencies'])
                lines.append(f"   📦 **Depends on:** {dep_count} dependency(ies)")
                
                # Show top dependencies
                for dep in entity_data['dependencies'][:3]:
                    dep_name = dep.get('name', 'unknown')
                    dep_file = dep.get('filepath', 'unknown')
                    lines.append(f"      • {dep_name} from {dep_file}")
                
                if len(entity_data['dependencies']) > 3:
                    lines.append(f"      • ... and {len(entity_data['dependencies']) - 3} more")
            
            # Analysis/Metrics
            if entity_data.get('analysis'):
                for analysis in entity_data['analysis']:
                    analysis_type = analysis.get('analysis_type', 'Analysis')
                    lines.append(f"   📊 **{analysis_type.title()}:**")
                    if analysis.get('metrics'):
                        for key, value in analysis['metrics'].items():
                            lines.append(f"      • {key}: {value}")
            
            # Text matches (if any)
            if entity_data.get('text_matches'):
                match_count = len(entity_data['text_matches'])
                lines.append(f"   📄 **Text matches:** {match_count} occurrence(s)")
                for match in entity_data['text_matches'][:2]:
                    filepath = match.get('filepath', 'unknown')
                    line_num = match.get('line_number', 'unknown')
                    lines.append(f"      • {filepath}:{line_num}")
            
            # Connection paths
            if entity_data.get('paths'):
                lines.append(f"   🛤️ **Connections:**")
                for path in entity_data['paths'][:2]:
                    from_sym = path.get('from', 'unknown')
                    to_sym = path.get('to', 'unknown')
                    distance = path.get('distance', -1)
                    lines.append(f"      • {from_sym} → {to_sym} ({distance} step(s))")
            
            lines.append("")  # Space between entities
        
        return lines
    
    def _render_focused_view(self, flags: Dict[str, Any]) -> List[str]:
        """Render focused view based on specific flags."""
        lines = []
        categories = self._organize_results_by_category()
        
        # Show only requested categories
        if flags.get('dependencies') == 'true' or 'dependencies_only' in flags:
            if categories.get('dependencies'):
                lines.append("📦 **DEPENDENCIES**")
                for i, result in enumerate(categories['dependencies'], 1):
                    lines.extend(self._format_dependency_result(i, result))
                lines.append("")
        
        if flags.get('usages') == 'true' or 'usages_only' in flags:
            if categories.get('usages'):
                lines.append("🔗 **USAGES & REFERENCES**")
                for i, result in enumerate(categories['usages'], 1):
                    lines.extend(self._format_usage_result(i, result))
                lines.append("")
        
        if 'analysis_only' in flags or flags.get('analyze_complexity'):
            if categories.get('analysis'):
                lines.append("📊 **ANALYSIS & METRICS**")
                for i, result in enumerate(categories['analysis'], 1):
                    lines.extend(self._format_analysis_result(i, result))
                lines.append("")
        
        if 'text_only' in flags:
            if categories.get('text_matches'):
                lines.append("📄 **TEXT MATCHES**")
                for i, result in enumerate(categories['text_matches'], 1):
                    lines.extend(self._format_text_match_result(i, result))
                lines.append("")
        
        if 'paths_only' in flags:
            if categories.get('paths'):
                lines.append("🛤️ **CONNECTION PATHS**")
                for i, result in enumerate(categories['paths'], 1):
                    lines.extend(self._format_path_result(i, result))
                lines.append("")
        
        # If no specific flags matched, fall back to categorized view
        if not lines:
            lines.extend(self._render_categorized_view(categories))
        
        return lines
    
    def _render_categorized_view(self, categories: Dict[str, List[Dict[str, Any]]]) -> List[str]:
        """Render the original categorized view."""
        lines = []
        
        # 1. Symbol Definitions and Matches
        if categories.get('definitions') or categories.get('symbol_matches'):
            lines.append("📍 **SYMBOL DEFINITIONS & MATCHES**")
            
            # Show definitions first
            index = 1
            if categories.get('definitions'):
                for result in categories['definitions']:
                    lines.extend(self._format_definition_result(index, result))
                    index += 1
            
            # Show symbol matches (from wildcard searches)
            if categories.get('symbol_matches'):
                for result in categories['symbol_matches']:
                    lines.extend(self._format_definition_result(index, result))
                    index += 1
            
            lines.append("")
        
        # 2. Usages/References  
        if categories.get('usages'):
            lines.append("🔗 **USAGES & REFERENCES**")
            for i, result in enumerate(categories['usages'], 1):
                lines.extend(self._format_usage_result(i, result))
            lines.append("")
        
        # 3. Dependencies
        if categories.get('dependencies'):
            lines.append("📦 **DEPENDENCIES**")
            for i, result in enumerate(categories['dependencies'], 1):
                lines.extend(self._format_dependency_result(i, result))
            lines.append("")
        
        # 4. Analysis & Metrics
        if categories.get('analysis'):
            lines.append("📊 **ANALYSIS & METRICS**")
            for i, result in enumerate(categories['analysis'], 1):
                lines.extend(self._format_analysis_result(i, result))
            lines.append("")
        
        # 5. Text Matches
        if categories.get('text_matches'):
            lines.append("📄 **TEXT MATCHES**")
            for i, result in enumerate(categories['text_matches'], 1):
                lines.extend(self._format_text_match_result(i, result))
            lines.append("")
        
        # 6. Path Information
        if categories.get('paths'):
            lines.append("🛤️ **CONNECTION PATHS**")
            for i, result in enumerate(categories['paths'], 1):
                lines.extend(self._format_path_result(i, result))
            lines.append("")
        
        return lines
    
    def _group_results_by_entity(self) -> Dict[str, Dict[str, Any]]:
        """Group results by entity/symbol to create unified views."""
        entities = {}
        
        for result in self.results:
            # Determine the entity name
            entity_name = result.get('name', 'unknown')
            kind = result.get('kind', '').lower()
            
            # Initialize entity if not exists
            if entity_name not in entities:
                entities[entity_name] = {
                    'definition': None,
                    'usages': [],
                    'dependencies': [],
                    'analysis': [],
                    'text_matches': [],
                    'paths': []
                }
            
            # Categorize the result
            if kind in ['symbol', 'definition', 'symbol_match']:
                entities[entity_name]['definition'] = result
            elif kind == 'usage':
                entities[entity_name]['usages'].append(result)
            elif kind == 'dependency':
                entities[entity_name]['dependencies'].append(result)
            elif kind == 'analysis':
                entities[entity_name]['analysis'].append(result)
            elif kind == 'text_match':
                entities[entity_name]['text_matches'].append(result)
            elif kind == 'path':
                entities[entity_name]['paths'].append(result)
            else:
                # Default to dependencies for unknown types
                entities[entity_name]['dependencies'].append(result)
        
        return entities
    
    def _organize_results_by_category(self) -> Dict[str, List[Dict[str, Any]]]:
        """Organize results into logical categories."""
        categories = {
            'definitions': [],
            'symbol_matches': [],
            'usages': [],
            'dependencies': [],
            'analysis': [],
            'text_matches': [],
            'paths': []
        }
        
        for result in self.results:
            kind = result.get('kind', '').lower()
            
            if kind == 'symbol' or kind == 'definition':
                categories['definitions'].append(result)
            elif kind == 'symbol_match':
                categories['symbol_matches'].append(result)
            elif kind == 'usage':
                categories['usages'].append(result)
            elif kind == 'dependency':
                categories['dependencies'].append(result)
            elif kind == 'analysis':
                categories['analysis'].append(result)
            elif kind == 'text_match':
                categories['text_matches'].append(result)
            elif kind == 'path':
                categories['paths'].append(result)
            else:
                # Default to dependencies for unknown types
                categories['dependencies'].append(result)
        
        return categories
    
    def _format_definition_result(self, index: int, result: Dict[str, Any]) -> List[str]:
        """Format a symbol definition result with rich context."""
        lines = []
        name = result.get('name', 'unknown')
        filepath = result.get('filepath', 'unknown')
        source = result.get('source', '')
        symbol_type = result.get('symbol_type', '')
        line_number = result.get('line_number', '')
        
        # Enhanced header with type and location
        type_emoji = self._get_type_emoji(symbol_type)
        header = f"   {index}. {type_emoji} **{name}**"
        if symbol_type:
            header += f" ({symbol_type})"
        lines.append(header)
        
        if filepath and filepath != 'unknown':
            location = f"      📁 Location: {filepath}"
            if line_number and line_number != 'unknown':
                location += f":{line_number}"
            lines.append(location)
        
        if source:
            # Extract and show docstring if available
            docstring = self._extract_docstring(source)
            if docstring:
                lines.append(f"      📖 Description: {docstring}")
            
            # Show enhanced code preview
            preview = self._create_code_preview(source, symbol_type)
            if preview:
                lines.append(f"      📝 Code Preview:")
                for line in preview:
                    lines.append(f"         │ {line}")
        
        # Add complexity and usage metrics if available
        if result.get('complexity'):
            lines.append(f"      📈 Complexity: {result['complexity']}")
        
        # Add usage count if available
        usage_count = result.get('usage_count', 0)
        if usage_count > 0:
            lines.append(f"      🔗 Used {usage_count} time(s)")
        
        # Add expandable options
        lines.append(f"      🔍 Expand: '{name} --usages' | '{name} --dependencies' | 'analyze {name}'")
        
        return lines
    
    def _extract_docstring(self, source: str) -> str:
        """Extract docstring from source code."""
        if not source:
            return ""
        
        lines = source.split('\n')
        in_docstring = False
        docstring_lines = []
        quote_type = None
        
        for line in lines[1:]:  # Skip first line (def/class)
            stripped = line.strip()
            
            if not in_docstring:
                if stripped.startswith('"""') or stripped.startswith("'''"):
                    quote_type = stripped[:3]
                    in_docstring = True
                    # Handle single-line docstring
                    if stripped.endswith(quote_type) and len(stripped) > 6:
                        return stripped[3:-3].strip()
                    # Multi-line docstring
                    docstring_lines.append(stripped[3:])
                elif stripped and not stripped.startswith('#'):
                    break  # No docstring found
            else:
                if stripped.endswith(quote_type):
                    docstring_lines.append(stripped[:-3])
                    break
                docstring_lines.append(stripped)
        
        if docstring_lines:
            docstring = ' '.join(docstring_lines).strip()
            # Truncate if too long
            if len(docstring) > 100:
                return docstring[:97] + "..."
            return docstring
        
        return ""
    
    def _create_code_preview(self, source: str, symbol_type: str) -> List[str]:
        """Create an enhanced code preview based on symbol type."""
        if not source:
            return []
        
        lines = source.split('\n')
        preview_lines = []
        
        if symbol_type == 'class':
            # For classes, show class definition and key methods
            for i, line in enumerate(lines[:10]):
                stripped = line.strip()
                if stripped:
                    preview_lines.append(line)
                    if stripped.startswith('def ') and len(preview_lines) > 1:
                        preview_lines.append("    # ... more methods ...")
                        break
                    if len(preview_lines) >= 4:
                        break
        
        elif symbol_type in ['function', 'async_function']:
            # For functions, show signature and first few meaningful lines
            for i, line in enumerate(lines[:8]):
                stripped = line.strip()
                if stripped:
                    preview_lines.append(line)
                    # Stop after return statement or if we have enough context
                    if stripped.startswith('return ') or len(preview_lines) >= 5:
                        break
        
        else:
            # For other types, show first few non-empty lines
            for line in lines[:5]:
                if line.strip():
                    preview_lines.append(line)
                    if len(preview_lines) >= 3:
                        break
        
        return preview_lines
    
    def _get_type_emoji(self, symbol_type: str) -> str:
        """Get emoji for symbol type."""
        type_emojis = {
            'class': '🏗️',
            'function': '⚡',
            'async_function': '🔄',
            'variable': '📦',
            'import': '📥',
            'decorator': '🎯',
            'symbol_match': '🔍',
            'symbol': '🔹'
        }
        return type_emojis.get(symbol_type.lower(), '🔹')
    
    def _format_usage_result(self, index: int, result: Dict[str, Any]) -> List[str]:
        """Format a usage/reference result."""
        lines = []
        name = result.get('name', 'unknown')
        filepath = result.get('filepath', 'unknown')
        source = result.get('source', '')
        
        lines.append(f"   {index}. 🔗 **{name}**")
        if filepath and filepath != 'unknown':
            lines.append(f"      📁 Used in: {filepath}")
        
        if source:
            # Show context around the usage
            source_lines = source.split('\n')
            context_lines = source_lines[:2]  # Show first couple lines for context
            
            if context_lines:
                lines.append(f"      📝 Context:")
                for line in context_lines:
                    if line.strip():
                        lines.append(f"         │ {line}")
        
        return lines
    
    def _format_dependency_result(self, index: int, result: Dict[str, Any]) -> List[str]:
        """Format a dependency result."""
        lines = []
        name = result.get('name', 'unknown')
        filepath = result.get('filepath', 'unknown')
        source = result.get('source', '')
        
        lines.append(f"   {index}. 📦 **{name}**")
        if filepath and filepath != 'unknown':
            lines.append(f"      📁 From: {filepath}")
        
        if source:
            # Show import statement or brief definition
            source_lines = source.split('\n')
            import_line = source_lines[0] if source_lines else ''
            
            if import_line.strip():
                lines.append(f"      📝 Import: {import_line.strip()}")
        
        return lines
    
    def _format_analysis_result(self, index: int, result: Dict[str, Any]) -> List[str]:
        """Format an analysis result."""
        lines = []
        analysis_type = result.get('analysis_type', 'unknown')
        
        lines.append(f"   {index}. 📊 **{analysis_type.upper()} ANALYSIS**")
        
        if result.get('metrics'):
            lines.append(f"      📈 Metrics:")
            for key, value in result['metrics'].items():
                lines.append(f"         • {key}: {value}")
        
        return lines
    
    def _format_text_match_result(self, index: int, result: Dict[str, Any]) -> List[str]:
        """Format a text match result."""
        lines = []
        filepath = result.get('filepath', 'unknown')
        line_number = result.get('line_number', 'unknown')
        line_content = result.get('line_content', '')
        
        lines.append(f"   {index}. 📄 **{filepath}:{line_number}**")
        if line_content:
            lines.append(f"      📝 Match: {line_content.strip()}")
        
        return lines
    
    def _format_path_result(self, index: int, result: Dict[str, Any]) -> List[str]:
        """Format a path/connection result."""
        lines = []
        from_symbol = result.get('from', 'unknown')
        to_symbol = result.get('to', 'unknown')
        path_info = result.get('path', [])
        distance = result.get('distance', -1)
        
        lines.append(f"   {index}. 🛤️ **{from_symbol} → {to_symbol}**")
        if distance >= 0:
            lines.append(f"      📏 Distance: {distance} step(s)")
        
        if path_info:
            lines.append(f"      🗺️ Path: {' → '.join(path_info)}")
        
        return lines

# ---------------------------
# Query Parsing
# ---------------------------

# Legacy functions kept for backward compatibility
def parse_query(query: str) -> Tuple[str, Dict[str, Any]]:
    """Legacy query parser - use parse_enhanced_query instead."""
    term, flags, _, _ = parse_enhanced_query(query)
    return term, flags

def detect_intent(query: str, flags: Dict[str, Any]) -> Dict[str, Any]:
    """Legacy intent detection - use detect_enhanced_intent instead."""
    return detect_enhanced_intent(query, flags)

# ---------------------------
# Graph Traversal (Stub)
# ---------------------------

def has_wordnet():
    """Check if WordNet data is available locally."""
    NLTK_DATA_PATH = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../nltk_data'))
    if NLTK_DATA_PATH not in nltk.data.path:
        nltk.data.path.append(NLTK_DATA_PATH)
    try:
        nltk.corpus.wordnet.ensure_loaded()
        return True
    except LookupError:
        return False

def determine_symbol_type(source: str) -> str:
    """Determine the type of symbol from its source code."""
    if not source:
        return "unknown"
    
    source_lower = source.lower().strip()
    
    if source_lower.startswith('class '):
        return "class"
    elif source_lower.startswith('def '):
        return "function"
    elif source_lower.startswith('async def '):
        return "async_function"
    elif 'import ' in source_lower:
        return "import"
    elif '=' in source_lower and 'def ' not in source_lower and 'class ' not in source_lower:
        return "variable"
    elif source_lower.startswith('@'):
        return "decorator"
    else:
        return "symbol"

def perform_wildcard_search(codebase: Codebase, pattern: str) -> List[Dict[str, Any]]:
    """Perform wildcard pattern search across symbol names."""
    import fnmatch
    
    results = []
    pattern_lower = pattern.lower()
    
    # Find all symbols matching the pattern
    matching_symbols = []
    for symbol in codebase.symbols:
        if fnmatch.fnmatch(symbol.name.lower(), pattern_lower):
            matching_symbols.append(symbol)
    
    # Sort by relevance (exact prefix matches first, then others)
    base_pattern = pattern_lower.replace('*', '').replace('?', '')
    matching_symbols.sort(key=lambda s: (
        not s.name.lower().startswith(base_pattern),  # Prefix matches first
        len(s.name),  # Shorter names first
        s.name.lower()  # Alphabetical
    ))
    
    # Convert to result format with rich context
    for symbol in matching_symbols[:15]:  # Limit results
        # Determine symbol type from source
        symbol_type = determine_symbol_type(symbol.source)
        
        results.append({
            "kind": "symbol_match",
            "name": symbol.name,
            "filepath": symbol.file.filepath if symbol.file else "unknown",
            "source": symbol.source,
            "symbol_type": symbol_type,
            "line_number": symbol.start_point.row + 1 if hasattr(symbol, 'start_point') and symbol.start_point else 'unknown',
            "complexity": calculate_symbol_complexity(symbol),
            "summary": f"{symbol_type.title()}: {symbol.name} in {symbol.file.filepath if symbol.file else 'unknown'}"
        })
    
    return results

def suggest_similar_symbols(codebase: Codebase, query: str, n=3, cutoff=0.6) -> List[Dict[str, Any]]:
    """Suggest similar symbols with rich context including file paths, types, and previews."""
    
    # Create symbol lookup for rich context
    symbol_lookup = {s.name: s for s in codebase.symbols}
    all_symbol_names = list(symbol_lookup.keys())
    
    # Use difflib for fuzzy matching with lower cutoff for more suggestions
    fuzzy_matches = difflib.get_close_matches(query, all_symbol_names, n=n*2, cutoff=max(0.3, cutoff-0.2))
    
    # Add partial matches (symbols containing the query)
    partial_matches = [s for s in all_symbol_names if query.lower() in s.lower() and s not in fuzzy_matches]
    
    # Add case-insensitive exact matches
    case_matches = [s for s in all_symbol_names if s.lower() == query.lower() and s not in fuzzy_matches]
    
    # Add symbols that start with the query
    prefix_matches = [s for s in all_symbol_names if s.lower().startswith(query.lower()) and s not in fuzzy_matches and s not in partial_matches]
    
    # Add semantic matches for common patterns
    semantic_matches = find_semantic_matches(codebase, query, all_symbol_names)
    
    # Use enhanced code-specific synonyms instead of WordNet
    synonym_matches = []
    code_synonyms = get_code_synonyms(query)
    for synonym in code_synonyms:
        # Find symbols that contain the synonym
        matches = [s for s in all_symbol_names if synonym.lower() in s.lower() and s not in fuzzy_matches and s not in prefix_matches]
        synonym_matches.extend(matches[:3])  # Limit matches per synonym
    
    # Combine and deduplicate, prioritizing exact matches
    all_suggestion_names = case_matches + fuzzy_matches + prefix_matches + partial_matches[:3] + semantic_matches + synonym_matches
    unique_names = list(dict.fromkeys(all_suggestion_names))[:n*2]  # Remove duplicates while preserving order
    
    # Convert to rich suggestion objects
    suggestions = []
    for name in unique_names:
        if name in symbol_lookup:
            symbol = symbol_lookup[name]
            symbol_type = determine_symbol_type(symbol.source)
            
            # Create preview (first line of source)
            preview = ""
            if symbol.source:
                first_line = symbol.source.split('\n')[0].strip()
                if len(first_line) > 80:
                    preview = first_line[:77] + "..."
                else:
                    preview = first_line
            
            suggestions.append({
                "name": name,
                "filepath": symbol.file.filepath if symbol.file else "unknown",
                "symbol_type": symbol_type,
                "preview": preview,
                "line_number": symbol.start_point.row + 1 if hasattr(symbol, 'start_point') and symbol.start_point else 'unknown',
                "complexity": calculate_symbol_complexity(symbol)
            })
    
    return suggestions

def find_semantic_matches(codebase: Codebase, query: str, all_symbols: List[str]) -> List[str]:
    """Find semantically related symbols using enhanced code-specific synonyms."""
    query_lower = query.lower()
    semantic_matches = []
    
    # Get code-specific synonyms
    synonyms = get_code_synonyms(query)
    
    # Find symbols containing these synonyms
    for symbol in all_symbols:
        symbol_lower = symbol.lower()
        
        # Check if any synonym appears in the symbol name
        for synonym in synonyms:
            if synonym in symbol_lower and symbol not in semantic_matches:
                semantic_matches.append(symbol)
                break
        
        # Also check if the query appears in the symbol (partial match)
        if query_lower in symbol_lower and symbol not in semantic_matches:
            semantic_matches.append(symbol)
    
    return semantic_matches[:10]  # Increased limit for better coverage

def is_text_search_query(term: str) -> bool:
    """Check if the query is a general text search rather than a specific symbol search."""
    # Check for wildcard patterns
    if '*' in term or '?' in term:
        return True
    
    # Check for boolean operators
    if any(op in term.upper() for op in [" OR ", " AND ", " NOT "]):
        return True
    
    # Check for multiple terms (space-separated)
    if len(term.split()) > 1:
        return True
    
    # Check for common search terms that are unlikely to be symbol names
    search_terms = {
        "todo", "fixme", "hack", "bug", "error", "exception", "warning", 
        "traceback", "catch", "try", "except", "raise", "assert", "fail",
        "broken", "crash", "def", "class", "import", "function", "method"
    }
    if term.lower() in search_terms:
        return True
    
    return False

def perform_text_search(codebase: Codebase, query: str) -> List[Dict[str, Any]]:
    """Perform text-based search across the codebase using ripgrep-style search."""
    from .search import search
    
    results = []
    
    # Handle wildcard patterns specially
    if '*' in query or '?' in query:
        return perform_wildcard_search(codebase, query)
    
    try:
        # Use the existing search tool for text-based queries
        search_result = search(codebase, query, page=1, files_per_page=20)
        
        if search_result.status == "success" and search_result.results:
            for file_result in search_result.results:
                for match in file_result.matches:
                    results.append({
                        "kind": "text_match",
                        "filepath": file_result.filepath,
                        "line_number": match.line_number,
                        "line_content": match.line,
                        "summary": f"Match in {file_result.filepath}:{match.line_number}: {match.line.strip()}"
                    })
        
        # If no results, try individual terms from boolean queries
        if not results and (" OR " in query.upper() or " AND " in query.upper()):
            # Extract individual terms from boolean query
            terms = re.split(r'\s+(?:OR|AND|NOT)\s+', query, flags=re.IGNORECASE)
            for term in terms:
                term = term.strip()
                if term and len(term) > 2:  # Skip very short terms
                    try:
                        term_result = search(codebase, term, page=1, files_per_page=5)
                        if term_result.status == "success" and term_result.results:
                            for file_result in term_result.results[:2]:  # Limit results per term
                                for match in file_result.matches[:2]:  # Limit matches per file
                                    results.append({
                                        "kind": "text_match",
                                        "filepath": file_result.filepath,
                                        "line_number": match.line_number,
                                        "line_content": match.line,
                                        "summary": f"'{term}' in {file_result.filepath}:{match.line_number}: {match.line.strip()}"
                                    })
                    except Exception:
                        continue  # Skip failed searches for individual terms
    
    except Exception as e:
        results.append({
            "error": f"Text search failed: {str(e)}",
            "summary": f"Text search failed: {str(e)}"
        })
    
    return results

# Legacy function - use perform_enhanced_graph_search instead
def perform_graph_search(codebase: Codebase, term: str, intent: Dict[str, Any]) -> List[Dict[str, Any]]:
    """Legacy search function - use perform_enhanced_graph_search instead."""
    results, _ = perform_enhanced_graph_search(codebase, term, intent, SearchMode.SYMBOL, QueryIntent.FIND)
    return results

# ---------------------------
# Enhanced Query Parsing
# ---------------------------

def parse_enhanced_query(query: str) -> Tuple[str, Dict[str, Any], str, str]:
    """
    Enhanced query parsing with natural language understanding.
    
    Returns:
        (main_term, flags, search_mode, intent)
    
    Examples:
        "find all usages of AgentGraph" -> ("AgentGraph", {"usages": "true"}, "symbol", "trace")
        "show me classes that inherit from BaseModel" -> ("BaseModel", {"type": "class", "relationship": "inheritance"}, "pattern", "explore")
        "analyze the complexity of the authentication module" -> ("authentication", {"type": "module"}, "analyze", "analyze")
        "trace the path from User to Database" -> ("User Database", {"to": "Database"}, "path", "trace")
    """
    original_query = query.lower().strip()
    
    # Extract explicit flags first
    flag_pattern = re.compile(r"--(\w+)=([^\s]+)")
    flags = dict(flag_pattern.findall(query))
    query_wo_flags = flag_pattern.sub("", query).strip()
    
    # Support boolean flags without values
    boolean_flags = ["usages", "dependencies", "deep", "all", "recursive", "verbose"]
    for flag in boolean_flags:
        if f"--{flag}" in query:
            flags[flag] = "true"
            query_wo_flags = query_wo_flags.replace(f"--{flag}", "").strip()
    
    # Natural language intent detection
    intent = QueryIntent.FIND  # default
    search_mode = SearchMode.SYMBOL  # default
    
    # Intent patterns
    if any(word in original_query for word in ["find", "search", "look for", "show me"]):
        intent = QueryIntent.FIND
    elif any(word in original_query for word in ["trace", "track", "follow", "path"]):
        intent = QueryIntent.TRACE
        if "path" in original_query or "from" in original_query:
            search_mode = SearchMode.PATH
    elif any(word in original_query for word in ["analyze", "analysis", "complexity", "metrics"]):
        intent = QueryIntent.ANALYZE
        search_mode = SearchMode.ANALYZE
    elif any(word in original_query for word in ["explore", "discover", "browse"]):
        intent = QueryIntent.EXPLORE
        search_mode = SearchMode.EXPLORE
    elif any(word in original_query for word in ["compare", "difference", "similar"]):
        intent = QueryIntent.COMPARE
    
    # Search mode patterns
    if any(word in original_query for word in ["text", "content", "contains", "matches"]):
        search_mode = SearchMode.TEXT
    elif any(word in original_query for word in ["pattern", "structure", "architecture"]):
        search_mode = SearchMode.PATTERN
    elif any(word in original_query for word in ["impact", "affected", "changes"]):
        search_mode = SearchMode.IMPACT
    elif any(word in original_query for word in ["entry", "main", "start", "entry point", "entry points"]):
        search_mode = SearchMode.ENTRY_POINTS
    elif any(word in original_query for word in ["popular", "most used", "frequently", "common"]):
        search_mode = SearchMode.POPULAR
    elif "all classes" in original_query or "list classes" in original_query:
        search_mode = SearchMode.SYMBOL
        flags["type"] = "class"
        flags["list_all"] = "true"
    elif "all functions" in original_query or "list functions" in original_query:
        search_mode = SearchMode.SYMBOL
        flags["type"] = "function"
        flags["list_all"] = "true"
    
    # Auto-detect flags from natural language
    if any(word in original_query for word in ["usage", "used", "uses", "references"]):
        flags["usages"] = "true"
    if any(word in original_query for word in ["depend", "import", "require"]):
        flags["dependencies"] = "true"
    if any(word in original_query for word in ["deep", "recursive", "all levels"]):
        flags["depth"] = "5"
    if any(word in original_query for word in ["class", "classes"]):
        flags["type"] = "class"
    elif any(word in original_query for word in ["function", "functions", "method", "methods"]):
        flags["type"] = "function"
    elif any(word in original_query for word in ["variable", "variables"]):
        flags["type"] = "variable"
    
    # Extract main search term
    # Remove common words and extract the actual symbol/term
    stop_words = {
        "find", "search", "show", "me", "all", "the", "of", "in", "from", "to", "with",
        "that", "which", "where", "how", "what", "usage", "usages", "dependencies",
        "classes", "functions", "methods", "variables", "trace", "path", "analyze"
    }
    
    words = query_wo_flags.split()
    main_terms = [word for word in words if word.lower() not in stop_words and len(word) > 1]
    main_term = " ".join(main_terms) if main_terms else query_wo_flags
    
    return main_term, flags, search_mode, intent

def detect_enhanced_intent(query: str, flags: Dict[str, Any]) -> Dict[str, Any]:
    """Enhanced intent detection with contextual understanding."""
    intent_data = flags.copy()
    
    # Add semantic context
    if "complexity" in query.lower():
        intent_data["analyze_complexity"] = "true"
    if "performance" in query.lower():
        intent_data["analyze_performance"] = "true"
    if "security" in query.lower():
        intent_data["analyze_security"] = "true"
    
    return intent_data

# ---------------------------
# Advanced Search Functions
# ---------------------------

def perform_path_search(codebase: Codebase, from_symbol: str, to_symbol: str) -> List[Dict[str, Any]]:
    """Find paths between two symbols in the dependency graph."""
    results = []
    
    try:
        # Get both symbols
        from_symbols = codebase.get_symbols(symbol_name=from_symbol)
        to_symbols = codebase.get_symbols(symbol_name=to_symbol)
        
        if not from_symbols:
            return [{"error": f"Source symbol '{from_symbol}' not found"}]
        if not to_symbols:
            return [{"error": f"Target symbol '{to_symbol}' not found"}]
        
        # Simple path finding (can be enhanced with graph algorithms)
        from_sym = from_symbols[0]
        to_sym = to_symbols[0]
        
        # Check direct dependency
        for dep in from_sym.dependencies:
            if dep.name == to_symbol:
                results.append({
                    "kind": "path",
                    "from": from_symbol,
                    "to": to_symbol,
                    "path": [from_symbol, to_symbol],
                    "distance": 1,
                    "summary": f"Direct dependency: {from_symbol} → {to_symbol}"
                })
        
        # Check reverse dependency (usage)
        for usage in from_sym.usages:
            if usage.usage_symbol.name == to_symbol:
                results.append({
                    "kind": "path",
                    "from": from_symbol,
                    "to": to_symbol,
                    "path": [from_symbol, to_symbol],
                    "distance": 1,
                    "summary": f"Direct usage: {from_symbol} ← {to_symbol}"
                })
        
        if not results:
            results.append({
                "kind": "path",
                "from": from_symbol,
                "to": to_symbol,
                "path": [],
                "distance": -1,
                "summary": f"No direct path found between {from_symbol} and {to_symbol}"
            })
    
    except Exception as e:
        results.append({"error": f"Path search failed: {str(e)}"})
    
    return results

def perform_analysis_search(codebase: Codebase, term: str, analysis_type: str = "general") -> List[Dict[str, Any]]:
    """Perform code analysis and return metrics."""
    results = []
    
    try:
        symbols = codebase.get_symbols(symbol_name=term) if term else codebase.symbols
        
        if not symbols and term:
            return [{"error": f"Symbol '{term}' not found for analysis"}]
        
        # Entry points analysis
        if analysis_type == "entry_points":
            entry_points = find_entry_points(codebase)
            for ep in entry_points:
                results.append({
                    "kind": "analysis",
                    "analysis_type": "entry_point",
                    "name": ep["name"],
                    "filepath": ep["filepath"],
                    "source": ep["source"],
                    "symbol_type": ep["type"],
                    "entry_type": ep["entry_type"],
                    "summary": f"Entry point: {ep['name']} ({ep['entry_type']})"
                })
        
        # Popular symbols analysis
        elif analysis_type == "popular":
            popular_symbols = find_popular_symbols(codebase)
            for symbol_info in popular_symbols:
                results.append({
                    "kind": "analysis",
                    "analysis_type": "popular_symbol",
                    "name": symbol_info["name"],
                    "filepath": symbol_info["filepath"],
                    "source": symbol_info["source"],
                    "symbol_type": symbol_info["type"],
                    "usage_count": symbol_info["usage_count"],
                    "summary": f"Popular: {symbol_info['name']} (used {symbol_info['usage_count']} times)"
                })
        
        # General analysis
        elif analysis_type == "general" or not analysis_type:
            total_symbols = len(codebase.symbols)
            total_files = len(set(s.file.filepath for s in codebase.symbols if s.file))
            
            if term and symbols:
                symbol = symbols[0]
                dep_count = len(symbol.dependencies)
                usage_count = len(symbol.usages)
                complexity = calculate_symbol_complexity(symbol)
                
                results.append({
                    "kind": "analysis",
                    "analysis_type": "symbol_analysis",
                    "symbol": term,
                    "metrics": {
                        "dependencies": dep_count,
                        "usages": usage_count,
                        "complexity": complexity,
                        "file": symbol.file.filepath if symbol.file else "unknown"
                    },
                    "summary": f"Analysis of {term}: {dep_count} deps, {usage_count} usages, complexity {complexity}"
                })
            else:
                results.append({
                    "kind": "analysis",
                    "analysis_type": "codebase_overview",
                    "metrics": {
                        "total_symbols": total_symbols,
                        "total_files": total_files,
                        "avg_dependencies": sum(len(s.dependencies) for s in codebase.symbols) / total_symbols if total_symbols > 0 else 0
                    },
                    "summary": f"Codebase overview: {total_symbols} symbols across {total_files} files"
                })
    
    except Exception as e:
        results.append({"error": f"Analysis failed: {str(e)}"})
    
    return results

def find_entry_points(codebase: Codebase) -> List[Dict[str, Any]]:
    """Find entry points and main functions in the codebase."""
    entry_points = []
    
    for symbol in codebase.symbols:
        if not symbol.source:
            continue
        
        name = symbol.name.lower()
        source = symbol.source.lower()
        filepath = symbol.file.filepath if symbol.file else ""
        
        # Check for main functions
        if name == "main" or name.endswith("_main"):
            entry_points.append({
                "name": symbol.name,
                "filepath": filepath,
                "source": symbol.source,
                "type": determine_symbol_type(symbol.source),
                "entry_type": "main_function"
            })
        
        # Check for CLI entry points
        elif "if __name__ == '__main__'" in source:
            entry_points.append({
                "name": symbol.name,
                "filepath": filepath,
                "source": symbol.source,
                "type": "script",
                "entry_type": "cli_script"
            })
        
        # Check for FastAPI/Flask apps
        elif any(pattern in source for pattern in ["app = fastapi", "app = flask", "@app.route"]):
            entry_points.append({
                "name": symbol.name,
                "filepath": filepath,
                "source": symbol.source,
                "type": determine_symbol_type(symbol.source),
                "entry_type": "web_app"
            })
        
        # Check for test runners
        elif name.startswith("test_") and "def test_" in source:
            entry_points.append({
                "name": symbol.name,
                "filepath": filepath,
                "source": symbol.source,
                "type": "function",
                "entry_type": "test_entry"
            })
    
    return entry_points[:10]  # Limit results

def find_popular_symbols(codebase: Codebase) -> List[Dict[str, Any]]:
    """Find the most used/referenced symbols in the codebase."""
    symbol_usage_count = {}
    
    # Count usages for each symbol
    for symbol in codebase.symbols:
        usage_count = len(symbol.usages)
        if usage_count > 0:
            symbol_usage_count[symbol.name] = {
                "symbol": symbol,
                "usage_count": usage_count
            }
    
    # Sort by usage count
    sorted_symbols = sorted(
        symbol_usage_count.items(),
        key=lambda x: x[1]["usage_count"],
        reverse=True
    )
    
    popular_symbols = []
    for name, info in sorted_symbols[:10]:  # Top 10
        symbol = info["symbol"]
        popular_symbols.append({
            "name": symbol.name,
            "filepath": symbol.file.filepath if symbol.file else "unknown",
            "source": symbol.source,
            "type": determine_symbol_type(symbol.source),
            "usage_count": info["usage_count"]
        })
    
    return popular_symbols

def list_symbols_by_type(codebase: Codebase, symbol_type: str) -> List[Dict[str, Any]]:
    """List all symbols of a specific type (class, function, etc.)."""
    results = []
    
    try:
        matching_symbols = []
        
        for symbol in codebase.symbols:
            if not symbol.source:
                continue
                
            detected_type = determine_symbol_type(symbol.source)
            if detected_type.lower() == symbol_type.lower():
                matching_symbols.append(symbol)
        
        # Sort by name for consistent ordering
        matching_symbols.sort(key=lambda s: s.name.lower())
        
        # Convert to result format
        for symbol in matching_symbols[:50]:  # Limit to 50 for readability
            results.append({
                "kind": "symbol",
                "name": symbol.name,
                "filepath": symbol.file.filepath if symbol.file else "unknown",
                "source": symbol.source,
                "symbol_type": determine_symbol_type(symbol.source),
                "line_number": symbol.start_point.row + 1 if hasattr(symbol, 'start_point') and symbol.start_point else 'unknown',
                "complexity": calculate_symbol_complexity(symbol),
                "usage_count": len(symbol.usages) if hasattr(symbol, 'usages') else 0,
                "summary": f"{symbol_type.title()}: {symbol.name} in {symbol.file.filepath if symbol.file else 'unknown'}"
            })
        
        # Add summary result
        if results:
            total_count = len(matching_symbols)
            shown_count = len(results)
            results.insert(0, {
                "kind": "analysis",
                "analysis_type": f"list_{symbol_type}s",
                "name": f"All {symbol_type.title()}s",
                "summary": f"Found {total_count} {symbol_type}(s) in codebase (showing {shown_count})",
                "metrics": {
                    "total_found": total_count,
                    "shown": shown_count,
                    "type": symbol_type
                }
            })
    
    except Exception as e:
        results.append({"error": f"Failed to list {symbol_type}s: {str(e)}"})
    
    return results

def calculate_symbol_complexity(symbol) -> str:
    """Calculate a simple complexity metric for a symbol."""
    try:
        if not symbol.source:
            return "unknown"
        
        lines = len(symbol.source.split('\n'))
        deps = len(symbol.dependencies)
        
        if lines < 10 and deps < 3:
            return "low"
        elif lines < 50 and deps < 10:
            return "medium"
        else:
            return "high"
    except:
        return "unknown"

def generate_follow_up_suggestions(query: str, results: List[Dict[str, Any]], search_mode: str) -> List[str]:
    """Generate intelligent follow-up search suggestions."""
    suggestions = []
    
    # Based on search mode
    if search_mode == SearchMode.SYMBOL and results:
        first_result = results[0]
        if first_result.get('name'):
            name = first_result['name']
            suggestions.extend([
                f"{name} --usages",
                f"{name} --dependencies --depth=2",
                f"analyze {name}"
            ])
    
    elif search_mode == SearchMode.TEXT:
        suggestions.extend([
            f"find classes related to {query}",
            f"analyze patterns in {query}",
            f"explore {query} dependencies"
        ])
    
    elif search_mode == SearchMode.ANALYZE:
        suggestions.extend([
            f"find usages of {query}",
            f"explore {query} patterns",
            f"trace {query} dependencies"
        ])
    
    # Generic suggestions
    if not suggestions:
        suggestions.extend([
            f"explore {query}",
            f"find patterns in {query}",
            f"analyze {query} complexity"
        ])
    
    return suggestions[:3]

# ---------------------------
# Legacy Main Entry Point (kept for backward compatibility)
# ---------------------------

def perform_enhanced_graph_search(
    codebase: Codebase,
    term: str,
    intent: Dict[str, Any],
    search_mode: str,
    query_intent: str,
    max_results: int = 15
) -> Tuple[List[Dict[str, Any]], Dict[str, Any]]:
    """
    Enhanced search function that handles multiple search modes and provides metadata.
    """
    import time
    start_time = time.time()
    
    results = []
    metadata = {
        "search_mode": search_mode,
        "query_intent": query_intent,
        "symbols_scanned": 0
    }
    
    try:
        # Handle different search modes
        if search_mode == SearchMode.PATH:
            # Extract from/to symbols for path search
            if " to " in term.lower():
                parts = term.lower().split(" to ")
                if len(parts) == 2:
                    from_sym, to_sym = parts[0].strip(), parts[1].strip()
                    results = perform_path_search(codebase, from_sym, to_sym)
                else:
                    results = [{"error": "Path search requires format: 'symbol1 to symbol2'"}]
            else:
                results = [{"error": "Path search requires format: 'symbol1 to symbol2'"}]
        
        elif search_mode == SearchMode.ANALYZE:
            # Perform analysis
            analysis_type = "general"
            if intent.get("analyze_complexity"):
                analysis_type = "complexity"
            elif intent.get("analyze_performance"):
                analysis_type = "performance"
            
            results = perform_analysis_search(codebase, term, analysis_type)
        
        elif search_mode == SearchMode.TEXT:
            # Text-based search
            results = perform_text_search(codebase, term)
            metadata["search_mode"] = SearchMode.TEXT
        
        elif search_mode == SearchMode.EXPLORE:
            # Enhanced exploration mode
            results = perform_exploration_search(codebase, term, intent)
        
        elif search_mode == SearchMode.ENTRY_POINTS:
            # Find entry points and main functions
            results = perform_analysis_search(codebase, term, "entry_points")
        
        elif search_mode == SearchMode.POPULAR:
            # Find most used symbols
            results = perform_analysis_search(codebase, term, "popular")
        
        else:
            # Default symbol-based search with enhancements
            results = perform_symbol_search(codebase, term, intent)
            metadata["symbols_scanned"] = len(codebase.symbols)
    
    except Exception as e:
        results = [{"error": f"Search failed: {str(e)}"}]
    
    # Store total results count before filtering
    total_results = len(results)
    metadata["total_found"] = total_results
    
    # Apply intelligent filtering and ranking
    if results and not any('error' in r for r in results):
        results = filter_and_rank_results(results, term, max_results=max_results, search_mode=search_mode)
        metadata["filtered_count"] = len(results)
    
    # Add timing metadata
    metadata["search_time"] = time.time() - start_time
    
    return results, metadata

def perform_exploration_search(codebase: Codebase, term: str, intent: Dict[str, Any]) -> List[Dict[str, Any]]:
    """Enhanced exploration that shows related symbols and patterns."""
    results = []
    
    try:
        # Find the main symbol
        symbols = codebase.get_symbols(symbol_name=term) if term else []
        
        if not symbols and term:
            # Try fuzzy matching
            suggestions = suggest_similar_symbols(codebase, term, n=5)
            if suggestions:
                results.append({
                    "error": f"Symbol '{term}' not found",
                    "suggestions": suggestions,
                    "summary": f"Symbol '{term}' not found. Try: {', '.join(suggestions)}"
                })
            return results
        
        if symbols:
            symbol = symbols[0]
            
            # Add the main symbol
            results.append({
                "kind": "symbol",
                "name": symbol.name,
                "filepath": symbol.file.filepath if symbol.file else "unknown",
                "source": symbol.source,
                "complexity": calculate_symbol_complexity(symbol),
                "relationships": symbol.dependencies + symbol.usages,
                "summary": f"Main symbol: {symbol.name}"
            })
            
            # Add related symbols (dependencies and usages)
            for dep in symbol.dependencies[:3]:  # Limit to top 3
                results.append({
                    "kind": "dependency",
                    "name": dep.name,
                    "filepath": dep.filepath,
                    "source": dep.source,
                    "summary": f"Dependency: {dep.name}"
                })
            
            for usage in symbol.usages[:3]:  # Limit to top 3
                results.append({
                    "kind": "usage",
                    "name": usage.name,
                    "filepath": usage.filepath,
                    "source": usage.source,
                    "summary": f"Usage: {usage.name}"
                })
        
        # If no specific symbol, show codebase overview
        if not results:
            results = perform_analysis_search(codebase, "", "general")
    
    except Exception as e:
        results.append({"error": f"Exploration failed: {str(e)}"})
    
    return results

def perform_symbol_search(codebase: Codebase, term: str, intent: Dict[str, Any]) -> List[Dict[str, Any]]:
    """Enhanced symbol search with better filtering and context."""
    symbol_name = term
    filepath = intent.get("file") or intent.get("filepath")
    max_depth = int(intent.get("depth", 1))
    collect_dependencies = intent.get("dependencies", "true").lower() != "false"
    collect_usages = intent.get("usages", "false").lower() == "true"
    
    # Enhanced filtering
    symbol_type = intent.get("type")  # class, function, variable, etc.
    list_all = intent.get("list_all", "false").lower() == "true"
    
    # Handle "all classes" or "all functions" queries
    if list_all and symbol_type:
        return list_symbols_by_type(codebase, symbol_type)

    obs: RevealSymbolObservation = reveal_symbol(
        codebase=codebase,
        symbol_name=symbol_name,
        filepath=filepath,
        max_depth=max_depth,
        collect_dependencies=collect_dependencies,
        collect_usages=collect_usages,
    )

    results = []
    if obs.status == "success":
        if collect_dependencies and obs.dependencies:
            for dep in obs.dependencies:
                # Apply type filtering if specified
                if symbol_type and not matches_symbol_type(dep, symbol_type):
                    continue
                    
                results.append({
                    "kind": "dependency",
                    "name": dep.name,
                    "filepath": dep.filepath,
                    "source": dep.source,
                    "symbol_type": determine_symbol_type(dep.source),
                    "complexity": calculate_symbol_complexity(dep),
                    "usage_count": len(dep.usages) if hasattr(dep, 'usages') else 0,
                    "summary": f"Dependency: {dep.name} in {dep.filepath}"
                })
        
        if collect_usages and obs.usages:
            for usage in obs.usages:
                # Apply type filtering if specified
                if symbol_type and not matches_symbol_type(usage, symbol_type):
                    continue
                    
                results.append({
                    "kind": "usage",
                    "name": usage.name,
                    "filepath": usage.filepath,
                    "source": usage.source,
                    "symbol_type": determine_symbol_type(usage.source),
                    "complexity": calculate_symbol_complexity(usage),
                    "usage_count": len(usage.usages) if hasattr(usage, 'usages') else 0,
                    "summary": f"Usage: {usage.name} in {usage.filepath}"
                })
    else:
        # Enhanced error handling with richer suggestions and fallback
        if obs.error and ("not found" in obs.error or "ambiguous" in obs.error):
            rich_suggestions = suggest_similar_symbols(codebase, symbol_name, n=5)
            
            # Add exploration suggestions based on the query
            exploration_suggestions = generate_exploration_suggestions(codebase, symbol_name)
            
            # Try fallback text search if no good suggestions
            fallback_results = []
            if len(rich_suggestions) < 3:
                try:
                    fallback_results = perform_text_search(codebase, symbol_name)
                    if fallback_results:
                        fallback_results = fallback_results[:3]  # Limit fallback results
                except:
                    pass
            
            # Convert rich suggestions to simple names for exploration tips
            suggestion_names = [s["name"] for s in rich_suggestions] + exploration_suggestions
            
            if rich_suggestions or exploration_suggestions or fallback_results:
                error_result = {
                    "error": obs.error,
                    "suggestions": rich_suggestions,  # Rich suggestions with context
                    "exploration_suggestions": exploration_suggestions,  # Simple name suggestions
                    "fallback_results": fallback_results,  # Text search fallback
                    "search_feedback": f"Searched {len(codebase.symbols)} symbols for '{symbol_name}'",
                    "exploration_tips": [
                        f"Try: 'all classes' or 'all functions' to explore",
                        f"Try wildcard: '{symbol_name.lower()}*'",
                        f"Try type filter: '{symbol_name} --type=class'",
                        f"Try text search: 'find {symbol_name} in code'"
                    ],
                    "summary": f"{obs.error} Found {len(suggestion_names)} alternatives + {len(fallback_results)} text matches"
                }
                
                # Add helpful context about what was searched
                if not rich_suggestions and not exploration_suggestions:
                    error_result["exploration_tips"].insert(0, "No similar symbols found - this might be a new concept")
                
                results.append(error_result)
            else:
                results.append({
                    "error": obs.error, 
                    "search_feedback": f"Searched {len(codebase.symbols)} symbols for '{symbol_name}' - no matches",
                    "summary": obs.error
                })
        else:
            results.append({"error": obs.error})

    return results

def generate_exploration_suggestions(codebase: Codebase, query: str) -> List[str]:
    """Generate exploration suggestions based on common patterns and the query."""
    suggestions = []
    query_lower = query.lower()
    
    # Common patterns based on the query
    patterns = {
        'auth': ['authentication', 'authorize', 'login', 'token', 'session', 'user', 'password'],
        'user': ['account', 'profile', 'auth', 'login', 'session'],
        'api': ['endpoint', 'route', 'handler', 'controller', 'service'],
        'db': ['database', 'model', 'table', 'query', 'connection'],
        'test': ['spec', 'mock', 'fixture', 'assert', 'expect'],
        'config': ['settings', 'environment', 'env', 'configuration'],
        'util': ['helper', 'tool', 'common', 'shared'],
        'error': ['exception', 'fail', 'catch', 'handle']
    }
    
    # Find related patterns
    for pattern, related_terms in patterns.items():
        if pattern in query_lower or query_lower in pattern:
            suggestions.extend(related_terms[:3])
    
    # Add common variations
    if query_lower.endswith('s'):
        suggestions.append(query_lower[:-1])  # Remove plural
    else:
        suggestions.append(query_lower + 's')  # Add plural
    
    # Add common prefixes/suffixes
    common_variations = [
        f"{query_lower}_handler",
        f"{query_lower}_service", 
        f"{query_lower}_controller",
        f"{query_lower}_model",
        f"get_{query_lower}",
        f"create_{query_lower}",
        f"update_{query_lower}",
        f"delete_{query_lower}"
    ]
    
    # Check which variations actually exist in the codebase
    all_symbols = [s.name.lower() for s in codebase.symbols]
    existing_variations = [var for var in common_variations if any(var in symbol for symbol in all_symbols)]
    
    suggestions.extend(existing_variations[:3])
    
    # Remove duplicates and return
    return list(dict.fromkeys(suggestions))[:5]

def matches_symbol_type(symbol, symbol_type: str) -> bool:
    """Check if a symbol matches the specified type."""
    if not symbol_type:
        return True
    
    # Simple type matching based on source code patterns
    source = symbol.source.lower() if symbol.source else ""
    
    if symbol_type.lower() == "class":
        return "class " in source
    elif symbol_type.lower() == "function":
        return "def " in source and "class " not in source
    elif symbol_type.lower() == "method":
        return "def " in source and "class " in source
    elif symbol_type.lower() == "variable":
        return "=" in source and "def " not in source and "class " not in source
    
    return True

# ---------------------------
# Result Filtering and Ranking
# ---------------------------

def calculate_relevance_score(result: Dict[str, Any], query: str) -> float:
    """Calculate relevance score for a search result."""
    score = 0.0
    
    # Base score by result type
    kind = result.get('kind', '').lower()
    if kind == 'dependency':
        score += 0.8  # Dependencies are usually important
    elif kind == 'usage':
        score += 0.6  # Usages are moderately important
    elif kind == 'text_match':
        score += 0.4  # Text matches are less important
    elif kind == 'analysis':
        score += 1.0  # Analysis results are very important
    elif kind == 'path':
        score += 0.9  # Path results are very important
    
    # Boost score based on name similarity to query
    name = result.get('name', '').lower()
    query_lower = query.lower()
    if name == query_lower:
        score += 1.0  # Exact match
    elif query_lower in name:
        score += 0.5  # Partial match
    elif any(word in name for word in query_lower.split()):
        score += 0.3  # Word match
    
    # Boost score for shorter, more focused results
    source = result.get('source', '')
    if source:
        lines = len(source.split('\n'))
        if lines < 10:
            score += 0.2  # Short, focused code
        elif lines > 100:
            score -= 0.1  # Very long code is less relevant
    
    # Boost score for files in main source directories
    filepath = result.get('filepath', '')
    if filepath:
        if '/src/' in filepath and not '/test' in filepath:
            score += 0.3  # Main source code
        elif '/test' in filepath:
            score -= 0.2  # Test code is less relevant
    
    return score

def deduplicate_results(results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Remove duplicate results based on name and filepath."""
    seen = set()
    deduplicated = []
    
    for result in results:
        # Create a key for deduplication
        name = result.get('name', '')
        filepath = result.get('filepath', '')
        kind = result.get('kind', '')
        
        # For text matches, include line number to avoid over-deduplication
        if kind == 'text_match':
            line_num = result.get('line_number', '')
            key = f"{name}:{filepath}:{line_num}"
        else:
            key = f"{name}:{filepath}:{kind}"
        
        if key not in seen:
            seen.add(key)
            deduplicated.append(result)
    
    return deduplicated

def filter_and_rank_results(
    results: List[Dict[str, Any]], 
    query: str, 
    max_results: int = 15,
    search_mode: str = SearchMode.SYMBOL
) -> List[Dict[str, Any]]:
    """Filter, deduplicate, rank and limit results for optimal agent consumption."""
    
    if not results:
        return results
    
    # Step 1: Deduplicate results
    results = deduplicate_results(results)
    
    # Step 2: Calculate relevance scores
    for result in results:
        result['_relevance_score'] = calculate_relevance_score(result, query)
    
    # Step 3: Sort by relevance score (highest first)
    results.sort(key=lambda x: x.get('_relevance_score', 0), reverse=True)
    
    # Step 4: Apply intelligent limits based on search mode
    if search_mode == SearchMode.TEXT:
        # For text search, limit to fewer results but group by file
        max_results = min(max_results, 8)
        file_groups = {}
        for result in results:
            filepath = result.get('filepath', 'unknown')
            if filepath not in file_groups:
                file_groups[filepath] = []
            file_groups[filepath].append(result)
        
        # Take top 2 results per file, up to max_results
        filtered = []
        for filepath, file_results in file_groups.items():
            filtered.extend(file_results[:2])  # Max 2 per file
            if len(filtered) >= max_results:
                break
        results = filtered[:max_results]
    
    elif search_mode == SearchMode.SYMBOL:
        # For symbol search, be more generous but still limit
        max_results = min(max_results, 12)
        
        # Prioritize: analysis > symbols > dependencies > usages
        analysis_results = [r for r in results if r.get('kind') == 'analysis']
        symbol_results = [r for r in results if r.get('kind') == 'symbol']
        dependency_results = [r for r in results if r.get('kind') == 'dependency']
        usage_results = [r for r in results if r.get('kind') == 'usage']
        
        # Take top results from each category
        filtered = []
        filtered.extend(analysis_results[:2])  # Max 2 analysis
        filtered.extend(symbol_results[:8])  # Max 8 symbols (main results)
        filtered.extend(dependency_results[:3])  # Max 3 dependencies
        filtered.extend(usage_results[:3])  # Max 3 usages
        
        results = filtered[:max_results]
    
    elif search_mode == SearchMode.ANALYZE:
        # For analysis, keep all results but limit to reasonable number
        max_results = min(max_results, 5)
        results = results[:max_results]
    
    else:
        # Default limiting
        results = results[:max_results]
    
    # Step 5: Remove the internal relevance score
    for result in results:
        result.pop('_relevance_score', None)
    
    return results

def generate_result_summary(results: List[Dict[str, Any]], total_found: int) -> str:
    """Generate a concise summary of results for the agent."""
    if not results:
        return "No matches found."
    
    shown = len(results)
    if total_found > shown:
        summary = f"Showing top {shown} of {total_found} results"
    else:
        summary = f"Found {shown} result(s)"
    
    # Add breakdown by type
    type_counts = {}
    for result in results:
        kind = result.get('kind', 'unknown')
        type_counts[kind] = type_counts.get(kind, 0) + 1
    
    if len(type_counts) > 1:
        breakdown = ", ".join([f"{count} {kind}" for kind, count in type_counts.items()])
        summary += f" ({breakdown})"
    
    return summary

# ---------------------------
# Main Entry Point (Enhanced)
# ---------------------------

def graph_search(
    codebase: Codebase,
    query: str,
    max_results: int = 15,
) -> GraphSearchObservation:
    """
    🚀 ULTIMATE GRAPH SEARCH - Enhanced codebase traversal with natural language understanding.
    
    🔍 **BASIC QUERIES (Most Reliable):**
        "ClassName" - Find specific class definitions
        "function_name" - Find specific functions
        "all classes" - List all classes in codebase
        "all functions" - List all functions in codebase
        "auth*" - Wildcard patterns for symbols starting with 'auth'
    
    📊 **ANALYSIS QUERIES:**
        "find entry points" - Main functions, CLI scripts, web apps
        "show popular symbols" - Most used functions/classes
        "explore codebase structure" - High-level overview
    
    🛤️ **ADVANCED QUERIES:**
        "SymbolName --usages" - Find where symbol is used
        "SymbolName --dependencies" - Find what symbol depends on
        "SymbolName --type=class" - Filter by symbol type
        "trace path from A to B" - Find dependency paths
    
    💡 **TIPS FOR SUCCESS:**
        - Start with exact symbol names for best results
        - Use "all classes" or "all functions" to explore
        - Add --type=class/function/variable to filter
        - Try wildcard patterns like "auth*" or "*user*"
        - If no results, tool will suggest alternatives and fallback to text search
    """
    # Use enhanced query parsing
    term, flags, search_mode, intent = parse_enhanced_query(query)
    
    # Enhanced intent detection
    enhanced_intent = detect_enhanced_intent(query, flags)
    
    try:
        # Perform enhanced search
        results, metadata = perform_enhanced_graph_search(
            codebase, term, enhanced_intent, search_mode, intent, max_results
        )
        
        # Apply max_results limit to the enhanced search
        metadata["max_results"] = max_results
        
        # Generate intelligent follow-up suggestions
        suggestions = generate_follow_up_suggestions(query, results, search_mode)
        
        status = "success"
        error = None
    except Exception as e:
        results = []
        metadata = {"error": str(e)}
        suggestions = []
        status = "error"
        error = str(e)

    return GraphSearchObservation(
        status=status,
        error=error,
        query=query,
        parsed_query={
            "term": term, 
            "flags": flags, 
            "search_mode": search_mode,
            "intent": intent,
            "enhanced_intent": enhanced_intent
        },
        results=results,
        search_metadata=metadata,
        suggestions=suggestions,
    )

# Enhanced code-specific synonym expansion
def get_code_synonyms(term: str) -> List[str]:
    """
    Get code-specific synonyms that understand programming terminology,
    naming conventions, and domain-specific vocabulary.
    """
    term_lower = term.lower()
    synonyms = set()
    
    # Programming concept synonyms
    concept_synonyms = {
        # Authentication & Authorization
        'auth': ['authentication', 'authorize', 'login', 'signin', 'credential', 'token', 'session', 'security'],
        'login': ['signin', 'auth', 'authenticate', 'logon', 'access'],
        'user': ['account', 'profile', 'member', 'person', 'client', 'customer', 'actor'],
        'password': ['pwd', 'pass', 'secret', 'credential', 'key'],
        'token': ['jwt', 'bearer', 'auth', 'credential', 'session'],
        
        # Data & Database
        'db': ['database', 'data', 'storage', 'persistence', 'repo', 'repository'],
        'model': ['entity', 'schema', 'table', 'record', 'data', 'object'],
        'query': ['search', 'find', 'select', 'filter', 'lookup'],
        'save': ['persist', 'store', 'write', 'insert', 'create'],
        'load': ['fetch', 'get', 'retrieve', 'read', 'find'],
        'delete': ['remove', 'destroy', 'drop', 'clear'],
        'update': ['modify', 'change', 'edit', 'patch'],
        
        # API & Web
        'api': ['endpoint', 'route', 'service', 'interface', 'gateway'],
        'request': ['req', 'call', 'invoke', 'fetch'],
        'response': ['resp', 'result', 'reply', 'return'],
        'handler': ['controller', 'processor', 'manager', 'service'],
        'middleware': ['interceptor', 'filter', 'guard', 'plugin'],
        'route': ['path', 'endpoint', 'url', 'mapping'],
        
        # Error Handling
        'error': ['exception', 'err', 'failure', 'fault', 'issue'],
        'catch': ['handle', 'trap', 'except'],
        'throw': ['raise', 'emit', 'trigger'],
        'validate': ['check', 'verify', 'assert', 'test'],
        
        # Configuration & Settings
        'config': ['configuration', 'settings', 'options', 'params', 'env', 'environment'],
        'setting': ['option', 'param', 'config', 'preference'],
        'env': ['environment', 'config', 'variable'],
        
        # Utilities & Helpers
        'util': ['utility', 'helper', 'tool', 'common', 'shared'],
        'helper': ['util', 'utility', 'assistant', 'support'],
        'manager': ['handler', 'controller', 'service', 'coordinator'],
        'factory': ['builder', 'creator', 'generator', 'maker'],
        'provider': ['supplier', 'source', 'factory', 'service'],
        
        # Testing
        'test': ['spec', 'check', 'verify', 'assert', 'mock', 'stub'],
        'mock': ['fake', 'stub', 'dummy', 'test'],
        'fixture': ['setup', 'data', 'sample', 'test'],
        
        # File & I/O
        'file': ['document', 'resource', 'asset', 'data'],
        'upload': ['import', 'load', 'transfer', 'send'],
        'download': ['export', 'fetch', 'retrieve', 'get'],
        'path': ['route', 'location', 'url', 'directory'],
        
        # Logging & Monitoring
        'log': ['logger', 'logging', 'record', 'trace'],
        'debug': ['trace', 'log', 'inspect', 'examine'],
        'monitor': ['watch', 'track', 'observe', 'check'],
        
        # Processing & Operations
        'process': ['handle', 'execute', 'run', 'perform'],
        'execute': ['run', 'invoke', 'call', 'perform'],
        'transform': ['convert', 'map', 'change', 'modify'],
        'parse': ['analyze', 'decode', 'interpret', 'read'],
        'serialize': ['encode', 'stringify', 'format'],
        'deserialize': ['decode', 'parse', 'read'],
        
        # State & Lifecycle
        'init': ['initialize', 'setup', 'create', 'start'],
        'destroy': ['cleanup', 'dispose', 'remove', 'delete'],
        'start': ['begin', 'init', 'launch', 'run'],
        'stop': ['end', 'halt', 'terminate', 'close'],
        'reset': ['clear', 'restart', 'refresh'],
        
        # Data Structures
        'list': ['array', 'collection', 'sequence', 'items'],
        'map': ['dict', 'dictionary', 'hash', 'table'],
        'set': ['collection', 'group', 'unique'],
        'queue': ['buffer', 'stack', 'pipeline'],
        'cache': ['buffer', 'store', 'memory', 'temp'],
        
        # Async & Concurrency
        'async': ['asynchronous', 'concurrent', 'parallel', 'await'],
        'await': ['wait', 'async', 'promise', 'future'],
        'promise': ['future', 'async', 'deferred'],
        'thread': ['worker', 'task', 'job', 'process'],
        'lock': ['mutex', 'semaphore', 'sync', 'guard'],
        
        # Architecture & Patterns
        'service': ['provider', 'manager', 'handler', 'component'],
        'component': ['module', 'part', 'element', 'widget'],
        'module': ['component', 'package', 'library', 'unit'],
        'plugin': ['extension', 'addon', 'module', 'component'],
        'adapter': ['wrapper', 'bridge', 'converter', 'translator'],
        'proxy': ['wrapper', 'delegate', 'representative'],
        'observer': ['listener', 'watcher', 'subscriber'],
        'subscriber': ['listener', 'observer', 'consumer'],
        'publisher': ['emitter', 'broadcaster', 'producer'],
    }
    
    # Add direct synonyms
    if term_lower in concept_synonyms:
        synonyms.update(concept_synonyms[term_lower])
    
    # Add reverse lookups (if term appears as a synonym)
    for key, values in concept_synonyms.items():
        if term_lower in values:
            synonyms.add(key)
            synonyms.update(values)
    
    # Naming convention variations
    naming_variations = generate_naming_variations(term)
    synonyms.update(naming_variations)
    
    # Remove the original term
    synonyms.discard(term)
    synonyms.discard(term_lower)
    
    return list(synonyms)

def generate_naming_variations(term: str) -> List[str]:
    """
    Generate common naming convention variations for a term.
    """
    variations = set()
    
    # Convert between different naming conventions
    # camelCase <-> snake_case <-> kebab-case <-> PascalCase
    
    # Split on common separators
    parts = re.split(r'[_\-\s]+|(?=[A-Z])', term)
    parts = [p.lower() for p in parts if p]
    
    if len(parts) > 1:
        # snake_case
        variations.add('_'.join(parts))
        # kebab-case
        variations.add('-'.join(parts))
        # camelCase
        if len(parts) > 1:
            variations.add(parts[0] + ''.join(p.capitalize() for p in parts[1:]))
        # PascalCase
        variations.add(''.join(p.capitalize() for p in parts))
        # lowercase
        variations.add(''.join(parts))
        # UPPERCASE
        variations.add('_'.join(parts).upper())
    
    # Common prefixes and suffixes
    base_term = term.lower()
    
    # Add/remove common prefixes
    prefixes = ['get', 'set', 'is', 'has', 'can', 'should', 'will', 'do', 'make', 'create', 'build', 'find', 'search']
    for prefix in prefixes:
        if base_term.startswith(prefix):
            # Remove prefix
            without_prefix = base_term[len(prefix):]
            if without_prefix:
                variations.add(without_prefix)
        else:
            # Add prefix
            variations.add(f"{prefix}{base_term.capitalize()}")
            variations.add(f"{prefix}_{base_term}")
    
    # Add/remove common suffixes
    suffixes = ['er', 'or', 'manager', 'handler', 'service', 'controller', 'provider', 'factory', 'builder', 'helper', 'util']
    for suffix in suffixes:
        if base_term.endswith(suffix):
            # Remove suffix
            without_suffix = base_term[:-len(suffix)]
            if without_suffix:
                variations.add(without_suffix)
        else:
            # Add suffix
            variations.add(f"{base_term}{suffix}")
            variations.add(f"{base_term}_{suffix}")
    
    # Plural/singular variations
    if base_term.endswith('s') and len(base_term) > 3:
        variations.add(base_term[:-1])  # Remove 's'
    else:
        variations.add(f"{base_term}s")  # Add 's'
    
    # Common abbreviations
    abbreviations = {
        'authentication': ['auth'],
        'configuration': ['config', 'cfg'],
        'database': ['db'],
        'repository': ['repo'],
        'application': ['app'],
        'information': ['info'],
        'parameter': ['param'],
        'argument': ['arg'],
        'function': ['func', 'fn'],
        'variable': ['var'],
        'temporary': ['temp', 'tmp'],
        'utilities': ['utils', 'util'],
        'manager': ['mgr'],
        'controller': ['ctrl'],
        'service': ['svc'],
        'request': ['req'],
        'response': ['resp', 'res'],
        'exception': ['exc', 'ex'],
        'message': ['msg'],
        'document': ['doc'],
        'identifier': ['id'],
        'number': ['num', 'nr'],
        'string': ['str'],
        'boolean': ['bool'],
        'integer': ['int'],
        'character': ['char'],
    }
    
    # Add abbreviations
    for full, abbrevs in abbreviations.items():
        if full in base_term:
            for abbrev in abbrevs:
                variations.add(base_term.replace(full, abbrev))
        for abbrev in abbrevs:
            if abbrev in base_term:
                variations.add(base_term.replace(abbrev, full))
    
    return list(variations)



 