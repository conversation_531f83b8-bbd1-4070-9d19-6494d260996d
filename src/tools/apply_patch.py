"""Tool for applying patches to files with precise line-based modifications."""

import os
import re
import difflib
from typing import Any, ClassVar, List, Dict, Optional, Union
from pydantic import Field

from codegen.sdk.core.codebase import Codebase
from .observation import Observation


class ApplyPatchObservation(Observation):
    """Response from applying a patch to a file."""

    filepath: str = Field(description="Path to the file that was patched")
    changes_applied: int = Field(description="Number of changes successfully applied")
    changes_failed: int = Field(description="Number of changes that failed to apply")
    backup_created: bool = Field(default=False, description="Whether a backup was created")
    preview: Optional[str] = Field(default=None, description="Preview of changes made")
    warnings: List[str] = Field(default_factory=list, description="Any warnings during patch application")

    str_template: ClassVar[str] = "Applied {changes_applied} changes to {filepath}"

    def _get_details(self) -> Dict[str, Any]:
        return {
            "changes_applied": self.changes_applied,
            "filepath": self.filepath,
            "changes_failed": self.changes_failed,
            "backup_created": self.backup_created
        }


class PatchChange:
    """Represents a single change in a patch."""
    
    def __init__(self, 
                 change_type: str,
                 line_number: Optional[int] = None,
                 old_content: Optional[str] = None,
                 new_content: Optional[str] = None,
                 context_lines: int = 3):
        self.change_type = change_type  # 'replace', 'insert', 'delete', 'append'
        self.line_number = line_number
        self.old_content = old_content
        self.new_content = new_content
        self.context_lines = context_lines
        self.applied = False
        self.error = None


def create_backup(filepath: str) -> str:
    """Create a backup of the file before applying patches."""
    backup_path = f"{filepath}.backup"
    counter = 1
    
    # Find a unique backup filename
    while os.path.exists(backup_path):
        backup_path = f"{filepath}.backup.{counter}"
        counter += 1
    
    try:
        with open(filepath, 'r', encoding='utf-8') as original:
            content = original.read()
        
        with open(backup_path, 'w', encoding='utf-8') as backup:
            backup.write(content)
        
        return backup_path
    except Exception as e:
        raise Exception(f"Failed to create backup: {str(e)}")


def find_best_match_line(lines: List[str], target_content: str, start_line: int = 0, context_lines: int = 3) -> Optional[int]:
    """Find the best matching line for the target content, considering context."""
    target_lines = target_content.strip().split('\n')
    
    if not target_lines:
        return None
    
    # Try exact match first
    for i in range(start_line, len(lines)):
        if lines[i].strip() == target_lines[0].strip():
            # Check if subsequent lines match
            match = True
            for j, target_line in enumerate(target_lines):
                if i + j >= len(lines) or lines[i + j].strip() != target_line.strip():
                    match = False
                    break
            if match:
                return i
    
    # Try fuzzy matching with difflib
    target_text = '\n'.join(target_lines)
    best_ratio = 0.0
    best_line = None
    
    for i in range(start_line, len(lines) - len(target_lines) + 1):
        candidate_lines = lines[i:i + len(target_lines)]
        candidate_text = '\n'.join(line.strip() for line in candidate_lines)
        
        ratio = difflib.SequenceMatcher(None, target_text, candidate_text).ratio()
        if ratio > best_ratio and ratio > 0.8:  # 80% similarity threshold
            best_ratio = ratio
            best_line = i
    
    return best_line


def apply_replace_change(lines: List[str], change: PatchChange) -> bool:
    """Apply a replace change to the lines."""
    if not change.old_content or not change.new_content:
        change.error = "Replace change requires both old_content and new_content"
        return False
    
    # Find the line to replace
    if change.line_number is not None:
        # Try exact line number first
        start_search = max(0, change.line_number - 1)
    else:
        start_search = 0
    
    match_line = find_best_match_line(lines, change.old_content, start_search, change.context_lines)
    
    if match_line is None:
        change.error = f"Could not find matching content: {change.old_content[:50]}..."
        return False
    
    # Replace the content
    old_lines = change.old_content.strip().split('\n')
    new_lines = change.new_content.strip().split('\n')
    
    # Remove old lines
    for _ in range(len(old_lines)):
        if match_line < len(lines):
            lines.pop(match_line)
    
    # Insert new lines
    for i, new_line in enumerate(new_lines):
        lines.insert(match_line + i, new_line)
    
    change.applied = True
    return True


def apply_insert_change(lines: List[str], change: PatchChange) -> bool:
    """Apply an insert change to the lines."""
    if not change.new_content:
        change.error = "Insert change requires new_content"
        return False
    
    insert_line = change.line_number or len(lines)
    
    # Ensure line number is valid
    if insert_line < 0:
        insert_line = 0
    elif insert_line > len(lines):
        insert_line = len(lines)
    
    new_lines = change.new_content.strip().split('\n')
    
    # Insert new lines
    for i, new_line in enumerate(new_lines):
        lines.insert(insert_line + i, new_line)
    
    change.applied = True
    return True


def apply_delete_change(lines: List[str], change: PatchChange) -> bool:
    """Apply a delete change to the lines."""
    if not change.old_content:
        change.error = "Delete change requires old_content"
        return False
    
    # Find the line to delete
    if change.line_number is not None:
        start_search = max(0, change.line_number - 1)
    else:
        start_search = 0
    
    match_line = find_best_match_line(lines, change.old_content, start_search, change.context_lines)
    
    if match_line is None:
        change.error = f"Could not find content to delete: {change.old_content[:50]}..."
        return False
    
    # Delete the lines
    old_lines = change.old_content.strip().split('\n')
    for _ in range(len(old_lines)):
        if match_line < len(lines):
            lines.pop(match_line)
    
    change.applied = True
    return True


def apply_append_change(lines: List[str], change: PatchChange) -> bool:
    """Apply an append change to the lines."""
    if not change.new_content:
        change.error = "Append change requires new_content"
        return False
    
    new_lines = change.new_content.strip().split('\n')
    lines.extend(new_lines)
    
    change.applied = True
    return True


def parse_unified_diff(diff_content: str) -> List[PatchChange]:
    """Parse a unified diff format into PatchChange objects."""
    changes = []
    lines = diff_content.split('\n')
    
    current_old_line = 0
    current_new_line = 0
    
    i = 0
    while i < len(lines):
        line = lines[i]
        
        # Parse hunk header
        if line.startswith('@@'):
            # Extract line numbers from @@ -old_start,old_count +new_start,new_count @@
            match = re.match(r'@@ -(\d+)(?:,(\d+))? \+(\d+)(?:,(\d+))? @@', line)
            if match:
                current_old_line = int(match.group(1))
                current_new_line = int(match.group(3))
            i += 1
            continue
        
        # Parse changes
        if line.startswith('-'):
            # Deletion
            old_content = line[1:]
            # Look for corresponding addition
            if i + 1 < len(lines) and lines[i + 1].startswith('+'):
                # This is a replacement
                new_content = lines[i + 1][1:]
                changes.append(PatchChange(
                    change_type='replace',
                    line_number=current_old_line,
                    old_content=old_content,
                    new_content=new_content
                ))
                i += 2
                current_old_line += 1
                current_new_line += 1
            else:
                # Pure deletion
                changes.append(PatchChange(
                    change_type='delete',
                    line_number=current_old_line,
                    old_content=old_content
                ))
                i += 1
                current_old_line += 1
        
        elif line.startswith('+'):
            # Addition
            new_content = line[1:]
            changes.append(PatchChange(
                change_type='insert',
                line_number=current_new_line,
                new_content=new_content
            ))
            i += 1
            current_new_line += 1
        
        else:
            # Context line
            i += 1
            current_old_line += 1
            current_new_line += 1
    
    return changes


def apply_patch(
    codebase: Codebase,
    filepath: str,
    changes: Union[List[PatchChange], str],
    create_backup: bool = True,
    dry_run: bool = False
) -> ApplyPatchObservation:
    """
    Apply a patch to a file with precise line-based modifications.
    
    Args:
        codebase: The codebase instance
        filepath: Path to the file to patch
        changes: List of PatchChange objects or unified diff string
        create_backup: Whether to create a backup before applying changes
        dry_run: If True, don't actually modify the file, just validate changes
    
    Returns:
        ApplyPatchObservation with results of the patch application
    """
    
    # Resolve the full file path
    if not os.path.isabs(filepath):
        full_path = os.path.join(codebase.repo_path, filepath)
    else:
        full_path = filepath
    
    if not os.path.exists(full_path):
        return ApplyPatchObservation(
            status="error",
            error=f"File not found: {filepath}",
            filepath=filepath,
            changes_applied=0,
            changes_failed=1
        )
    
    # Parse changes if provided as string
    if isinstance(changes, str):
        try:
            changes = parse_unified_diff(changes)
        except Exception as e:
            return ApplyPatchObservation(
                status="error",
                error=f"Failed to parse diff: {str(e)}",
                filepath=filepath,
                changes_applied=0,
                changes_failed=1
            )
    
    # Read the file
    try:
        with open(full_path, 'r', encoding='utf-8') as f:
            original_content = f.read()
        lines = original_content.split('\n')
    except Exception as e:
        return ApplyPatchObservation(
            status="error",
            error=f"Failed to read file: {str(e)}",
            filepath=filepath,
            changes_applied=0,
            changes_failed=1
        )
    
    # Create backup if requested and not dry run
    backup_created = False
    backup_path = None
    if create_backup and not dry_run:
        try:
            backup_path = create_backup(full_path)
            backup_created = True
        except Exception as e:
            return ApplyPatchObservation(
                status="error",
                error=f"Failed to create backup: {str(e)}",
                filepath=filepath,
                changes_applied=0,
                changes_failed=1
            )
    
    # Apply changes
    changes_applied = 0
    changes_failed = 0
    warnings = []
    
    # Sort changes by line number (descending) to avoid line number shifts
    sorted_changes = sorted(changes, key=lambda c: c.line_number or 0, reverse=True)
    
    for change in sorted_changes:
        try:
            success = False
            
            if change.change_type == 'replace':
                success = apply_replace_change(lines, change)
            elif change.change_type == 'insert':
                success = apply_insert_change(lines, change)
            elif change.change_type == 'delete':
                success = apply_delete_change(lines, change)
            elif change.change_type == 'append':
                success = apply_append_change(lines, change)
            else:
                change.error = f"Unknown change type: {change.change_type}"
                success = False
            
            if success:
                changes_applied += 1
            else:
                changes_failed += 1
                if change.error:
                    warnings.append(f"Change failed: {change.error}")
        
        except Exception as e:
            changes_failed += 1
            warnings.append(f"Error applying change: {str(e)}")
    
    # Write the modified content back to file (if not dry run)
    if not dry_run and changes_applied > 0:
        try:
            modified_content = '\n'.join(lines)
            with open(full_path, 'w', encoding='utf-8') as f:
                f.write(modified_content)
        except Exception as e:
            # Restore backup if write failed
            if backup_path and os.path.exists(backup_path):
                try:
                    with open(backup_path, 'r', encoding='utf-8') as backup:
                        backup_content = backup.read()
                    with open(full_path, 'w', encoding='utf-8') as f:
                        f.write(backup_content)
                except:
                    pass
            
            return ApplyPatchObservation(
                status="error",
                error=f"Failed to write modified file: {str(e)}",
                filepath=filepath,
                changes_applied=0,
                changes_failed=len(changes),
                backup_created=backup_created
            )
    
    # Generate preview of changes
    preview = None
    if changes_applied > 0:
        modified_content = '\n'.join(lines)
        diff = difflib.unified_diff(
            original_content.splitlines(keepends=True),
            modified_content.splitlines(keepends=True),
            fromfile=f"a/{filepath}",
            tofile=f"b/{filepath}",
            lineterm=""
        )
        preview = ''.join(diff)
    
    status = "success" if changes_failed == 0 else "partial" if changes_applied > 0 else "error"
    
    return ApplyPatchObservation(
        status=status,
        filepath=filepath,
        changes_applied=changes_applied,
        changes_failed=changes_failed,
        backup_created=backup_created,
        preview=preview,
        warnings=warnings
    )


# Convenience functions for common patch operations

def replace_lines(
    codebase: Codebase,
    filepath: str,
    old_content: str,
    new_content: str,
    line_number: Optional[int] = None,
    create_backup: bool = True
) -> ApplyPatchObservation:
    """Replace specific lines in a file."""
    change = PatchChange(
        change_type='replace',
        line_number=line_number,
        old_content=old_content,
        new_content=new_content
    )
    return apply_patch(codebase, filepath, [change], create_backup)


def insert_lines(
    codebase: Codebase,
    filepath: str,
    new_content: str,
    line_number: Optional[int] = None,
    create_backup: bool = True
) -> ApplyPatchObservation:
    """Insert new lines into a file."""
    change = PatchChange(
        change_type='insert',
        line_number=line_number,
        new_content=new_content
    )
    return apply_patch(codebase, filepath, [change], create_backup)


def delete_lines(
    codebase: Codebase,
    filepath: str,
    old_content: str,
    line_number: Optional[int] = None,
    create_backup: bool = True
) -> ApplyPatchObservation:
    """Delete specific lines from a file."""
    change = PatchChange(
        change_type='delete',
        line_number=line_number,
        old_content=old_content
    )
    return apply_patch(codebase, filepath, [change], create_backup)


def append_lines(
    codebase: Codebase,
    filepath: str,
    new_content: str,
    create_backup: bool = True
) -> ApplyPatchObservation:
    """Append lines to the end of a file."""
    change = PatchChange(
        change_type='append',
        new_content=new_content
    )
    return apply_patch(codebase, filepath, [change], create_backup) 