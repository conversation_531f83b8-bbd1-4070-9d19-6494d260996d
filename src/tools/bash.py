"""Tools for running bash commands."""

import re
import shlex
import subprocess
from typing import ClassVar, Optional

from pydantic import Field

from .observation import Observation

# Whitelist of allowed commands and their flags
ALLOWED_COMMANDS = {
    "ls": {"-l", "-a", "-h", "-t", "-r", "--color"},
    "cat": {"-n", "--number"},
    "head": {"-n"},
    "tail": {"-n", "-f"},
    "grep": {"-i", "-r", "-n", "-l", "-v", "--color"},
    "find": {"-name", "-type", "-size", "-mtime"},
    "pwd": set(),
    "echo": set(),  # echo is safe with any args
    "ps": {"-ef", "-aux"},
    "df": {"-h"},
    "du": {"-h", "-s"},
    "wc": {"-l", "-w", "-c"},
    # Git commands
    "git": {
        "add", "commit", "push", "pull", "status", "log", "diff", "branch", 
        "checkout", "merge", "clone", "fetch", "remote", "config", "init",
        "reset", "revert", "stash", "tag", "show", "blame", "bisect",
        "-m", "-a", "-b", "-f", "-u", "-v", "--oneline", "--graph", 
        "--decorate", "--all", "--stat", "--name-only", "--cached",
        "--staged", "--hard", "--soft", "--mixed", "--origin", "--upstream",
        "--set-upstream-origin", "--force", "--quiet", "--verbose"
    },
    # GitHub CLI commands
    "gh": {
        "pr", "issue", "repo", "auth", "workflow", "release", "gist",
        "create", "list", "view", "edit", "close", "merge", "review",
        "comment", "status", "checks", "diff", "checkout", "ready",
        "login", "logout", "status", "clone", "fork", "browse",
        "--title", "--body", "--draft", "--assignee", "--reviewer",
        "--label", "--milestone", "--project", "--base", "--head",
        "--web", "--json", "--jq", "--template", "--state", "--author",
        "--search", "--limit", "--repo", "--org", "--user"
    },
    # Node.js and npm commands (useful for web projects)
    "node": {"-v", "--version", "-e", "--eval", "-p", "--print"},
    "npm": {
        "install", "run", "start", "build", "test", "init", "publish",
        "update", "uninstall", "list", "info", "search", "audit",
        "--save", "--save-dev", "--global", "-g", "--production",
        "--dry-run", "--silent", "--verbose", "--force"
    },
    # Python commands
    "python": {"-c", "-m", "-V", "--version", "-h", "--help"},
    "python3": {"-c", "-m", "-V", "--version", "-h", "--help"},
    "pip": {
        "install", "uninstall", "list", "show", "freeze", "search",
        "check", "download", "wheel", "hash", "completion", "debug",
        "--upgrade", "--force-reinstall", "--no-deps", "--user",
        "--requirement", "-r", "--editable", "-e", "--target",
        "--platform", "--python-version", "--implementation",
        "--abi", "--root", "--prefix", "--src", "--upgrade-strategy",
        "--install-option", "--global-option", "--compile", "--no-compile",
        "--no-warn-script-location", "--no-warn-conflicts", "--no-binary",
        "--only-binary", "--prefer-binary", "--require-hashes", "--progress-bar",
        "--no-build-isolation", "--use-pep517", "--no-use-pep517", "--check-build-dependencies",
        "--break-system-packages", "--dry-run", "--report", "--quiet", "-q", "--verbose", "-v"
    },
    # Make commands
    "make": {
        "all", "clean", "install", "test", "build", "dist", "docs",
        "help", "setup", "dev", "lint", "format", "check", "coverage",
        "-f", "--file", "--makefile", "-C", "--directory", "-j", "--jobs",
        "-k", "--keep-going", "-n", "--dry-run", "--just-print", "--recon",
        "-s", "--silent", "--quiet", "-t", "--touch", "-v", "--version",
        "-w", "--print-directory", "--no-print-directory"
    },
    # Docker commands (useful for containerized development)
    "docker": {
        "build", "run", "ps", "images", "pull", "push", "stop", "start",
        "restart", "rm", "rmi", "exec", "logs", "inspect", "version",
        "info", "system", "network", "volume", "compose",
        "-d", "--detach", "-it", "--interactive", "--tty", "-p", "--publish",
        "-v", "--volume", "--name", "--rm", "--env", "-e", "--workdir", "-w",
        "--user", "-u", "--privileged", "--network", "--restart", "--memory",
        "--cpus", "--platform", "--quiet", "-q", "--verbose", "-v", "--force", "-f"
    },
    # Curl for API calls
    "curl": {
        "-X", "--request", "-H", "--header", "-d", "--data", "-F", "--form",
        "-u", "--user", "-b", "--cookie", "-c", "--cookie-jar", "-A", "--user-agent",
        "-e", "--referer", "-L", "--location", "-f", "--fail", "-s", "--silent",
        "-S", "--show-error", "-v", "--verbose", "-w", "--write-out", "-o", "--output",
        "-O", "--remote-name", "-T", "--upload-file", "-x", "--proxy", "-k", "--insecure",
        "--compressed", "--max-time", "--connect-timeout", "--retry", "--retry-delay"
    },
    # Text processing
    "sort": {"-n", "-r", "-u", "-k", "-t", "-f", "-i", "-b", "-d", "-g", "-h", "-V"},
    "uniq": {"-c", "-d", "-u", "-i", "-f", "-s", "-w"},
    "cut": {"-d", "-f", "-c", "-b", "--delimiter", "--fields", "--characters", "--bytes"},
    "awk": set(),  # awk is complex but generally safe for text processing
    "sed": set(),  # sed is complex but generally safe for text processing
    # File operations (safe ones)
    "mkdir": {"-p", "--parents", "-v", "--verbose", "-m", "--mode"},
    "touch": {"-a", "-m", "-c", "--no-create", "-d", "--date", "-r", "--reference", "-t"},
    "which": set(),
    "whereis": set(),
    "whoami": set(),
    "id": set(),
    "date": {"-u", "--utc", "-d", "--date", "-f", "--file", "-r", "--reference", "+%Y-%m-%d", "+%H:%M:%S"},
    "uname": {"-a", "-s", "-n", "-r", "-v", "-m", "-p", "-i", "-o"},
    # Archive operations
    "tar": {
        "-c", "--create", "-x", "--extract", "-t", "--list", "-f", "--file",
        "-z", "--gzip", "-j", "--bzip2", "-J", "--xz", "-v", "--verbose",
        "-p", "--preserve-permissions", "-h", "--dereference", "--exclude",
        "--exclude-from", "-C", "--directory"
    },
    "zip": {"-r", "--recurse-paths", "-q", "--quiet", "-v", "--verbose", "-9", "-0"},
    "unzip": {"-q", "--quiet", "-v", "--verbose", "-l", "--list", "-t", "--test", "-d", "--extract-dir"},
}


class RunBashCommandObservation(Observation):
    """Response from running a bash command."""

    stdout: Optional[str] = Field(
        default=None,
        description="Standard output from the command",
    )
    stderr: Optional[str] = Field(
        default=None,
        description="Standard error from the command",
    )
    command: str = Field(
        description="The command that was executed",
    )
    pid: Optional[int] = Field(
        default=None,
        description="Process ID for background commands",
    )

    str_template: ClassVar[str] = "Command '{command}' completed"


def validate_command(command: str) -> tuple[bool, str]:
    """Validate if a command is safe to execute.

    Args:
        command: The command to validate

    Returns:
        Tuple of (is_valid, error_message)
    """
    try:
        # Check for dangerous patterns first, before splitting
        dangerous_patterns = [
            (r"[|;&`$]", "shell operators (|, ;, &, `, $)"),
            (r"rm\s", "remove command"),
            (r">\s", "output redirection"),
            (r">>\s", "append redirection"),
            (r"<\s", "input redirection"),
            (r"\.\.", "parent directory traversal"),
            (r"sudo\s", "sudo command"),
            (r"chmod\s", "chmod command"),
            (r"chown\s", "chown command"),
            (r"mv\s", "move command"),
            (r"cp\s", "copy command"),
        ]

        for pattern, description in dangerous_patterns:
            if re.search(pattern, command):
                return False, f"Command contains dangerous pattern: {description}"

        # Split command into tokens while preserving quoted strings
        tokens = shlex.split(command)
        if not tokens:
            return False, "Empty command"

        # Get base command (first token)
        base_cmd = tokens[0]

        # Check if base command is in whitelist
        if base_cmd not in ALLOWED_COMMANDS:
            return False, f"Command '{base_cmd}' is not allowed. Allowed commands: {', '.join(sorted(ALLOWED_COMMANDS.keys()))}"

        # Extract and split combined flags (e.g., -la -> -l -a)
        flags = set()
        for token in tokens[1:]:
            if token.startswith("-"):
                if token.startswith("--"):
                    # Handle long options (e.g., --color)
                    flags.add(token)
                else:
                    # Handle combined short options (e.g., -la)
                    # Skip the first "-" and add each character as a flag
                    for char in token[1:]:
                        flags.add(f"-{char}")

        allowed_flags = ALLOWED_COMMANDS[base_cmd]

        # For commands with no flag restrictions (like echo), skip flag validation
        if allowed_flags:
            invalid_flags = flags - allowed_flags
            if invalid_flags:
                return False, f"Flags {invalid_flags} are not allowed for command '{base_cmd}'. Allowed flags: {allowed_flags}"

        return True, ""

    except Exception as e:
        return False, f"Failed to validate command: {e!s}"


def run_bash_command(command: str, is_background: bool = False) -> RunBashCommandObservation:
    """Run a bash command and return its output.

    Args:
        command: The command to run
        is_background: Whether to run the command in the background

    Returns:
        RunBashCommandObservation containing the command output or error
    """
    # First validate the command
    is_valid, error_message = validate_command(command)
    if not is_valid:
        return RunBashCommandObservation(
            status="error",
            error=f"Invalid command: {error_message}",
            command=command,
        )

    try:
        if is_background:
            # For background processes, we use Popen and return immediately
            process = subprocess.Popen(
                command,
                shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
            )
            return RunBashCommandObservation(
                status="success",
                command=command,
                pid=process.pid,
            )

        # For foreground processes, we wait for completion
        result = subprocess.run(
            command,
            shell=True,
            capture_output=True,
            text=True,
            check=True,  # This will raise CalledProcessError if command fails
        )

        return RunBashCommandObservation(
            status="success",
            command=command,
            stdout=result.stdout,
            stderr=result.stderr,
        )

    except subprocess.CalledProcessError as e:
        return RunBashCommandObservation(
            status="error",
            error=f"Command failed with exit code {e.returncode}",
            command=command,
            stdout=e.stdout,
            stderr=e.stderr,
        )
    except Exception as e:
        return RunBashCommandObservation(
            status="error",
            error=f"Failed to run command: {e!s}",
            command=command,
        )
