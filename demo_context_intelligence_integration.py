#!/usr/bin/env python3
"""
Demo: Context Intelligence Integration with Rippr Agents

This script demonstrates how the Context Intelligence Layer enhances
autonomous agents with semantic understanding of codebases.
"""

import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def demo_context_intelligence_with_agents():
    """Demonstrate context intelligence integration with agents"""
    print("🎯 Context Intelligence + Agent Integration Demo")
    print("=" * 60)
    
    try:
        from codegen import Codebase
        from context_intelligence import ContextIntelligenceLayer, EnhancedAgentTools
        
        # Load codebase
        print("📁 Loading codebase...")
        codebase = Codebase(".")
        print(f"   ✅ Loaded {len(codebase.symbols)} symbols")
        
        # Initialize context intelligence
        print("\n🧠 Initializing Context Intelligence...")
        context_layer = ContextIntelligenceLayer(codebase)
        enhanced_tools = EnhancedAgentTools(codebase)
        print("   ✅ Context Intelligence ready")
        
        # Demo scenarios that agents might encounter
        agent_scenarios = [
            {
                "query": "I need to fix a bug where user authentication is failing",
                "description": "Bug fixing scenario"
            },
            {
                "query": "Add rate limiting to the API endpoints",
                "description": "Feature addition scenario"
            },
            {
                "query": "Where is the database connection code located?",
                "description": "Code discovery scenario"
            },
            {
                "query": "Refactor the error handling to be more consistent",
                "description": "Code improvement scenario"
            }
        ]
        
        print(f"\n🤖 Testing {len(agent_scenarios)} agent scenarios with context intelligence...")
        
        for i, scenario in enumerate(agent_scenarios, 1):
            print(f"\n{'='*50}")
            print(f"Scenario {i}: {scenario['description']}")
            print(f"Agent Query: \"{scenario['query']}\"")
            print(f"{'='*50}")
            
            # Step 1: Extract semantic context
            print("\n📊 Step 1: Semantic Context Analysis")
            context = context_layer.extract_context(scenario['query'])
            
            print(f"   🎯 Intent: {context.task.intent}")
            print(f"   🏷️  Domain: {context.task.domain}")
            print(f"   📈 Confidence: {context.confidence:.2f}")
            print(f"   🔍 Entities: {context.task.entities}")
            print(f"   ⚡ Impact Scope: {context.impact_scope}")
            
            # Step 2: Enhanced search with context
            print("\n🔍 Step 2: Enhanced Search with Context")
            search_results = enhanced_tools.enhanced_search(scenario['query'])
            
            # Show first few lines of search results
            lines = search_results.split('\n')
            for line in lines[:5]:
                if line.strip():
                    print(f"   {line}")
            
            if len(lines) > 5:
                print(f"   ... ({len(lines) - 5} more lines)")
            
            # Step 3: Show relevant symbols
            if context.relevant_symbols:
                print(f"\n🧩 Step 3: Relevant Symbols ({len(context.relevant_symbols)} found)")
                for j, symbol in enumerate(context.relevant_symbols[:3], 1):
                    print(f"   {j}. {symbol.symbol_name} ({symbol.domain})")
                    if symbol.file_path:
                        print(f"      📁 {symbol.file_path}")
                
                if len(context.relevant_symbols) > 3:
                    print(f"   ... and {len(context.relevant_symbols) - 3} more symbols")
            
            # Step 4: Show suggested actions
            if context.suggested_actions:
                print(f"\n💡 Step 4: AI-Generated Suggestions")
                for j, action in enumerate(context.suggested_actions[:3], 1):
                    print(f"   {j}. {action}")
                
                if len(context.suggested_actions) > 3:
                    print(f"   ... and {len(context.suggested_actions) - 3} more suggestions")
            
            # Step 5: Show how this helps agents
            print(f"\n🚀 Step 5: Agent Enhancement Benefits")
            print(f"   ✅ Automatic intent classification: {context.task.intent}")
            print(f"   ✅ Domain-specific context: {context.task.domain}")
            print(f"   ✅ Relevant code discovery: {len(context.relevant_symbols)} symbols")
            print(f"   ✅ Impact assessment: {context.impact_scope} scope")
            print(f"   ✅ Actionable suggestions: {len(context.suggested_actions)} items")
        
        # Summary
        print(f"\n{'='*60}")
        print("📊 Demo Summary")
        print(f"{'='*60}")
        print("✅ Context Intelligence Layer successfully analyzed all scenarios")
        print("✅ Semantic understanding provided for each agent query")
        print("✅ Enhanced search results with domain-specific context")
        print("✅ Automatic symbol discovery and relationship mapping")
        print("✅ AI-generated suggestions for each task type")
        print("\n🎉 Context Intelligence is ready to enhance autonomous agents!")
        
        return True
        
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def demo_performance_characteristics():
    """Demonstrate performance characteristics"""
    print("\n⚡ Performance Characteristics Demo")
    print("=" * 40)
    
    try:
        import time
        from context_intelligence import TaskClassifier, ContextIntelligenceLayer
        from codegen import Codebase
        
        # Test classification speed
        classifier = TaskClassifier()
        
        print("🧪 Task Classification Performance:")
        queries = [
            "fix authentication bug",
            "add new feature",
            "refactor code",
            "find symbol",
            "analyze performance"
        ]
        
        start_time = time.time()
        for query in queries * 20:  # 100 total
            classifier.analyze_task(query)
        end_time = time.time()
        
        avg_time = (end_time - start_time) / 100 * 1000
        print(f"   Average: {avg_time:.2f}ms per classification")
        print(f"   Target: <100ms ({'✅ PASS' if avg_time < 100 else '❌ FAIL'})")
        
        # Test context extraction performance
        codebase = Codebase(".")
        context_layer = ContextIntelligenceLayer(codebase)
        
        print("\n🧪 Context Extraction Performance:")
        start_time = time.time()
        for query in queries * 4:  # 20 total
            context_layer.extract_context(query)
        end_time = time.time()
        
        avg_time = (end_time - start_time) / 20 * 1000
        print(f"   Average: {avg_time:.2f}ms per extraction")
        print(f"   Target: <500ms ({'✅ PASS' if avg_time < 500 else '❌ FAIL'})")
        
        print("\n📊 Performance Summary:")
        print("   ✅ Fast enough for real-time agent assistance")
        print("   ✅ Suitable for interactive development workflows")
        print("   ✅ Minimal overhead on agent operations")
        
        return True
        
    except Exception as e:
        print(f"❌ Performance demo failed: {e}")
        return False

def main():
    """Run the complete demo"""
    print("🚀 Context Intelligence Integration Demo")
    print("=" * 60)
    print("This demo shows how the Context Intelligence Layer enhances")
    print("autonomous coding agents with semantic codebase understanding.")
    print("=" * 60)
    
    demos = [
        ("Context Intelligence + Agent Integration", demo_context_intelligence_with_agents),
        ("Performance Characteristics", demo_performance_characteristics)
    ]
    
    results = []
    
    for demo_name, demo_func in demos:
        print(f"\n{'='*20} {demo_name} {'='*20}")
        try:
            result = demo_func()
            results.append((demo_name, result))
        except Exception as e:
            print(f"❌ {demo_name} failed: {e}")
            results.append((demo_name, False))
    
    # Final summary
    print("\n" + "="*60)
    print("🎯 Final Demo Results")
    print("="*60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for demo_name, result in results:
        status = "✅ SUCCESS" if result else "❌ FAILED"
        print(f"{status} {demo_name}")
    
    print(f"\nOverall: {passed}/{total} demos successful")
    
    if passed == total:
        print("\n🎉 Context Intelligence Layer is fully operational!")
        print("✨ Ready to enhance autonomous coding agents")
        print("\n📖 Key Benefits:")
        print("   • Semantic understanding of agent queries")
        print("   • Automatic code discovery and context")
        print("   • Domain-specific intelligence")
        print("   • Performance optimized for real-time use")
        print("   • Zero agent training required")
    else:
        print(f"\n⚠️  {total - passed} demo(s) failed")
    
    return 0 if passed == total else 1

if __name__ == "__main__":
    exit(main()) 