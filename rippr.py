#!/usr/bin/env python3
"""
Rippr CLI Wrapper Script

This script allows you to run the Rippr CLI directly without installation.
Simply run: python rippr.py [arguments]
"""

import sys
import os

print("\033[93mWarning: This direct script is deprecated. For best experience, install the package:\033[0m")
print("pip install -e .\n")
print("Then use the 'rippr' command directly.\n")

# Add the project root directory to the Python path
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Try to use the installed package first
try:
    from rippr.cli import main
except ImportError:
    # Fall back to the source version
    try:
        from src.cli import main
    except ImportError:
        print("❌ Error: Unable to import Rippr CLI module")
        print("💡 Make sure you're in the project root directory")
        sys.exit(1)

if __name__ == "__main__":
    main()