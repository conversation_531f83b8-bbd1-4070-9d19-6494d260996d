#!/usr/bin/env python3
"""
Script to test the GitHub Copilot API's models endpoint.
This script demonstrates how to access the list of available Copilot models
and saves the results to JSON files for inspection.
"""

import os
import sys
import json
import logging
from datetime import datetime
from typing import Dict, Any

# Add the parent directory to the Python path so we can import from src
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(name)s: %(message)s",
    handlers=[logging.StreamHandler()]
)

# Import the CopilotChat class from our package
from src.copilot.client import CopilotChat

def save_json_file(data: Any, filename: str, description: str) -> None:
    """Save data to a JSON file with pretty formatting."""
    script_dir = os.path.dirname(os.path.abspath(__file__))
    filepath = os.path.join(script_dir, filename)

    try:
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False, default=str)
        print(f"✅ {description} saved to: {filepath}")
    except Exception as e:
        print(f"❌ Error saving {description} to {filepath}: {e}")


def main():
    """
    Main function to test the GitHub Copilot models endpoint.
    """
    # Enable debug mode for detailed request/response logging
    os.environ["COPILOT_DEBUG"] = "true"

    print("🚀 GitHub Copilot Models Fetcher")
    print("=" * 50)
    print("Initializing GitHub Copilot client...")

    # Check if GITHUB_COPILOT_OAUTH_TOKEN env var is set
    github_token = os.environ.get("GITHUB_COPILOT_OAUTH_TOKEN")
    if not github_token:
        if os.path.exists(os.path.expanduser("~/.github_copilot_oauth_token.json")):
            print("✅ Found GitHub token in ~/.github_copilot_oauth_token.json")
        else:
            print("⚠️  Warning: No GitHub OAuth token found in environment or token file.")
            print("   You may need to authenticate through the device flow.")

    try:
        # Initialize the CopilotChat client
        # It will automatically handle OAuth token authentication
        copilot_client = CopilotChat()
        print("✅ CopilotChat client initialized successfully")

    except AttributeError as e:
        print(f"\n❌ Error initializing CopilotChat: {e}")
        print("\nIt looks like there's an issue with the CopilotChat class.")
        print("Please check the implementation and try again.")
        sys.exit(1)

    try:
        print("\n📡 Fetching available Copilot models...")

        # Get both raw and filtered models
        print("   - Fetching raw models data...")
        raw_models = copilot_client.get_models_raw()

        print("   - Fetching filtered models...")
        filtered_models = copilot_client.get_models()

        # Convert filtered models to dict format for JSON serialization
        filtered_models_dict = [model.model_dump() for model in filtered_models]

        # Create timestamp for filenames
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # Save raw models (complete API response)
        raw_filename = f"available-models-raw-{timestamp}.json"
        save_json_file(raw_models, raw_filename, "Raw models data")

        # Save filtered models (processed and filtered)
        filtered_filename = f"available-models-filtered-{timestamp}.json"
        save_json_file(filtered_models_dict, filtered_filename, "Filtered models data")

        # Also update the main available-models.json file with raw data
        save_json_file(raw_models, "available-models.json", "Main available models file")

        print(f"\n📊 Summary:")
        print(f"   - Total models in raw response: {len(raw_models.get('data', []))}")
        print(f"   - Filtered models: {len(filtered_models_dict)}")

        print(f"\n🎯 Available Model Names (for configuration):")
        print("=" * 50)
        for i, model in enumerate(filtered_models, 1):
            print(f"{i:2d}. {model.name}")
            print(f"    ID: {model.id}")
            print(f"    Family: {model.capabilities.family}")
            print(f"    Supports Tools: {model.supports_tools()}")
            print(f"    Supports Vision: {model.supports_vision()}")
            print(f"    Max Tokens: {model.max_token_count()}")
            print()

        print("📁 Files created:")
        print(f"   - {raw_filename} (complete API response)")
        print(f"   - {filtered_filename} (filtered and processed)")
        print(f"   - available-models.json (updated main file)")

        return {
            'raw_models': raw_models,
            'filtered_models': filtered_models_dict,
            'model_names': [model.name for model in filtered_models]
        }

    except Exception as e:
        print(f"\n❌ Error fetching Copilot models: {e}")
        import traceback
        traceback.print_exc()
        raise

if __name__ == "__main__":
    main()
