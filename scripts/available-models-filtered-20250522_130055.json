[{"capabilities": {"family": "gpt-4.1", "limits": {"max_context_window_tokens": 128000, "max_output_tokens": 16384, "max_prompt_tokens": 128000}, "supports": {"streaming": true, "tool_calls": true, "parallel_tool_calls": true, "vision": true}, "object": "model_capabilities"}, "id": "gpt-4.1", "name": "GPT-4.1", "policy": null, "vendor": "Azure OpenAI", "model_picker_enabled": true, "object": "model", "preview": false, "version": "gpt-4.1-2025-04-14"}, {"capabilities": {"family": "claude-3.5-sonnet", "limits": {"max_context_window_tokens": 90000, "max_output_tokens": 8192, "max_prompt_tokens": 90000}, "supports": {"streaming": true, "tool_calls": true, "parallel_tool_calls": true, "vision": true}, "object": "model_capabilities"}, "id": "claude-3.5-sonnet", "name": "Claude 3.5 Sonnet", "policy": {"state": "enabled"}, "vendor": "Anthropic", "model_picker_enabled": true, "object": "model", "preview": false, "version": "claude-3.5-sonnet"}, {"capabilities": {"family": "claude-3.7-sonnet", "limits": {"max_context_window_tokens": 200000, "max_output_tokens": 16384, "max_prompt_tokens": 90000}, "supports": {"streaming": true, "tool_calls": true, "parallel_tool_calls": true, "vision": true}, "object": "model_capabilities"}, "id": "claude-3.7-sonnet", "name": "Claude 3.7 Sonnet", "policy": {"state": "enabled"}, "vendor": "Anthropic", "model_picker_enabled": true, "object": "model", "preview": false, "version": "claude-3.7-sonnet"}, {"capabilities": {"family": "claude-3.7-sonnet-thought", "limits": {"max_context_window_tokens": 200000, "max_output_tokens": 16384, "max_prompt_tokens": 90000}, "supports": {"streaming": true, "tool_calls": false, "parallel_tool_calls": false, "vision": true}, "object": "model_capabilities"}, "id": "claude-3.7-sonnet-thought", "name": "Claude 3.7 Sonnet Thinking", "policy": {"state": "enabled"}, "vendor": "Anthropic", "model_picker_enabled": true, "object": "model", "preview": false, "version": "claude-3.7-sonnet-thought"}, {"capabilities": {"family": "claude-opus-4", "limits": {"max_context_window_tokens": 80000, "max_output_tokens": 16000, "max_prompt_tokens": 80000}, "supports": {"streaming": true, "tool_calls": false, "parallel_tool_calls": false, "vision": false}, "object": "model_capabilities"}, "id": "claude-opus-4", "name": "<PERSON> 4", "policy": {"state": "enabled"}, "vendor": "Anthropic", "model_picker_enabled": true, "object": "model", "preview": true, "version": "claude-opus-4"}, {"capabilities": {"family": "claude-sonnet-4", "limits": {"max_context_window_tokens": 80000, "max_output_tokens": 16000, "max_prompt_tokens": 80000}, "supports": {"streaming": true, "tool_calls": true, "parallel_tool_calls": true, "vision": false}, "object": "model_capabilities"}, "id": "claude-sonnet-4", "name": "<PERSON> 4", "policy": {"state": "enabled"}, "vendor": "Anthropic", "model_picker_enabled": true, "object": "model", "preview": true, "version": "claude-sonnet-4"}, {"capabilities": {"family": "gpt-4o", "limits": {"max_context_window_tokens": 128000, "max_output_tokens": 4096, "max_prompt_tokens": 64000}, "supports": {"streaming": true, "tool_calls": true, "parallel_tool_calls": true, "vision": true}, "object": "model_capabilities"}, "id": "gpt-4o", "name": "GPT-4o", "policy": null, "vendor": "Azure OpenAI", "model_picker_enabled": true, "object": "model", "preview": false, "version": "gpt-4o-2024-11-20"}, {"capabilities": {"family": "gemini-2.0-flash", "limits": {"max_context_window_tokens": 1000000, "max_output_tokens": 8192, "max_prompt_tokens": 128000}, "supports": {"streaming": true, "tool_calls": false, "parallel_tool_calls": false, "vision": true}, "object": "model_capabilities"}, "id": "gemini-2.0-flash-001", "name": "Gemini 2.0 Flash", "policy": {"state": "enabled"}, "vendor": "Google", "model_picker_enabled": true, "object": "model", "preview": false, "version": "gemini-2.0-flash-001"}, {"capabilities": {"family": "gemini-2.5-pro", "limits": {"max_context_window_tokens": 128000, "max_output_tokens": 64000, "max_prompt_tokens": 128000}, "supports": {"streaming": true, "tool_calls": true, "parallel_tool_calls": true, "vision": true}, "object": "model_capabilities"}, "id": "gemini-2.5-pro", "name": "Gemini 2.5 Pro (Preview)", "policy": {"state": "enabled"}, "vendor": "Google", "model_picker_enabled": true, "object": "model", "preview": true, "version": "gemini-2.5-pro-preview-05-06"}, {"capabilities": {"family": "o1-ga", "limits": {"max_context_window_tokens": 200000, "max_output_tokens": 0, "max_prompt_tokens": 20000}, "supports": {"streaming": false, "tool_calls": true, "parallel_tool_calls": false, "vision": false}, "object": "model_capabilities"}, "id": "o1", "name": "o1 (Preview)", "policy": null, "vendor": "Azure OpenAI", "model_picker_enabled": true, "object": "model", "preview": true, "version": "o1-2024-12-17"}, {"capabilities": {"family": "o3-mini", "limits": {"max_context_window_tokens": 200000, "max_output_tokens": 100000, "max_prompt_tokens": 64000}, "supports": {"streaming": true, "tool_calls": true, "parallel_tool_calls": false, "vision": false}, "object": "model_capabilities"}, "id": "o3-mini", "name": "o3-mini", "policy": null, "vendor": "Azure OpenAI", "model_picker_enabled": true, "object": "model", "preview": false, "version": "o3-mini-2025-01-31"}, {"capabilities": {"family": "o4-mini", "limits": {"max_context_window_tokens": 128000, "max_output_tokens": 16384, "max_prompt_tokens": 128000}, "supports": {"streaming": true, "tool_calls": true, "parallel_tool_calls": true, "vision": true}, "object": "model_capabilities"}, "id": "o4-mini", "name": "o4-mini (Preview)", "policy": {"state": "enabled"}, "vendor": "Azure OpenAI", "model_picker_enabled": true, "object": "model", "preview": true, "version": "o4-mini-2025-04-16"}]